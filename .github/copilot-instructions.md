This is wordpress theme made with roots sage template. It has roots acorn enabled which brings laravel php ecosystem into wordpress. Project uses woocommerce. Project uses pnpm, tailwind, vite, laravel, inertia, alpine.js, livewire 3, acf blocks, log1x/acf-composer blocks. PHP routes are in folder ./routes. Frontend related stuff is in ./resources. Inertia pages are in ./resources/js/pages. Php related modles, services, providers are in ./app folder. In react components use zustand for state management and @tanstack/react-query for api relates stuff.

Try to make components as modular as possible and in own files.

This is carbon compensation platform. The compensate process consists of 3 steps:

1. Get footprint
   - get footprint with table based calculation
   - get footprint from consulting service
   - get gootprint by importing footprint
2. Compensate
   - search for compensation product and offset the amoun based on footprint
3. Communicata
   - create communcite material (png,pdf,texts)

Roles:

- administrator
- compensation project owner
- consult
- customer

Requirements:
The new climate platform is designed to help companies achieve carbon neutrality by guiding them through three core steps: 1. Calculate – Estimate carbon footprint. 2. Compensate – Offset remaining emissions via verified projects. 3. Communicate – Share results and environmental efforts with stakeholders.

⸻

🎯 Business Goals
• Centralize carbon neutrality efforts into one platform.
• Lower entry barriers for companies by making reliable tools and partners accessible.
• Position the platform as a trusted, long-term partner in climate action.

⸻

👥 User Groups 1. Client Companies (Buyers) – Calculate emissions, purchase offset services, download certificates, and receive marketing materials. 2. Offset Providers (Sellers) – Offer CO₂ removal projects, respond to inquiries/orders, maintain profiles and project details. 3. Consultants (Sellers) – Provide carbon footprint calculation and advisory services for both companies and offset providers. 4. Platform Operators – Enable transactions, facilitate communications, and potentially charge a fee from either party.

⸻

🧭 Platform Phases and Features

Phase 1: Calculate
• Companies explore educational content.
• Use built-in calculator or purchase carbon footprint calculations.
• Compare consultants or send RFPs.
• Upload documents, receive certified reports and visuals.
• Consultants can create profiles, manage services, respond to requests.

Phase 2: Compensate
• Companies browse, compare, and purchase CO₂ offset projects.
• Filter by price, method, benefits, location, certification, and reviews.
• Support for one-time or subscription-based purchases.
• Option to skip Phase 1 by inputting emissions manually.
• Projects include data-rich cards with name, image, certificate, price, rating, etc.
• Offset providers can respond to RFPs, manage orders, upload materials.
• Consultants can assist offset providers in setting up and productizing their projects.

Phase 3: Communicate
• Auto-generate branded materials (PNG, PDF, text) based on customer activity:
• “Reliable Carbon Footprint” (after Phase 1)
• “Reliable Offset” (after Phase 2)
• “Carbon Neutral” (after both)
• Also supports materials for consultants and offset providers.
• Templates vary by content richness and intended audience.

⸻

🛠️ Technical Features

Multilingual Support
• Content available in Finnish and English initially.
• Additional languages via automatic translation with toggle for original language.

Payment Service
• Multi-currency support (EUR, USD, GBP, SEK, DKK, NOK, JPY).
• Pricing shown in user-selected currency, with original value also viewable.
• External payment provider integration.
• Supports invoicing and bank transfer.

Digital Signatures
• Simple digital signing method (e.g., drawing signature).
• Strong national e-ID is not required.

User Management
• Standard account management, password handling, admin controls.
• Users must accept terms of service; renewal required if terms change.
• Organizations can have multiple users and vice versa.
• Admins can manage users.
• Supports logins via Google, Apple, Meta, and LinkedIn.
