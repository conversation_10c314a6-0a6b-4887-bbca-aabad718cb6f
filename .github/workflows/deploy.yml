name: Deploy Theme

on:
  push:
    branches: [ main ]
  workflow_dispatch:

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.3'
          extensions: dom, curl, libxml, mbstring, zip, pcntl, pdo, sqlite, pdo_sqlite, bcmath, soap, intl, gd, exif, iconv
          coverage: none

      - name: Install Composer dependencies
        run: composer install --optimize-autoloader --no-interaction

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'

      - name: Install pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 9.15.4
          run_install: false

      - name: Get pnpm store directory
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - name: Setup pnpm cache
        uses: actions/cache@v3
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-
     

      - name: Install pnpm dependencies
        run: pnpm install

      - name: Build assets
        run: pnpm build

      - name: Create deployment directory
        run: |
          mkdir deployment
          rsync -av --exclude='.*' \
                    --exclude='*.md' \
                    --exclude='*.config.js' \
                    --exclude='package.json' \
                    --exclude='pnpm-lock.yaml' \
                    --exclude='tsconfig.json' \
                    --exclude='node_modules' \
                    --exclude='psalm.xml' \
                    --exclude='tests' \
                    . deployment/

      - name: Deploy to server
        uses: easingthemes/ssh-deploy@main
        with:
          SSH_PRIVATE_KEY: ${{ secrets.SSH_PRIVATE_KEY }}
          ARGS: "--delete"
          SOURCE: "deployment/"
          REMOTE_HOST: ${{ secrets.REMOTE_HOST }}
          REMOTE_USER: ${{ secrets.REMOTE_USER }}
          REMOTE_PORT: ${{ secrets.REMOTE_PORT }}
          TARGET: ${{ secrets.REMOTE_PATH }}/deployment
          EXCLUDE: "/.git/, /.github/, /node_modules/, /tests/, *.md, *.config.js, package.json, pnpm-lock.yaml, tsconfig.json"
          SCRIPT_AFTER: |
            # Copy .env file to deployment directory
            echo "Copying .env file to deployment directory"
            ls -la ${{ secrets.REMOTE_PATH }}/co2market
            cp ${{ secrets.REMOTE_PATH }}/co2market/.env ${{ secrets.REMOTE_PATH }}/deployment/

            echo "Removing co2market directory"
            # Remove the entire co2market folder
            rm -rf ${{ secrets.REMOTE_PATH }}/co2market

            echo "Renaming deployment folder to co2market"
            # Rename deployment folder to co2market
            mv ${{ secrets.REMOTE_PATH }}/deployment ${{ secrets.REMOTE_PATH }}/co2market
            # Run in the co2market directory
            cd ${{ secrets.REMOTE_PATH }}/co2market
            composer run cache:clear --no-interaction
            composer run cache --no-interaction
            echo "Deployment complete"
          SCRIPT_AFTER_REQUIRED: true


