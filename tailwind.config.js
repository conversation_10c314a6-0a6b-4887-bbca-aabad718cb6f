/** @type {import('tailwindcss').Config} config */
const config = {
  darkMode: 'class',
  content: ['./app/**/*.php', './resources/**/*.{php,vue,js,jsx,tsx}'],
  theme: {
    extend: {
      colors: {
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))'
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))'
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive) / <alpha-value>)',
          foreground: 'hsl(var(--destructive-foreground) / <alpha-value>)'
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))'
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))'
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))'
        },
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))'
        },
        sidebar: {
          DEFAULT: 'hsl(var(--sidebar-background))',
          foreground: 'hsl(var(--sidebar-foreground))',
          primary: 'hsl(var(--sidebar-primary))',
          'primary-foreground': 'hsl(var(--sidebar-primary-foreground))',
          accent: 'hsl(var(--sidebar-accent))',
          'accent-foreground': 'hsl(var(--sidebar-accent-foreground))',
          border: 'hsl(var(--sidebar-border))',
          ring: 'hsl(var(--sidebar-ring))'
        },
        'ox-green': {
          100: '#FAFAF5',
          200: '#E1F0DC',
          300: '#C8DCC8',
          400: '#A5E6AA',
          500: '#6EC376',
          600: '#286444'
        },
        'ox-orange': {
          200: '#FFE7BF',
          300: '#D7BEA0',
          400: '#FFCD41',
          600: '#375055'
        },
        'ox-blue': {
          200: '#D2EBF0',
          300: '#A5BED2',
          400: '#7DD2DC',
          600: '#234B69'
        },
        'ox-black': '#0A2222',
        'ox-warning': {
          DEFAULT: '#F00446',
          secondary: '#FFC3B1'
        }
      },
      gridTemplateColumns: {
        '20': 'repeat(20, minmax(0, 1fr))'
      },
      gridTemplateRows: {
        '20': 'repeat(20, minmax(0, 1fr))'
      }
    }
  },
  plugins: []
};

export default config;
