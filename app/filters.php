<?php

/**
 * Theme filters.
 */

namespace App;

// Include WooCommerce filters
require_once __DIR__ . '/Woocommerce/filters.php';

/**
 * Add "… Continued" to the excerpt.
 *
 * @return string
 */
add_filter('excerpt_more', function () {
    return sprintf(' &hellip; <a href="%s">%s</a>', get_permalink(), __('Continued', 'sage'));
});

/**
 * Add breadcrumbs bar shortcode
 */
add_shortcode('breadcrumbs_bar', function () {
    return view('components.breadcrumbs-bar')->render();
});

