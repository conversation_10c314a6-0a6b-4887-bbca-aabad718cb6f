<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Product;
use Exception;

class ProductController extends Controller
{
    public function getUserProducts(Request $request): JsonResponse
    {
        if (!is_user_logged_in()) {
            return response()->json([
                'success' => false,
                'message' => 'User not authenticated',
            ], 401);
        }

        try {
            $user = User::getCurrentUser();
            $page = max(1, intval($request->input('page', 1)));
            $limit = intval($request->input('limit', 10));

            $products = $user->getOwnedProducts($page, $limit);

            return response()->json([
                'success' => true,
                'data' => $products,
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    public function store(Request $request): JsonResponse
    {
        if (!is_user_logged_in()) {
            return response()->json([
                'success' => false,
                'message' => 'User not authenticated',
            ], 401);
        }

        try {
            $user = User::getCurrentUser();
            $data = $request->all();

            if (!$this->canCreateProductType($user, $data['product_type'])) {
                return response()->json([
                    'success' => false,
                    'message' => 'You do not have permission to create this type of product',
                ], 403);
            }

            $product_id = $this->createProduct($data, $user->id);

            $product = new Product($product_id);

            return response()->json([
                'success' => true,
                'data' => [
                    'product' => $product->to_array(),
                    'message' => 'Product created successfully'
                ],
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    private function canCreateProductType(User $user, string $product_type): bool
    {
        $user_roles = array_map(fn($role) => $role?->toString(), $user->roles);

        if (in_array('administrator', $user_roles)) {
            return true;
        }

        if ($product_type === 'compensation-project') {
            return in_array('compensation-project-owner', $user_roles);
        }

        if ($product_type === 'consult-service') {
            return in_array('consultant', $user_roles);
        }

        return false;
    }

    private function createProduct(array $data, int $author_id): int
    {
        $product_data = [
            'post_title' => sanitize_text_field($data['name']),
            'post_content' => wp_kses_post($data['description'] ?? ''),
            'post_excerpt' => sanitize_text_field($data['short_description'] ?? ''),
            'post_status' => 'publish',
            'post_author' => $author_id,
            'post_type' => 'product',
        ];

        $product_id = wp_insert_post($product_data);

        if (is_wp_error($product_id)) {
            throw new Exception('Failed to create product: ' . $product_id->get_error_message());
        }

        $product = wc_get_product($product_id);
        if (!$product) {
            throw new Exception('Failed to get WooCommerce product');
        }

        $product->set_regular_price($data['price'] ?? 0);
        $product->set_price($data['price'] ?? 0);
        $product->save();

        wp_set_object_terms($product_id, $data['product_type'], 'product_cat');

        $this->saveProductFields($product_id, $data);

        // Handle image upload
        $this->handleImageUpload($product_id);

        return $product_id;
    }

    private function handleImageUpload(int $product_id): void
    {
        if (!isset($_FILES['image']) || $_FILES['image']['error'] !== UPLOAD_ERR_OK) {
            return;
        }

        // Handle file upload
        if (!function_exists('wp_handle_upload')) {
            require_once(ABSPATH . 'wp-admin/includes/file.php');
        }

        $uploadedfile = $_FILES['image'];
        $upload_overrides = ['test_form' => false];

        // Validate file type
        $allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
        if (!in_array($uploadedfile['type'], $allowed_types)) {
            throw new Exception('Invalid file type. Only JPEG, PNG, GIF, and WebP files are allowed.');
        }

        // Validate file size (10MB max)
        if ($uploadedfile['size'] > 10 * 1024 * 1024) {
            throw new Exception('File size too large. Maximum 10MB allowed.');
        }

        $movefile = wp_handle_upload($uploadedfile, $upload_overrides);

        if ($movefile && !isset($movefile['error'])) {
            // Create attachment
            $attachment = [
                'post_mime_type' => $uploadedfile['type'],
                'post_title' => sanitize_file_name($uploadedfile['name']),
                'post_content' => '',
                'post_status' => 'inherit'
            ];

            $attachment_id = wp_insert_attachment($attachment, $movefile['file'], $product_id);

            if (!is_wp_error($attachment_id)) {
                // Generate metadata
                if (!function_exists('wp_generate_attachment_metadata')) {
                    require_once(ABSPATH . 'wp-admin/includes/image.php');
                }
                $attachment_data = wp_generate_attachment_metadata($attachment_id, $movefile['file']);
                wp_update_attachment_metadata($attachment_id, $attachment_data);

                // Set as featured image
                set_post_thumbnail($product_id, $attachment_id);

                // Also add to product gallery
                $product = wc_get_product($product_id);
                if ($product) {
                    $gallery_ids = $product->get_gallery_image_ids();
                    $gallery_ids[] = $attachment_id;
                    $product->set_gallery_image_ids($gallery_ids);
                    $product->save();
                }
            }
        } else {
            throw new Exception('Failed to upload image: ' . ($movefile['error'] ?? 'Unknown error'));
        }
    }

    private function saveProductFields(int $product_id, array $data): void
    {
        $common_fields = [
            'location' => $data['location'] ?? '',
            'country' => $data['country'] ?? '',
            'local_vat' => $data['local_vat'] ?? 24,
            'can_ask_for_offer' => $data['can_ask_for_offer'] ?? false,
        ];

        foreach ($common_fields as $field => $value) {
            update_field($field, $value, $product_id);
        }

        if ($data['product_type'] === 'compensation-project') {
            $this->saveCompensationProjectFields($product_id, $data);
        } elseif ($data['product_type'] === 'consult-service') {
            $this->saveConsultingServiceFields($product_id, $data);

            // Handle subcategories for consulting services
            if (!empty($data['consulting_subcategories'])) {
                $category_ids = array_merge(
                    [get_term_by('slug', 'consult-service', 'product_cat')->term_id],
                    $data['consulting_subcategories']
                );
                wp_set_object_terms($product_id, $category_ids, 'product_cat');
            }
        }
    }

    private function saveCompensationProjectFields(int $product_id, array $data): void
    {
        $fields = [
            'project_provider' => $data['project_provider'] ?? '',
            'project_id' => $data['project_id'] ?? '',
            'available_tonnes' => $data['available_tonnes'] ?? 0,
            'active' => $data['active'] ?? false,
            'impact_description' => $data['impact_description'] ?? '',
            'webpage' => $data['webpage'] ?? '',
            'address' => $data['address'] ?? '',
            'contact' => $data['contact'] ?? '',
            'certified' => $data['certified'] ?? '',
            'certificate' => $data['certificate'] ?? '',
        ];

        foreach ($fields as $field => $value) {
            update_field($field, $value, $product_id);
        }
    }

    private function saveConsultingServiceFields(int $product_id, array $data): void
    {
        $fields = [
            'service_id' => $data['service_id'] ?? null,
            'consultant_id' => $data['consultant_id'] ?? null,
            'company_name' => $data['company_name'] ?? '',
            'time_estimate' => $data['time_estimate'] ?? '',
            'standard' => $data['standard'] ?? [],
        ];

        foreach ($fields as $field => $value) {
            update_field($field, $value, $product_id);
        }
    }

    public function getAllowedProductTypes(): JsonResponse
    {
        if (!is_user_logged_in()) {
            return response()->json([
                'success' => false,
                'message' => 'User not authenticated',
            ], 401);
        }

        try {
            $user = User::getCurrentUser();
            $user_roles = array_map(fn($role) => $role?->toString(), $user->roles);

            $allowed_types = [];

            if (in_array('administrator', $user_roles)) {
                $allowed_types = ['compensation-project', 'consult-service'];
            } else {
                if (in_array('compensation-project-owner', $user_roles)) {
                    $allowed_types[] = 'compensation-project';
                }
                if (in_array('consultant', $user_roles)) {
                    $allowed_types[] = 'consult-service';
                }
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'allowed_types' => $allowed_types,
                    'user_roles' => $user_roles,
                ],
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    public function getFormOptions(): JsonResponse
    {
        try {
            return response()->json([
                'success' => true,
                'data' => [
                    'countries' => Product::get_all_countries(),
                    'certifications' => Product::get_all_certifications(),
                    'standards' => $this->getStandardOptions(),
                    'consulting_subcategories' => $this->getConsultingSubcategories(),
                ],
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    private function getStandardOptions(): array
    {
        return [
            'Greenhouse Gas Protocol (GHG Protocol)',
            'ISO 14064',
            'ISO 14067',
            'Climate Disclosure Standards Board (CDSB) Framework',
            'EU Corporate Sustainability Reporting Directive (CSRD) (EU)',
            'PAS 2050 (UK)',
            'Bilan Carbone (FR)',
            'Environmental Protection Agency (EPA) GHG Reporting Program (US)',
            'National Greenhouse and Energy Reporting (NGER) Scheme (AU)',
            '温室气体排放核算方法与报告指南 (China\'s Greenhouse Gas Accounting Guidelines) (CN)',
            '温室効果ガス排出量算定・報告制度 (Mandatory Greenhouse Gas Accounting and Reporting System) (JP)',
            'カーボンフットプリント制度 (Carbon Footprint Program) (JP)',
        ];
    }

    private function getConsultingSubcategories(): array
    {
        $terms = get_terms([
            'taxonomy' => 'product_cat',
            'parent' => $this->getConsultServiceParentId(),
            'hide_empty' => false,
        ]);

        if (is_wp_error($terms)) {
            return [];
        }

        $subcategories = [];
        foreach ($terms as $term) {
            $subcategories[] = [
                'id' => $term->term_id,
                'slug' => $term->slug,
                'name' => $term->name,
            ];
        }

        return $subcategories;
    }

    private function getConsultServiceParentId(): int
    {
        $term = get_term_by('slug', 'consult-service', 'product_cat');
        return $term ? $term->term_id : 0;
    }

    public function show(int $id): JsonResponse
    {
        if (!is_user_logged_in()) {
            return response()->json([
                'success' => false,
                'message' => 'User not authenticated',
            ], 401);
        }

        try {
            $user = User::getCurrentUser();
            $product = new Product($id);

            // Check if user owns this product or is admin - use get_post to access author
            $post = get_post($product->get_id());
            if (!$post || ($post->post_author != $user->id && !in_array('administrator', array_map(fn($role) => $role?->toString(), $user->roles)))) {
                return response()->json([
                    'success' => false,
                    'message' => 'You do not have permission to view this product',
                ], 403);
            }

            return response()->json([
                'success' => true,
                'data' => $product->to_array(),
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    public function update(Request $request, int $id): JsonResponse
    {
        if (!is_user_logged_in()) {
            return response()->json([
                'success' => false,
                'message' => 'User not authenticated',
            ], 401);
        }

        try {
            $user = User::getCurrentUser();
            $product = new Product($id);
            $data = $request->all();

            // Check if user owns this product or is admin - use get_post to access author
            $post = get_post($product->get_id());
            if (!$post || ($post->post_author != $user->id && !in_array('administrator', array_map(fn($role) => $role?->toString(), $user->roles)))) {
                return response()->json([
                    'success' => false,
                    'message' => 'You do not have permission to edit this product',
                ], 403);
            }

            $this->updateProduct($id, $data);

            // Refresh product data
            $updated_product = new Product($id);

            return response()->json([
                'success' => true,
                'data' => [
                    'product' => $updated_product->to_array(),
                    'message' => 'Product updated successfully'
                ],
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    private function updateProduct(int $product_id, array $data): void
    {
        $product_data = [
            'ID' => $product_id,
            'post_title' => sanitize_text_field($data['name']),
            'post_content' => wp_kses_post($data['description'] ?? ''),
            'post_excerpt' => sanitize_text_field($data['short_description'] ?? ''),
        ];

        $result = wp_update_post($product_data);

        if (is_wp_error($result)) {
            throw new Exception('Failed to update product: ' . $result->get_error_message());
        }

        $product = wc_get_product($product_id);
        if (!$product) {
            throw new Exception('Failed to get WooCommerce product');
        }

        $product->set_regular_price($data['price'] ?? 0);
        $product->set_price($data['price'] ?? 0);
        $product->save();

        if (isset($data['product_type'])) {
            wp_set_object_terms($product_id, $data['product_type'], 'product_cat');
        }

        // Handle image removal
        if (isset($data['remove_image']) && ($data['remove_image'] === 'true' || $data['remove_image'] === true)) {
            $this->removeProductImage($product_id);
        }

        // Handle new image upload
        if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
            // Remove existing image first
            $this->removeProductImage($product_id);
            // Upload new image
            $this->handleImageUpload($product_id);
        }

        $this->saveProductFields($product_id, $data);
    }

    private function removeProductImage(int $product_id): void
    {
        // Get current featured image
        $thumbnail_id = get_post_thumbnail_id($product_id);

        if ($thumbnail_id) {
            // Remove from featured image
            delete_post_thumbnail($product_id);

            // Remove from gallery
            $product = wc_get_product($product_id);
            if ($product) {
                $gallery_ids = $product->get_gallery_image_ids();
                $gallery_ids = array_diff($gallery_ids, [$thumbnail_id]);
                $product->set_gallery_image_ids($gallery_ids);
                $product->save();
            }

            // Delete the attachment (optional - you might want to keep it for other uses)
            // wp_delete_attachment($thumbnail_id, true);
        }
    }

    public function archive(int $id): JsonResponse
    {
        if (!is_user_logged_in()) {
            return response()->json([
                'success' => false,
                'message' => 'User not authenticated',
            ], 401);
        }

        try {
            $user = User::getCurrentUser();
            $product = new Product($id);

            // Check if user owns this product or is admin - use get_post to access author
            $post = get_post($product->get_id());
            if (!$post || ($post->post_author != $user->id && !in_array('administrator', array_map(fn($role) => $role?->toString(), $user->roles)))) {
                return response()->json([
                    'success' => false,
                    'message' => 'You do not have permission to archive this product',
                ], 403);
            }

            // Archive the product by changing status to draft
            $result = wp_update_post([
                'ID' => $id,
                'post_status' => 'draft'
            ]);

            if (is_wp_error($result)) {
                throw new Exception('Failed to archive product: ' . $result->get_error_message());
            }

            return response()->json([
                'success' => true,
                'message' => 'Product archived successfully',
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 500);
        }
    }
}
