<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\OffsetProcess;
use App\Enums\CalculationType;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Exception;

class FootprintCalculateController extends Controller
{
    /**
     * Handle footprint calculation and saving
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        // Check if user is logged in
        if (!is_user_logged_in()) {
            return response()->json([
                'success' => false,
                'message' => 'You must be logged in to save a footprint',
            ], 401);
        }

        // Validate request
        $request->validate([
            'name' => 'required|string|max:255',
            'calculation' => 'required|array',
            'calculation.type' => 'required|string|in:generic',
            'calculation.emissions' => 'required|array',
            'calculation.emissions.*.name' => 'required|string',
            'calculation.emissions.*.total' => 'required|numeric',
        ]);

        $user_id = get_current_user_id();
        $name = $request->input('name');
        $calculation = $request->input('calculation');

        try {
            // Create new offset process
            $process = OffsetProcess::create(0, $name, $user_id);
            
            if (!$process) {
                throw new Exception('Failed to create offset process');
            }

            // Set calculation type to GENERIC
            update_field('calculation_type', 'generic', $process->id);
            
            // Set emissions data
            $emissions = [];
            foreach ($calculation['emissions'] as $emission) {
                $emissions[] = [
                    'name' => $emission['name'],
                    'value' => $emission['total']
                ];
            }
            update_field('emissions', $emissions, $process->id);

            return response()->json([
                'success' => true,
                'message' => 'Footprint saved successfully',
                'process_id' => $process->id,
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error saving footprint: ' . $e->getMessage(),
            ], 500);
        }
    }
}
