<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class BusinessExampleController extends Controller
{
    /**
     * Get business examples based on level and value
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        // Extract query parameters
        $level = $request->get('level', 0);
        $value = $request->get('value', '');

        $args = [
            'post_type' => 'footprint-example',
            'posts_per_page' => -1
        ];

        if ($value) {
            $meta_query = [
                [
                    'key' => "level_$level",
                    'value' => $value,
                    'compare' => '='
                ]
            ];
            $args['meta_query'] = $meta_query;
        }

        $query = new \WP_Query($args);
        $posts = $query->posts;

        $levels = [];
        foreach ($posts as $post) {
            $post_id = $post->ID;
            $level_value = get_post_meta($post_id, "level_" . ($level + 1), true);
            $featured_image = get_the_post_thumbnail_url($post_id, 'full');
            $url = get_permalink($post_id);
            
            if ($level == 5) {
                $level_value = get_post_meta($post_id, "size", true);
            }
            
            if ($level_value && !array_key_exists($level_value, $levels)) {
                $levels[$level_value] = [
                    'title' => $level_value,
                    'image' => $featured_image,
                    'url' => $url
                ];
            }
        }

        return response()->json(array_values($levels));
    }
}
