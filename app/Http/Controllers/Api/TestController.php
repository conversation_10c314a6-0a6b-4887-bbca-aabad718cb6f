<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;

class TestController extends Controller
{
    /**
     * Return a test response
     */
    public function index(): JsonResponse
    {
        $current_user = wp_get_current_user();

        return response()->json([
            'message' => 'Hello World',
            'current_user_id' => $current_user->ID,
            'current_user_email' => $current_user->user_email,
        ]);
    }
}
