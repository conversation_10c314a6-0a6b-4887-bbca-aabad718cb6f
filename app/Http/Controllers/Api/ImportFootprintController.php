<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\OffsetProcess;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Exception;

class ImportFootprintController extends Controller
{
    /**
     * Handle footprint file uploads
     */
    public function upload(Request $request): JsonResponse
    {
        // Check if user is logged in
        if (!is_user_logged_in()) {
            return response()->json([
                'success' => false,
                'message' => 'You must be logged in to upload files',
            ], 401);
        }

        // Validate request
        $request->validate([
            'files' => 'required|array|max:5',
            'files.*' => 'file|max:20480', // 20MB max per file
        ]);

        $user_id = get_current_user_id();

        try {
            // Create new offset process
            $process = OffsetProcess::create(0, 'Imported Footprint ' . date('Y-m-d H:i:s'), $user_id);

            // Get uploaded files
            $files = $request->file('files');
            $uploaded_files = [];

            // Process each file
            foreach ($files as $file) {
                // Get the file from Laravel's UploadedFile
                $tmp_name = $file->getPathname();
                $name = $file->getClientOriginalName();
                $type = $file->getMimeType();

                // Prepare file data for WordPress upload
                $_FILES['import_footprint'] = [
                    'name' => $name,
                    'type' => $type,
                    'tmp_name' => $tmp_name,
                    'error' => 0,
                    'size' => $file->getSize()
                ];

                // Include WordPress media handling functions
                require_once ABSPATH . 'wp-admin/includes/file.php';
                require_once ABSPATH . 'wp-admin/includes/media.php';
                require_once ABSPATH . 'wp-admin/includes/image.php';

                // Upload file to WordPress media library
                $attachment_id = \media_handle_upload('import_footprint', 0);

                if (is_wp_error($attachment_id)) {
                    continue;
                }

                // Get file data
                $file_data = [
                    'file' => [
                        'ID' => $attachment_id,
                        'id' => $attachment_id,
                        'title' => \get_the_title($attachment_id),
                        'filename' => basename(\get_attached_file($attachment_id)),
                        'url' => \wp_get_attachment_url($attachment_id),
                        'link' => \get_attachment_link($attachment_id),
                        'alt' => \get_post_meta($attachment_id, '_wp_attachment_image_alt', true),
                        'author' => \get_post_field('post_author', $attachment_id),
                        'description' => \get_post_field('post_content', $attachment_id),
                        'caption' => \get_post_field('post_excerpt', $attachment_id),
                        'name' => \get_post_field('post_name', $attachment_id),
                        'status' => \get_post_field('post_status', $attachment_id),
                        'uploadedTo' => \get_post_field('post_parent', $attachment_id),
                        'date' => \get_post_field('post_date', $attachment_id),
                        'modified' => \get_post_field('post_modified', $attachment_id),
                        'type' => \get_post_mime_type($attachment_id),
                        'subtype' => current(explode('/', \get_post_mime_type($attachment_id))),
                        'icon' => \wp_mime_type_icon($attachment_id),
                    ]
                ];

                $uploaded_files[] = $file_data;
            }

            // Update offset process with uploaded files
            \update_field('files', $uploaded_files, $process->id);

            return response()->json([
                'success' => true,
                'message' => 'Files uploaded successfully',
                'process_id' => $process->id,
                'files' => $uploaded_files,
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error uploading files: ' . $e->getMessage(),
            ], 500);
        }
    }
}
