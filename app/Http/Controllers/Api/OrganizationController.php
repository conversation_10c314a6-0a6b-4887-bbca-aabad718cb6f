<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Organization;
use Exception;

class OrganizationController extends Controller
{
    public function getUserOrganization(): JsonResponse
    {
        if (!is_user_logged_in()) {
            return response()->json([
                'success' => false,
                'message' => 'User not authenticated',
            ], 401);
        }

        try {
            $user = User::getCurrentUser();
            $organization = $user->organization;

            if (!$organization) {
                return response()->json([
                    'success' => true,
                    'data' => null,
                ]);
            }

            $offsetProcesses = $organization->getOffsetProcesses();
            $organizationData = $organization->toArray();
            $organizationData['offset_processes'] = array_map(fn($process) => [
                'id' => $process->id,
                'name' => $process->name,
                'step' => $process->step->toString(),
                'user_id' => $process->user_id,
            ], $offsetProcesses);

            return response()->json([
                'success' => true,
                'data' => $organizationData,
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    public function createOrganization(Request $request): JsonResponse
    {
        if (!is_user_logged_in()) {
            return response()->json([
                'success' => false,
                'message' => 'User not authenticated',
            ], 401);
        }

        try {
            $user = User::getCurrentUser();

            if ($user->organization) {
                return response()->json([
                    'success' => false,
                    'message' => 'User already belongs to an organization',
                ], 409);
            }

            $data = $request->validate([
                'name' => 'required|string|max:255',
            ]);

            $organization = Organization::create($data['name'], [$user->id]);

            return response()->json([
                'success' => true,
                'data' => $organization->toArray(),
                'message' => 'Organization created successfully',
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    public function updateOrganization(Request $request): JsonResponse
    {
        if (!is_user_logged_in()) {
            return response()->json([
                'success' => false,
                'message' => 'User not authenticated',
            ], 401);
        }

        try {
            $user = User::getCurrentUser();
            $organization = $user->organization;

            if (!$organization) {
                return response()->json([
                    'success' => false,
                    'message' => 'User does not belong to an organization',
                ], 404);
            }

            if ($organization->creator_id != $user->id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Only organization creator can update organization details',
                ], 403);
            }

            $data = $request->validate([
                'name' => 'sometimes|string|max:255',
            ]);

            if (isset($data['name'])) {
                wp_update_post([
                    'ID' => $organization->id,
                    'post_title' => $data['name'],
                ]);
            }

            $updated_organization = new Organization($organization->id);

            return response()->json([
                'success' => true,
                'data' => $updated_organization->toArray(),
                'message' => 'Organization updated successfully',
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    public function addUserToOrganization(Request $request): JsonResponse
    {
        if (!is_user_logged_in()) {
            return response()->json([
                'success' => false,
                'message' => 'User not authenticated',
            ], 401);
        }

        try {
            $user = User::getCurrentUser();
            $organization = $user->organization;

            if (!$organization) {
                return response()->json([
                    'success' => false,
                    'message' => 'User does not belong to an organization',
                ], 404);
            }

            if ($organization->creator_id != $user->id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Only organization creator can add users',
                ], 403);
            }

            $data = $request->validate([
                'email' => 'required|email',
            ]);

            $target_user = get_user_by('email', $data['email']);
            if (!$target_user) {
                return response()->json([
                    'success' => false,
                    'message' => 'User with this email does not exist',
                ], 404);
            }

            $existing_organization = Organization::getUserOrganization($target_user->ID);
            if ($existing_organization) {
                return response()->json([
                    'success' => false,
                    'message' => 'User already belongs to an organization',
                ], 409);
            }

            $current_user_ids = array_map(fn($u) => $u->id, $organization->users);
            if (!in_array($target_user->ID, $current_user_ids)) {
                $current_user_ids[] = $target_user->ID;
                update_field('users', $current_user_ids, $organization->id);
            }

            $updated_organization = new Organization($organization->id);

            return response()->json([
                'success' => true,
                'data' => $updated_organization->toArray(),
                'message' => 'User added to organization successfully',
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    public function removeUserFromOrganization(Request $request): JsonResponse
    {
        if (!is_user_logged_in()) {
            return response()->json([
                'success' => false,
                'message' => 'User not authenticated',
            ], 401);
        }

        try {
            $user = User::getCurrentUser();
            $organization = $user->organization;

            if (!$organization) {
                return response()->json([
                    'success' => false,
                    'message' => 'User does not belong to an organization',
                ], 404);
            }

            if ($organization->creator_id != $user->id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Only organization creator can remove users',
                ], 403);
            }

            $data = $request->validate([
                'user_id' => 'required|integer',
            ]);

            if ($data['user_id'] == $user->id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Organization creator cannot remove themselves',
                ], 400);
            }

            $current_user_ids = array_map(fn($u) => $u->id, $organization->users);
            $updated_user_ids = array_filter($current_user_ids, fn($id) => $id != $data['user_id']);

            update_field('users', $updated_user_ids, $organization->id);

            $updated_organization = new Organization($organization->id);

            return response()->json([
                'success' => true,
                'data' => $updated_organization->toArray(),
                'message' => 'User removed from organization successfully',
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 500);
        }
    }
}
