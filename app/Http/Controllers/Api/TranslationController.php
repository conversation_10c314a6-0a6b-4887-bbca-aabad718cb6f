<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Google\Cloud\Translate\V3\Client\TranslationServiceClient;
use Google\Cloud\Translate\V3\TranslateTextRequest;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\RateLimiter;
use Exception;

class TranslationController extends Controller
{
    /**
     * Translate text to destination language
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function translate(Request $request): JsonResponse
    {
        $request->validate([
            'text' => 'required|string',
            'target' => 'required|string|size:2',
            'source' => 'nullable|string|size:2',
        ]);

        $text = $request->input('text');
        $target = $request->input('target');
        $source = $request->input('source');

        // Try to decode 'text' as JSON to see if it's an object (multiple fields)
        $decodedTexts = json_decode($text, true);
        $isMulti = is_array($decodedTexts);

        // For caching, use a hash of the input
        $cacheKey = "translation:{$source}:{$target}:" . md5($text);

        // Check if translation is cached
        if (Cache::has($cacheKey)) {
            return response()->json([
                'success' => true,
                'translation' => Cache::get($cacheKey),
                'cached' => true,
            ]);
        }

        // Rate limiting
        $userId = get_current_user_id() ?: $request->ip();
        if (RateLimiter::tooManyAttempts("translations:minute:{$userId}", 10))
            return response()->json(['success' => false, 'message' => 'Too many translation requests. Please try again later.'], 429);
        if (RateLimiter::tooManyAttempts("translations:hour:{$userId}", 50))
            return response()->json(['success' => false, 'message' => 'Hourly translation limit reached. Please try again later.'], 429);
        if (RateLimiter::tooManyAttempts("translations:day:{$userId}", 200))
            return response()->json(['success' => false, 'message' => 'Daily translation limit reached. Please try again later.'], 429);

        RateLimiter::hit("translations:minute:{$userId}", 60);
        RateLimiter::hit("translations:hour:{$userId}", 3600);
        RateLimiter::hit("translations:day:{$userId}", 86400);

        try {
            $base64Credentials = env('GOOGLE_CREDENTIALS');
            if ($base64Credentials === false) throw new Exception('GOOGLE_CREDENTIALS environment variable is not set.');
            $jsonCredentials = base64_decode($base64Credentials);
            if ($jsonCredentials === false) throw new Exception('Failed to decode GOOGLE_CREDENTIALS.');
            $credentials = json_decode($jsonCredentials, true);
            if ($credentials === null) throw new Exception('Failed to parse JSON credentials.');

            $translate = new TranslationServiceClient(['credentials' => $credentials]);
            $projectId = $credentials['project_id'];
            $location = 'global';
            $parent = $translate->locationName($projectId, $location);

            // Prepare content(s)
            if ($isMulti) {
                // Multiple fields: extract values for translation
                $contents = array_values($decodedTexts);
            } else {
                // Single string
                $contents = [$text];
            }

            $requestObj = new TranslateTextRequest([
                'contents' => $contents,
                'target_language_code' => $target,
                'parent' => $parent,
                'mime_type' => 'text/plain',
            ]);
            if ($source) $requestObj->setSourceLanguageCode($source);

            $response = $translate->translateText($requestObj);
            $translations = $response->getTranslations();

            // Gather translated results
            if ($isMulti) {
                // Map translation back to keys
                $keys = array_keys($decodedTexts);
                $translatedTexts = [];
                foreach ($translations as $i => $translation) {
                    $translatedTexts[$keys[$i]] = $translation->getTranslatedText();
                }
                $finalTranslation = json_encode($translatedTexts, JSON_UNESCAPED_UNICODE);
            } else {
                $finalTranslation = $translations[0]->getTranslatedText();
            }

            // Cache the result for 24 hours
            Cache::put($cacheKey, $finalTranslation, 86400);

            return response()->json([
                'success' => true,
                'translation' => $finalTranslation,
                'cached' => false,
            ]);
        } catch (\Exception $e) {
            Log::error("Translation error: " . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Translation service error',
            ], 500);
        }
    }
}
