<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use WC_Order_Query;

class OrderController extends Controller
{
    /**
     * Get paginated orders for the current user
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getUserOrders(Request $request): JsonResponse
    {
        // Ensure user is logged in
        if (!is_user_logged_in()) {
            return response()->json([
                'success' => false,
                'message' => 'User not authenticated',
            ], 401);
        }

        $current_user_id = get_current_user_id();
        $page = max(1, intval($request->input('page', 1)));
        $per_page = intval($request->input('per_page', 25));

        // Validate per_page to only allow specific values
        $allowed_per_page = [25, 50, 100];
        if (!in_array($per_page, $allowed_per_page)) {
            $per_page = 25;
        }

        // Calculate offset
        $offset = ($page - 1) * $per_page;

        // Query WooCommerce orders
        $args = [
            'customer_id' => $current_user_id,
            'limit' => $per_page,
            'offset' => $offset,
            'orderby' => 'date',
            'order' => 'DESC',
            'paginate' => true,
        ];

        $query = new WC_Order_Query($args);
        $result = $query->get_orders();
        
        $orders = [];
        $total_orders = 0;
        $max_pages = 1;

        if ($result) {
            $total_orders = $result->total;
            $max_pages = $result->max_num_pages;

            foreach ($result->orders as $order) {
                $order_data = $order->get_data();
                
                // Format the order data
                $orders[] = [
                    'id' => $order->get_id(),
                    'order_number' => $order->get_order_number(),
                    'status' => $order->get_status(),
                    'status_name' => wc_get_order_status_name($order->get_status()),
                    'date_created' => $order->get_date_created()->date('Y-m-d H:i:s'),
                    'formatted_date' => $order->get_date_created()->date_i18n(get_option('date_format') . ' ' . get_option('time_format')),
                    'total' => $order->get_total(),
                    'formatted_total' => $order->get_formatted_order_total(),
                    'payment_method' => $order->get_payment_method_title(),
                    'items_count' => count($order->get_items()),
                ];
            }
        }

        return response()->json([
            'success' => true,
            'data' => [
                'orders' => $orders,
                'pagination' => [
                    'total' => $total_orders,
                    'per_page' => $per_page,
                    'current_page' => $page,
                    'max_pages' => $max_pages,
                ],
            ],
        ]);
    }

    /**
     * Get details of a single order
     *
     * @param Request $request
     * @param int $id Order ID
     * @return JsonResponse
     */
    public function getOrder(Request $request, int $id): JsonResponse
    {
        // Ensure user is logged in
        if (!is_user_logged_in()) {
            return response()->json([
                'success' => false,
                'message' => 'User not authenticated',
            ], 401);
        }

        $current_user_id = get_current_user_id();
        
        // Get the order
        $order = wc_get_order($id);
        
        // Check if order exists and belongs to the current user
        if (!$order || $order->get_customer_id() != $current_user_id) {
            return response()->json([
                'success' => false,
                'message' => 'Order not found or does not belong to the current user',
            ], 404);
        }
        
        // Get billing and shipping addresses
        $billing = $order->get_address('billing');
        $shipping = $order->get_address('shipping');
        
        // Format items
        $items = [];
        foreach ($order->get_items() as $item_id => $item) {
            $product = $item->get_product();
            $product_id = $item->get_product_id();
            $variation_id = $item->get_variation_id();
            
            $product_data = [
                'id' => $product_id,
                'variation_id' => $variation_id ?: null,
                'name' => $item->get_name(),
                'quantity' => $item->get_quantity(),
                'subtotal' => $item->get_subtotal(),
                'total' => $item->get_total(),
                'tax' => $item->get_total_tax(),
                'formatted_subtotal' => wc_price($item->get_subtotal()),
                'formatted_total' => wc_price($item->get_total()),
                'formatted_tax' => wc_price($item->get_total_tax()),
            ];
            
            // Add product image if available
            if ($product) {
                $product_data['image'] = wp_get_attachment_image_url($product->get_image_id(), 'thumbnail') ?: wc_placeholder_img_src('thumbnail');
                $product_data['permalink'] = $product->get_permalink();
            }
            
            $items[] = $product_data;
        }
        
        // Order notes
        $notes = [];
        foreach($order->get_customer_order_notes() as $note) {
            $notes[] = [
                'id' => $note->comment_ID,
                'content' => $note->comment_content,
                'date' => $note->comment_date,
                'formatted_date' => human_time_diff(strtotime($note->comment_date_gmt), current_time('timestamp')) . ' ago',
            ];
        }
        
        // Order totals
        $totals = [];
        foreach ($order->get_order_item_totals() as $key => $total) {
            $totals[] = [
                'key' => $key,
                'label' => $total['label'],
                'value' => $total['value'],
            ];
        }
        
        // Format the order data
        $order_data = [
            'id' => $order->get_id(),
            'order_number' => $order->get_order_number(),
            'status' => $order->get_status(),
            'status_name' => wc_get_order_status_name($order->get_status()),
            'date_created' => $order->get_date_created()->date('Y-m-d H:i:s'),
            'formatted_date' => $order->get_date_created()->date_i18n(get_option('date_format') . ' ' . get_option('time_format')),
            'date_modified' => $order->get_date_modified() ? $order->get_date_modified()->date('Y-m-d H:i:s') : null,
            'date_completed' => $order->get_date_completed() ? $order->get_date_completed()->date('Y-m-d H:i:s') : null,
            'date_paid' => $order->get_date_paid() ? $order->get_date_paid()->date('Y-m-d H:i:s') : null,
            'customer_note' => $order->get_customer_note(),
            
            // Payment details
            'payment_method' => $order->get_payment_method(),
            'payment_method_title' => $order->get_payment_method_title(),
            'transaction_id' => $order->get_transaction_id(),
            
            // Order totals
            'subtotal' => $order->get_subtotal(),
            'tax_total' => $order->get_total_tax(),
            'shipping_total' => $order->get_shipping_total(),
            'discount_total' => $order->get_discount_total(),
            'total' => $order->get_total(),
            'formatted_subtotal' => wc_price($order->get_subtotal()),
            'formatted_tax_total' => wc_price($order->get_total_tax()),
            'formatted_shipping_total' => wc_price($order->get_shipping_total()),
            'formatted_discount_total' => wc_price($order->get_discount_total()),
            'formatted_total' => $order->get_formatted_order_total(),
            
            // Addresses
            'billing' => $billing,
            'shipping' => $shipping,
            
            // Items and notes
            'items' => $items,
            'notes' => $notes,
            'totals' => $totals,
            
            // Download permissions
            'downloads' => $order->get_downloadable_items(),
            
            // Carbon footprint data (if applicable)
            'carbon_footprint' => [
                'amount' => get_post_meta($order->get_id(), '_carbon_footprint', true) ?: 0,
                'units' => 'kg CO2',
                'offset' => get_post_meta($order->get_id(), '_carbon_offset', true) === 'yes',
                'offset_amount' => get_post_meta($order->get_id(), '_carbon_offset_amount', true) ?: 0,
            ],
        ];
        
        return response()->json([
            'success' => true,
            'data' => [
                'order' => $order_data,
            ],
        ]);
    }
}