<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class WordPress
{
    /**
     * Makes sure the 'template_redirect' action is executed when using custom routes.
     * The 'template_redirect' action is required for displaying the WP_Admin_Bar.
     */
    public function handle(Request $request, Closure $next)
    {
        do_action('template_redirect');

        return $next($request);
    }
}
