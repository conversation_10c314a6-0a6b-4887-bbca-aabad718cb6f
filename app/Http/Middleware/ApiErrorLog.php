<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Throwable;

class ApiErrorLog
{
    public function handle(Request $request, Closure $next)
    {
        $startTime = microtime(true);

        try {
            $response = $next($request);

            $endTime = microtime(true);
            $duration = round(($endTime - $startTime) * 1000, 2); // ms

            // Log API errors (4xx and 5xx responses)
            if ($response->getStatusCode() >= 400) {
                $this->logApiError($request, $response, $duration);
            }

            // Log slow requests (over 2 seconds)
            if ($duration > 2000) {
                $this->logSlowRequest($request, $response, $duration);
            }

            return $response;
        } catch (Throwable $e) {
            $endTime = microtime(true);
            $duration = round(($endTime - $startTime) * 1000, 2);

            // Log the exception
            $this->logException($request, $e, $duration);

            // Re-throw the exception to let <PERSON><PERSON> handle it normally
            throw $e;
        }
    }

    private function logApiError(Request $request, $response, float $duration)
    {
        $context = [
            'type' => 'api_error',
            'url' => $request->fullUrl(),
            'method' => $request->method(),
            'status_code' => $response->getStatusCode(),
            'duration_ms' => $duration,
            'user_context' => $this->getUserContext(),
            'request_context' => $this->getRequestContext($request),
            'wordpress_context' => $this->getWordPressContext(),
            'request_data' => $this->sanitizeRequestData($request),
            'response_data' => $this->getResponseData($response),
        ];

        $level = $response->getStatusCode() >= 500 ? 'critical' : 'error';
        Log::channel('logtail')->{$level}('API Error Response', $context);
    }

    private function logException(Request $request, Throwable $e, float $duration)
    {
        $context = [
            'type' => 'api_exception',
            'url' => $request->fullUrl(),
            'method' => $request->method(),
            'duration_ms' => $duration,
            'user_context' => $this->getUserContext(),
            'request_context' => $this->getRequestContext($request),
            'wordpress_context' => $this->getWordPressContext(),
            'request_data' => $this->sanitizeRequestData($request),
            'exception' => [
                'class' => get_class($e),
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString(),
                'previous' => $e->getPrevious() ? [
                    'class' => get_class($e->getPrevious()),
                    'message' => $e->getPrevious()->getMessage(),
                ] : null,
            ],
        ];

        Log::channel('logtail')->critical('API Exception', $context);
    }

    private function logSlowRequest(Request $request, $response, float $duration)
    {
        $context = [
            'type' => 'slow_request',
            'url' => $request->fullUrl(),
            'method' => $request->method(),
            'status_code' => $response->getStatusCode(),
            'duration_ms' => $duration,
            'user_context' => $this->getUserContext(),
            'request_context' => $this->getRequestContext($request),
            'wordpress_context' => $this->getWordPressContext(),
        ];

        Log::channel('logtail')->warning('Slow API Request', $context);
    }

    private function getUserContext(): array
    {
        $context = [
            'user_id' => null,
            'user_login' => null,
            'user_roles' => [],
            'is_logged_in' => false,
        ];

        if (function_exists('is_user_logged_in') && is_user_logged_in()) {
            $context['is_logged_in'] = true;
            $context['user_id'] = get_current_user_id();

            $user = wp_get_current_user();
            if ($user) {
                $context['user_login'] = $user->user_login;
                $context['user_roles'] = $user->roles;
            }
        }

        return $context;
    }

    private function getRequestContext(Request $request): array
    {
        return [
            'request_id' => $request->header('X-Request-ID', uniqid()),
            'user_agent' => $request->userAgent(),
            'ip' => $request->ip(),
            'referer' => $request->header('Referer'),
            'content_type' => $request->header('Content-Type'),
            'accept' => $request->header('Accept'),
            'route_name' => $request->route() ? $request->route()->getName() : null,
        ];
    }

    private function getWordPressContext(): array
    {
        $context = [
            'wp_version' => null,
            'theme' => null,
            'is_multisite' => false,
            'memory_usage' => null,
            'memory_limit' => null,
        ];

        if (function_exists('get_bloginfo')) {
            $context['wp_version'] = get_bloginfo('version');
            $context['theme'] = get_template();
            $context['is_multisite'] = is_multisite();
        }

        $context['memory_usage'] = memory_get_usage(true);
        $context['memory_limit'] = ini_get('memory_limit');

        return $context;
    }

    private function sanitizeRequestData(Request $request): array
    {
        $data = $request->all();

        // Remove sensitive fields
        $sensitiveFields = [
            'password',
            'password_confirmation',
            'token',
            'api_key',
            'secret',
            'auth_token',
            'access_token',
            'refresh_token',
            'api_secret',
            'client_secret',
        ];

        foreach ($sensitiveFields as $field) {
            if (isset($data[$field])) {
                $data[$field] = '[REDACTED]';
            }
        }

        // Limit size of request data to prevent log bloat
        $jsonData = json_encode($data);
        if (strlen($jsonData) > 5000) {
            return [
                'size_limit_exceeded' => true,
                'original_size' => strlen($jsonData),
                'preview' => substr($jsonData, 0, 1000) . '...[TRUNCATED]',
            ];
        }

        return $data;
    }

    private function getResponseData($response): array
    {
        $content = $response->getContent();
        $contentType = $response->headers->get('Content-Type', '');

        // Only try to decode JSON responses
        if (strpos($contentType, 'application/json') !== false && is_string($content)) {
            $decoded = json_decode($content, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                // Limit response data size
                $jsonData = json_encode($decoded);
                if (strlen($jsonData) > 3000) {
                    return [
                        'size_limit_exceeded' => true,
                        'original_size' => strlen($jsonData),
                        'preview' => substr($jsonData, 0, 500) . '...[TRUNCATED]',
                        'success' => $decoded['success'] ?? null,
                        'message' => $decoded['message'] ?? null,
                    ];
                }
                return $decoded;
            }
        }

        return [
            'content_type' => $contentType,
            'content_length' => strlen($content),
            'content_preview' => substr($content, 0, 200) . (strlen($content) > 200 ? '...' : ''),
        ];
    }
}
