<?php

add_action('init', function () {
    register_nav_menu('footer-menu', __('Footer Menu', CO2MARKET_TEXT_DOMAIN));
});

add_action('acf/init', function () {
    if (!function_exists('acf_add_local_field_group')) return;

    acf_add_local_field_group(array(
        'key' => 'group_footer_sections',
        'title' => 'Footer Sections',
        'fields' => array(
            array(
                'key' => 'field_navigation_sections',
                'label' => 'Navigation Sections',
                'name' => 'navigation_sections',
                'type' => 'repeater',
                'sub_fields' => array(
                    array(
                        'key' => 'field_nav_section_title',
                        'label' => 'Section Title',
                        'name' => 'title',
                        'type' => 'text',
                    ),
                    array(
                        'key' => 'field_nav_section_links',
                        'label' => 'Links',
                        'name' => 'links',
                        'type' => 'repeater',
                        'sub_fields' => array(
                            array(
                                'key' => 'field_nav_link',
                                'label' => 'Link',
                                'name' => 'link',
                                'type' => 'acfe_advanced_link',
                            ),
                        ),
                    ),
                ),
            ),
            array(
                'key' => 'field_information_section',
                'label' => 'Information Section',
                'name' => 'information_section',
                'type' => 'group',
                'sub_fields' => array(
                    array(
                        'key' => 'field_information_title',
                        'label' => 'Title',
                        'name' => 'title',
                        'type' => 'text',
                    ),
                    array(
                        'key' => 'field_information_links',
                        'label' => 'Links',
                        'name' => 'links',
                        'type' => 'repeater',
                        'sub_fields' => array(
                            array(
                                'key' => 'field_info_link',
                                'label' => 'Link',
                                'name' => 'link',
                                'type' => 'acfe_advanced_link',
                            ),
                        ),
                    ),
                ),
            ),
            array(
                'key' => 'field_social_section',
                'label' => 'Social Section',
                'name' => 'social_section',
                'type' => 'group',
                'sub_fields' => array(
                    array(
                        'key' => 'field_social_main_image',
                        'label' => 'Main Image',
                        'name' => 'main_image',
                        'type' => 'image',
                        'return_format' => 'array',
                    ),
                    array(
                        'key' => 'field_socials',
                        'label' => 'Socials',
                        'name' => 'socials',
                        'type' => 'repeater',
                        'sub_fields' => array(
                            array(
                                'key' => 'field_social_name',
                                'label' => 'Name',
                                'name' => 'name',
                                'type' => 'text',
                            ),
                            array(
                                'key' => 'field_social_icon',
                                'label' => 'Icon',
                                'name' => 'icon',
                                'type' => 'image',
                                'return_format' => 'array',
                            ),
                            array(
                                'key' => 'field_social_url',
                                'label' => 'URL',
                                'name' => 'url',
                                'type' => 'url',
                            ),
                        ),
                    ),
                ),
            ),
        ),
        'location' => array(
            array(
                array(
                    'param' => 'nav_menu',
                    'operator' => '==',
                    'value' => 'location/footer-menu',
                ),
            ),
        ),
        'menu_order' => 0,
        'style' => 'default',
        'label_placement' => 'top',
        'hide_on_screen' => '',
        'active' => true,
        'description' => '',
    ));
});
