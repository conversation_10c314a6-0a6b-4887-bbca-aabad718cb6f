<?php

add_action('init', function () {
    register_post_type('calculation', array(
      'labels' => array(
        'name' => 'Calculations',
        'singular_name' => 'Calculation',
        'menu_name' => 'Calculations',
        'all_items' => 'All Calculations',
        'edit_item' => 'Edit Calculation',
        'view_item' => 'View Calculation',
        'view_items' => 'View Calculations',
        'add_new_item' => 'Add New Calculation',
        'add_new' => 'Add New Calculation',
        'new_item' => 'New Calculation',
        'parent_item_colon' => 'Parent Calculation:',
        'search_items' => 'Search Calculations',
        'not_found' => 'No calculations found',
        'not_found_in_trash' => 'No calculations found in Trash',
        'archives' => 'Calculation Archives',
        'attributes' => 'Calculation Attributes',
        'insert_into_item' => 'Insert into calculation',
        'uploaded_to_this_item' => 'Uploaded to this calculation',
        'filter_items_list' => 'Filter calculations list',
        'filter_by_date' => 'Filter calculations by date',
        'items_list_navigation' => 'Calculations list navigation',
        'items_list' => 'Calculations list',
        'item_published' => 'Calculation published.',
        'item_published_privately' => 'Calculation published privately.',
        'item_reverted_to_draft' => 'Calculation reverted to draft.',
        'item_scheduled' => 'Calculation scheduled.',
        'item_updated' => 'Calculation updated.',
        'item_link' => 'Calculation Link',
        'item_link_description' => 'A link to a calculation.',
      ),
      'public' => true,
      'show_in_rest' => true,
      'menu_icon' => 'dashicons-admin-post',
      'supports' => array(
        0 => 'title',
        1 => 'author',
      ),
      'delete_with_user' => false,
    ));
});
