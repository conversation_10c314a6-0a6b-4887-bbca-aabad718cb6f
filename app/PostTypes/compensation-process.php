<?php

add_action('init', function () {
    register_post_type('compensation-process', array(
      'labels' => array(
        'name' => 'Compensation processes',
        'singular_name' => 'Compensation process',
        'menu_name' => 'Compensation processes',
        'all_items' => 'All Compensation processes',
        'edit_item' => 'Edit Compensation process',
        'view_item' => 'View Compensation process',
        'view_items' => 'View Compensation processes',
        'add_new_item' => 'Add New Compensation process',
        'add_new' => 'Add New Compensation process',
        'new_item' => 'New Compensation process',
        'parent_item_colon' => 'Parent Compensation process:',
        'search_items' => 'Search Compensation processes',
        'not_found' => 'No compensation processes found',
        'not_found_in_trash' => 'No compensation processes found in Trash',
        'archives' => 'Compensation process Archives',
        'attributes' => 'Compensation process Attributes',
        'insert_into_item' => 'Insert into compensation process',
        'uploaded_to_this_item' => 'Uploaded to this compensation process',
        'filter_items_list' => 'Filter compensation processes list',
        'filter_by_date' => 'Filter compensation processes by date',
        'items_list_navigation' => 'Compensation processes list navigation',
        'items_list' => 'Compensation processes list',
        'item_published' => 'Compensation process published.',
        'item_published_privately' => 'Compensation process published privately.',
        'item_reverted_to_draft' => 'Compensation process reverted to draft.',
        'item_scheduled' => 'Compensation process scheduled.',
        'item_updated' => 'Compensation process updated.',
        'item_link' => 'Compensation process Link',
        'item_link_description' => 'A link to a compensation process.',
      ),
      'public' => false,
      'publicly_queryable' => true,
      'show_ui' => true,
      'show_in_rest' => true,
      'menu_icon' => 'dashicons-awards',
      'supports' => array(
        0 => 'title',
      ),
      'delete_with_user' => false,
    ));
});
