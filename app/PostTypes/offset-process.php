<?php

add_action('init', function () {
    register_post_type('offset-process', array(
        'labels' => array(
            'name' => 'Offset processes',
            'singular_name' => 'Offset process',
            'menu_name' => 'Offset processes',
            'all_items' => 'All Offset processes',
            'edit_item' => 'Edit Offset process',
            'view_item' => 'View Offset process',
            'view_items' => 'View Offset processes',
            'add_new_item' => 'Add New Offset process',
            'add_new' => 'Add New Offset process',
            'new_item' => 'New Offset process',
            'parent_item_colon' => 'Parent Offset process:',
            'search_items' => 'Search Offset processes',
            'not_found' => 'No offset processes found',
            'not_found_in_trash' => 'No offset processes found in Trash',
            'archives' => 'Offset process Archives',
            'attributes' => 'Offset process Attributes',
            'insert_into_item' => 'Insert into offset process',
            'uploaded_to_this_item' => 'Uploaded to this offset process',
            'filter_items_list' => 'Filter offset processes list',
            'filter_by_date' => 'Filter offset processes by date',
            'items_list_navigation' => 'Offset processes list navigation',
            'items_list' => 'Offset processes list',
            'item_published' => 'Offset process published.',
            'item_published_privately' => 'Offset process published privately.',
            'item_reverted_to_draft' => 'Offset process reverted to draft.',
            'item_scheduled' => 'Offset process scheduled.',
            'item_updated' => 'Offset process updated.',
            'item_link' => 'Offset process Link',
            'item_link_description' => 'A link to an offset process.',
        ),
        'public' => false,
        'publicly_queryable' => true,
        'show_ui' => true,
        'show_in_rest' => true,
        'menu_icon' => 'dashicons-chart-area',
        'supports' => array(
            'title',
            'author'
        ),
        'delete_with_user' => false,
    ));
});
