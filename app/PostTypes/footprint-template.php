<?php

add_action('init', function () {
    register_post_type('footprint-template', array(
    'labels' => array(
        'name' => 'Footprint templates',
        'singular_name' => 'Footprint template',
        'menu_name' => 'Footprint templates',
        'all_items' => 'All Footprint templates',
        'edit_item' => 'Edit Footprint template',
        'view_item' => 'View Footprint template',
        'view_items' => 'View Footprint templates',
        'add_new_item' => 'Add New Footprint template',
        'add_new' => 'Add New Footprint template',
        'new_item' => 'New Footprint template',
        'parent_item_colon' => 'Parent Footprint template:',
        'search_items' => 'Search Footprint templates',
        'not_found' => 'No footprint templates found',
        'not_found_in_trash' => 'No footprint templates found in Trash',
        'archives' => 'Footprint template Archives',
        'attributes' => 'Footprint template Attributes',
        'insert_into_item' => 'Insert into footprint template',
        'uploaded_to_this_item' => 'Uploaded to this footprint template',
        'filter_items_list' => 'Filter footprint templates list',
        'filter_by_date' => 'Filter footprint templates by date',
        'items_list_navigation' => 'Footprint templates list navigation',
        'items_list' => 'Footprint templates list',
        'item_published' => 'Footprint template published.',
        'item_published_privately' => 'Footprint template published privately.',
        'item_reverted_to_draft' => 'Footprint template reverted to draft.',
        'item_scheduled' => 'Footprint template scheduled.',
        'item_updated' => 'Footprint template updated.',
        'item_link' => 'Footprint template Link',
        'item_link_description' => 'A link to a footprint template.',
    ),
    'public' => true,
    'show_in_rest' => true,
    'menu_icon' => 'dashicons-admin-post',
    'supports' => array(
        0 => 'title',
    ),
    'delete_with_user' => false,
));
});
