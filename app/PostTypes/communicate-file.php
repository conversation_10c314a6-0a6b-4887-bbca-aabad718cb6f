<?php

add_action('init', function () {
    register_post_type('communicate-file', array(
    'labels' => array(
        'name' => 'Communicate files',
        'singular_name' => 'Communicate file',
        'menu_name' => 'Communicate files',
        'all_items' => 'All Communicate files',
        'edit_item' => 'Edit Communicate file',
        'view_item' => 'View Communicate file',
        'view_items' => 'View Communicate files',
        'add_new_item' => 'Add New Communicate file',
        'add_new' => 'Add New Communicate file',
        'new_item' => 'New Communicate file',
        'parent_item_colon' => 'Parent Communicate file:',
        'search_items' => 'Search Communicate files',
        'not_found' => 'No communicate files found',
        'not_found_in_trash' => 'No communicate files found in Trash',
        'archives' => 'Communicate file Archives',
        'attributes' => 'Communicate file Attributes',
        'insert_into_item' => 'Insert into communicate file',
        'uploaded_to_this_item' => 'Uploaded to this communicate file',
        'filter_items_list' => 'Filter communicate files list',
        'filter_by_date' => 'Filter communicate files by date',
        'items_list_navigation' => 'Communicate files list navigation',
        'items_list' => 'Communicate files list',
        'item_published' => 'Communicate file published.',
        'item_published_privately' => 'Communicate file published privately.',
        'item_reverted_to_draft' => 'Communicate file reverted to draft.',
        'item_scheduled' => 'Communicate file scheduled.',
        'item_updated' => 'Communicate file updated.',
        'item_link' => 'Communicate file Link',
        'item_link_description' => 'A link to a communicate file.',
    ),
    'public' => false,
    'publicly_queryable' => true,
    'show_ui' => true,
    'show_in_rest' => true,
    'menu_icon' => 'dashicons-format-aside',
    'supports' => array(
        0 => 'title',
    ),
    'delete_with_user' => false,
));
});
