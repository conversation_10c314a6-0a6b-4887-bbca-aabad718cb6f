<?php

namespace App\Blocks;

use Log1x\AcfComposer\Block;
use Log1x\AcfComposer\Builder;
use App\Models\Product;
use WC_Product;

class ProductSlider extends Block
{
    /**
     * Product types available for selection
     */
    protected const PRODUCT_TYPES = [
        'consult-service' => 'Consulting Services',
        'compensation-project' => 'Compensation Projects'
    ];

    /**
     * The block name.
     *
     * @var string
     */
    public $name = 'Product Slider';

    /**
     * The block description.
     *
     * @var string
     */
    public $description = 'A product slider for CO2market';

    /**
     * The block category.
     *
     * @var string
     */
    public $category = 'theme';

    /**
     * The block icon.
     *
     * @var string|array
     */
    public $icon = 'editor-ul';

    /**
     * The block keywords.
     *
     * @var array
     */
    public $keywords = [];

    /**
     * The block post type allow list.
     *
     * @var array
     */
    public $post_types = ['page'];

    /**
     * The parent block type allow list.
     *
     * @var array
     */
    public $parent = [];

    /**
     * The ancestor block type allow list.
     *
     * @var array
     */
    public $ancestor = [];

    /**
     * The default block mode.
     *
     * @var string
     */
    public $mode = 'preview';

    /**
     * The default block alignment.
     *
     * @var string
     */
    public $align = '';

    /**
     * The default block text alignment.
     *
     * @var string
     */
    public $align_text = '';

    /**
     * The default block content alignment.
     *
     * @var string
     */
    public $align_content = '';

    /**
     * The default block spacing.
     *
     * @var array
     */
    public $spacing = [
        'padding' => null,
        'margin' => null,
    ];

    /**
     * The supported block features.
     *
     * @var array
     */
    public $supports = [
        'align' => false,
        'align_text' => false,
        'align_content' => false,
        'full_height' => false,
        'anchor' => false,
        'mode' => true,
        'multiple' => true,
        'jsx' => true,
        'color' => [
            'background' => false,
            'text' => false,
            'gradients' => false,
        ],
        'spacing' => [
            'padding' => false,
            'margin' => false,
        ],
    ];

    /**
     * The block styles.
     *
     * @var array
     */
    public $styles = ['light', 'dark'];

    /**
     * The block preview example data.
     *
     * @var array
     */
    public $example = [
        'products' => [],
        'product_type' => 'compensation-project'
    ];

    /**
     * The block template.
     *
     * @var array
     */
    public $template = [
        'core/heading' => ['placeholder' => 'Hello World'],
        'core/paragraph' => ['placeholder' => 'Welcome to the Product Slider block.'],
    ];

    /**
     * Data to be passed to the block before rendering.
     */
    public function with(): array
    {
        return [
            'products' => $this->get_wc_products(),
            'slider_id' => uniqid('product-slider-'),
        ];
    }

    /**
     * The block field group.
     */
    public function fields(): array
    {
        $fields = Builder::make('product_slider');

        $fields
            ->addSelect('product_type', [
                'label' => 'Product Type',
                'instructions' => 'Select the type of products to display',
                'required' => 1,
                'choices' => self::PRODUCT_TYPES,
                'default_value' => 'compensation-project',
                'ui' => 1,
            ])
            ->addPostObject('products', [
                'label' => 'Products',
                'instructions' => 'Select products to display in the slider',
                'required' => 1,
                'post_type' => ['product'],
                'multiple' => 1,
                'return_format' => 'object',
                'ui' => 1,
                'filters' => [
                    [
                        'param' => 'taxonomy',
                        'operator' => '==',
                        'value' => 'product_type',
                        'reference' => 'product_type' // This will reference the product_type field value
                    ]
                ]
            ]);

        return $fields->build();
    }

    /**
     * Retrieve the products.
     *
     * @return array<Product>
     */
    public function get_wc_products(): array
    {
        $products = get_field('products');
        $ids = array_map(function ($product) {
            $product_id = $product->ID;
            return $product_id;
        }, $products);
        $args = [
            "include" => $ids,
        ];
        $_products = wc_get_products($args);

        return array_map(function (WC_Product $product) {
            return new Product($product);
        }, $_products);
    }

    /**
     * Retrieve the products.
     *
     * @return array
     */
    public function getProducts()
    {
        $products = get_field('products');
        $product_type = get_field('product_type');

        if (!$products) {
            return $this->example['products'];
        }

        // If we have products and a type, filter them
        if ($product_type && is_array($products)) {
            $products = array_filter($products, function ($product) use ($product_type) {
                $terms = get_the_terms($product->ID, 'product_type');
                if (!$terms || is_wp_error($terms)) {
                    return false;
                }

                return array_reduce($terms, function ($carry, $term) use ($product_type) {
                    return $carry || $term->slug === $product_type;
                }, false);
            });
        }

        return $products;
    }

    /**
     * Assets enqueued when rendering the block.
     */
    public function assets(array $block): void {
        // Enqueue Swiper's CSS
        wp_enqueue_style(
            'swiper-css',
            'https://cdn.jsdelivr.net/npm/swiper@11.0.0/swiper-bundle.min.css',
            array(),
            '11.0.0'
        );
    
        // Enqueue Swiper's JavaScript
        wp_enqueue_script(
            'swiper-js',
            'https://cdn.jsdelivr.net/npm/swiper@11.0.0/swiper-bundle.min.js',
            array(),
            '11.0.0',
            false
        );
    }
}
