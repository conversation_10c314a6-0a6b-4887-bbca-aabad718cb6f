<?php

namespace App\Blocks;

use Log1x\AcfComposer\Block;
use Log1x\AcfComposer\Builder;
use App\Models\FootprintExample;

class FootprintExampleSlider extends Block
{
    /**
     * The block name.
     *
     * @var string
     */
    public $name = 'Footprint Example Slider';

    /**
     * The block description.
     *
     * @var string
     */
    public $description = 'Footprint example slider CO2market';

    /**
     * The block category.
     *
     * @var string
     */
    public $category = 'theme';

    /**
     * The block icon.
     *
     * @var string|array
     */
    public $icon = 'editor-ul';

    /**
     * The block keywords.
     *
     * @var array
     */
    public $keywords = [];

    /**
     * The block post type allow list.
     *
     * @var array
     */
    public $post_types = ['page'];

    /**
     * The parent block type allow list.
     *
     * @var array
     */
    public $parent = [];

    /**
     * The ancestor block type allow list.
     *
     * @var array
     */
    public $ancestor = [];

    /**
     * The default block mode.
     *
     * @var string
     */
    public $mode = 'preview';

    /**
     * The default block alignment.
     *
     * @var string
     */
    public $align = '';

    /**
     * The default block text alignment.
     *
     * @var string
     */
    public $align_text = '';

    /**
     * The default block content alignment.
     *
     * @var string
     */
    public $align_content = '';

    /**
     * The default block spacing.
     *
     * @var array
     */
    public $spacing = [
        'padding' => null,
        'margin' => null,
    ];

    /**
     * The supported block features.
     *
     * @var array
     */
    public $supports = [
        'align' => true,
        'align_text' => false,
        'align_content' => false,
        'full_height' => false,
        'anchor' => false,
        'mode' => true,
        'multiple' => true,
        'jsx' => true,
        'color' => [
            'background' => false,
            'text' => false,
            'gradients' => false,
        ],
        'spacing' => [
            'padding' => false,
            'margin' => false,
        ],
    ];

    /**
     * The block styles.
     *
     * @var array
     */
    public $styles = ['light', 'dark'];

    /**
     * The block preview example data.
     *
     * @var array
     */
    public $example = [
        'examples' => []
    ];

    /**
     * The block template.
     *
     * @var array
     */
    public $template = [
        'core/heading' => ['placeholder' => 'Hello World'],
        'core/paragraph' => ['placeholder' => 'Welcome to the Footprint Example Slider block.'],
    ];

    /**
     * Data to be passed to the block before rendering.
     */
    public function with(): array
    {
        return [
            'examples' => $this->get_examples(),
            'slider_id' => uniqid('footprint-example-slider-'),
        ];
    }

    /**
     * The block field group.
     */
    public function fields(): array
    {
        $fields = Builder::make('footprint_example_slider');

        $fields
            ->addPostObject('examples', [
                'label' => 'Footprint Examples',
                'instructions' => 'Select footprint examples to display in the slider',
                'required' => 1,
                'post_type' => ['footprint-example'],
                'multiple' => 1,
                'return_format' => 'object',
                'ui' => 1,
            ]);

        return $fields->build();
    }

    /**
     * Retrieve the examples.
     *
     * @return array<FootprintExample>
     */
    public function get_examples(): array
    {
        $examples = get_field('examples') ?: [];
        
        return array_map(function ($post) {
            return new FootprintExample($post);
        }, $examples);
    }

    /**
     * Assets enqueued when rendering the block.
     */
    public function assets(array $block): void
    {
        wp_enqueue_style(
            'swiper-css',
            'https://cdn.jsdelivr.net/npm/swiper@11.0.0/swiper-bundle.min.css',
            array(),
            '11.0.0'
        );
    
        wp_enqueue_script(
            'swiper-js',
            'https://cdn.jsdelivr.net/npm/swiper@11.0.0/swiper-bundle.min.js',
            array(),
            '11.0.0',
            false
        );
    }
}
