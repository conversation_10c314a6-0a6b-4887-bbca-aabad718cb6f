<?php

namespace App\Blocks;

use Log1x\AcfComposer\Block;
use StoutLogic\AcfBuilder\FieldsBuilder;

class MainHero extends Block
{
    /**
     * The block name.
     *
     * @var string
     */
    public $name = 'Main Hero';

    /**
     * The block description.
     *
     * @var string
     */
    public $description = 'A main hero block with corner images';

    /**
     * The block category.
     *
     * @var string
     */
    public $category = 'theme';

    /**
     * The block icon.
     *
     * @var string|array
     */
    public $icon = 'format-image';

    /**
     * The block keywords.
     *
     * @var array
     */
    public $keywords = ['hero', 'main', 'banner'];

    /**
     * The block post type allow list.
     *
     * @var array
     */
    public $post_types = ['post', 'page'];

    /**
     * The parent block type allow list.
     *
     * @var array
     */
    public $parent = [];

    /**
     * The ancestor block type allow list.
     *
     * @var array
     */
    public $ancestor = [];

    /**
     * The default block mode.
     *
     * @var string
     */
    public $mode = 'preview';

    /**
     * The default block alignment.
     *
     * @var string
     */
    public $align = '';

    /**
     * The default block text alignment.
     *
     * @var string
     */
    public $align_text = '';

    /**
     * The default block content alignment.
     *
     * @var string
     */
    public $align_content = '';

    /**
     * The default block spacing.
     *
     * @var array
     */
    public $spacing = [
        'padding' => null,
        'margin' => null,
    ];

    /**
     * The supported block features.
     *
     * @var array
     */
    public $supports = [
        'align' => false,
        'mode' => false,
        'jsx' => true,
        'color' => [
            'background' => false,
            'text' => false,
            'gradients' => false,
        ],
        'spacing' => [
            'padding' => false,
            'margin' => false,
        ],
    ];

    /**
     * The block styles.
     *
     * @var array
     */
    public $styles = ['light', 'dark'];

    /**
     * The block preview example data.
     *
     * @var array
     */
    public $example = [
        'items' => [
            ['item' => 'Item one'],
            ['item' => 'Item two'],
            ['item' => 'Item three'],
        ],
    ];

    /**
     * The block template.
     *
     * @var array
     */
    public $template = [
        'core/heading' => ['placeholder' => 'Hello World'],
        'core/paragraph' => ['placeholder' => 'Welcome to the Main Hero block.'],
    ];

    /**
     * Data to be passed to the block before rendering.
     */
    public function with(): array
    {
        return [
            'top_left_image' => get_field('top_left_image'),
            'bottom_right_image' => get_field('bottom_right_image'),
            'left_image_width' => get_field('left_image_width'),
            'right_image_width' => get_field('right_image_width'),
        ];
    }

    /**
     * The block field group.
     */
    public function fields(): array
    {
        $mainHero = new FieldsBuilder('main_hero');

        $mainHero
            ->addImage('top_left_image', [
                'label' => 'Top Left Corner Image',
                'instructions' => 'Select an image for the top left corner',
                'required' => true,
                'return_format' => 'array',
                'preview_size' => 'medium',
            ])
            ->addImage('bottom_right_image', [
                'label' => 'Bottom Right Corner Image',
                'instructions' => 'Select an image for the bottom right corner',
                'required' => true,
                'return_format' => 'array',
                'preview_size' => 'medium',
            ])
            ->addText('left_image_width', [
                'label' => 'Left Image Width',
                'instructions' => 'Specify the width for the left image (e.g., 50%)',
                'required' => false,
            ])
            ->addText('right_image_width', [
                'label' => 'Right Image Width',
                'instructions' => 'Specify the width for the right image (e.g., 50%)',
                'required' => false,
            ]);

        return $mainHero->build();
    }

    /**
     * Assets enqueued when rendering the block.
     */
    public function assets(array $block): void
    {
        //
    }
}
