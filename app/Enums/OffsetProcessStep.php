<?php

namespace App\Enums;

enum OffsetProcessStep: string
{
    case CALCULATE = 'calculate';
    case OFFSET = 'offset';
    case COMMUNICATE = 'communicate';
    case COMPLETE = 'complete';

    public function toString(): string
    {
        return $this->value;
    }

    public function toLabel(): string
    {
        return match ($this) {
            self::CALCULATE => __('Calculate', CO2MARKET_TEXT_DOMAIN),
            self::OFFSET => __('Offset', CO2MARKET_TEXT_DOMAIN),
            self::COMMUNICATE => __('Communicate', CO2MARKET_TEXT_DOMAIN),
            self::COMPLETE => __('Complete', CO2MARKET_TEXT_DOMAIN),
            default => __('Unknown', CO2MARKET_TEXT_DOMAIN),
        };
    }

    public function hasCompletedCalculateStep(): bool
    {
        return $this !== self::CALCULATE;
    }

    public function hasCompletedOffsetStep(): bool
    {
        return $this->hasCompletedCalculateStep() && $this !== self::OFFSET;
    }

    public function hasCompletedCommunicateStep(): bool
    {
        return $this->hasCompletedOffsetStep() && $this !== self::COMMUNICATE;
    }

    public function hasCompletedCompleteStep(): bool
    {
        return $this->hasCompletedCommunicateStep() && $this === self::COMPLETE;
    }

    public function getStepNumber(): int
    {
        return match ($this) {
            self::CALCULATE => 1,
            self::OFFSET => 2,
            self::COMMUNICATE => 3,
            self::COMPLETE => 4,
            default => 1,
        };
    }


    public static function fromString(string $value): ?self
    {
        return match ($value) {
            'calculate' => self::CALCULATE,
            'offset' => self::OFFSET,
            'communicate' => self::COMMUNICATE,
            'complete' => self::COMPLETE,
            default => self::CALCULATE,
        };
    }

    public function toArray(): array
    {
        return [
            'value' => $this->value,
            'label' => $this->toLabel(),
            'step_number' => $this->getStepNumber(),
        ];
    }
}
