<?php

namespace App\Enums;

enum CalculationType: string
{
    case AUDITED = 'audited';
    case APPROVED = 'approved';
    case UNAPPROVED = 'unapproved';
    case PROFESSIONAL = 'professional';
    case GENERIC = 'table-based';

    public function toString(): string
    {
        return $this->value;
    }

    public function toLabel(): string
    {
        return match ($this) {
            self::AUDITED => __('Audited', CO2MARKET_TEXT_DOMAIN),
            self::APPROVED => __('Approved', CO2MARKET_TEXT_DOMAIN),
            self::UNAPPROVED => __('Unapproved', CO2MARKET_TEXT_DOMAIN),
            self::GENERIC => __('Generic', CO2MARKET_TEXT_DOMAIN),
            self::PROFESSIONAL => __('Professional', CO2MARKET_TEXT_DOMAIN),
            default => __('Unknown', CO2MARKET_TEXT_DOMAIN),
        };
    }

    public function toColor(): string
    {
        return match ($this) {
            self::AUDITED => 'green',
            self::APPROVED => 'blue',
            self::UNAPPROVED => 'red',
            self::GENERIC => 'orange',
            self::PROFESSIONAL => 'blue',
            default => 'gray',
        };
    }

    public static function fromString(string $value): ?self
    {
        return match ($value) {
            'audited' => self::AUDITED,
            'approved' => self::APPROVED,
            'unapproved' => self::UNAPPROVED,
            'generic' => self::GENERIC,
            'professional' => self::PROFESSIONAL,
            default => null
        };
    }
}
