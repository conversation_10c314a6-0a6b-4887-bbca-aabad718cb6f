<?php

namespace App\Enums;

enum Role: string
{
    case ADMIN = 'administrator';
    case COMPENSATION_PROJECT_OWNER = 'compensation-project-owner';
    case CONSULTANT = 'consultant';

    public function toString(): string
    {
        return $this->value;
    }

    public function getLabel(): string
    {
        return match ($this) {
            self::ADMIN => __('Administrator', CO2MARKET_TEXT_DOMAIN),
            self::COMPENSATION_PROJECT_OWNER => __('Compensation Project Owner', CO2MARKET_TEXT_DOMAIN),
            self::CONSULTANT => __('Consultant', CO2MARKET_TEXT_DOMAIN),
            default => __('Unknown', CO2MARKET_TEXT_DOMAIN),
        };
    }

    public function getColor(): string
    {
        return match ($this) {
            self::ADMIN => 'blue',
            self::COMPENSATION_PROJECT_OWNER => 'yellow',
            self::CONSULTANT => 'green',
            default => 'gray',
        };
    }

    public static function fromString(string $value): ?self
    {
        return match ($value) {
            'administrator' => self::ADMIN,
            'compensation-project-owner' => self::COMPENSATION_PROJECT_OWNER,
            'consultant' => self::CONSULTANT,
            default => null
        };
    }

    public function toArray(): array
    {
        return [
            'label' => $this->getLabel(),
            'color' => $this->getColor(),
            'role' => $this->toString(),
        ];
    }
}
