<?php

namespace App\Livewire\Product;

use Livewire\Component;
use App\Models\Product;
use Livewire\WithPagination;
use Livewire\Attributes\Url;

class ServiceListing extends Component
{
    use WithPagination;
    public $perPage = 9;

    #[Url]
    public $search = '';

    #[Url]
    public $sort = 'default';


    #[Url]
    public $countries = [];

    #[Url]
    public $types = [];

    #[Url]
    public $min_price = null;

    #[Url]
    public $max_price = null;


    #[Url]
    public $min_stars = null;

    #[Url]
    public $min_reviews = null;

    #[Url]
    public $max_reviews = null;

    public $loading = true;


    public function mount()
    {

        // Get search from URL on initial load
        $search = request()->query('search', '');
        if (!empty($search)) {
            $this->search = $search;
        }

        // Get sort from URL on initial load
        $sort = request()->query('sort', 'default');
        if (!empty($sort)) {
            $this->sort = $sort;
        }


        // Get countries from URL on initial load
        $countries = request()->query('countries', []);
        if (!empty($countries)) {
            $this->countries = is_array($countries) ? $countries : explode(',', $countries);
        }

        // Get types from URL on initial load
        $types = request()->query('types', []);
        if (!empty($types)) {
            $this->types = is_array($types) ? $types : explode(',', $types);
        }

        // Get price filters from URL
        $min_price = request()->query('min_price');
        $max_price = request()->query('max_price');

        if ($min_price !== null) {
            $this->min_price = (float) $min_price;
        }

        if ($max_price !== null) {
            $this->max_price = (float) $max_price;
        }

        // Get star rating filter from URL
        $min_stars = request()->query('min_stars');
        if ($min_stars !== null) {
            $this->min_stars = (int) $min_stars;
        }

        // Get reviews filter from URL
        $min_reviews = request()->query('min_reviews');
        $max_reviews = request()->query('max_reviews');

        if ($min_reviews !== null) {
            $this->min_reviews = (int) $min_reviews;
        }

        if ($max_reviews !== null) {
            $this->max_reviews = (int) $max_reviews;
        }
    }

    public function update_price_range($min, $max)
    {
        $this->min_price = $min;
        $this->max_price = $max;
        $this->resetPage();
    }

    public function update_min_stars($stars)
    {
        // Toggle off if clicking the same star rating
        if ($this->min_stars === $stars) {
            $this->min_stars = null;
        } else {
            $this->min_stars = $stars;
        }

        $this->resetPage();
    }

    public function update_reviews_range($min, $max)
    {
        $this->min_reviews = $min;
        $this->max_reviews = $max;
        $this->resetPage();
    }

    public function get_filters(){
        return [
            'min_stars' => $this->min_stars,
            'min_reviews' => $this->min_reviews,
            'max_reviews' => $this->max_reviews,
            'min_price' => $this->min_price,
            'max_price' => $this->max_price,
            'countries' => $this->countries,
            'types' => $this->types,
            'per_page' => $this->perPage,
            'page' => $this->getPage()
        ];
    }

    public function clear_all_filters()
    {
        $this->countries = [];
        $this->types = [];
        $this->min_price = null;
        $this->max_price = null;
        $this->min_stars = null;
        $this->min_reviews = null;
        $this->max_reviews = null;

        $this->resetPage();
    }

    protected function get_products()
    {
        $filters = $this->get_filters();

        $args = [
            'post_type' => 'product',
            'posts_per_page' => $filters["per_page"],
            'paged' => $filters["page"],
            'tax_query' => [
                [
                    'taxonomy' => 'product_cat',
                    'field' => 'slug',
                    'terms' => 'consult-service',
                    'operator' => 'IN'
                ]
            ]
        ];

        // Add search if present
        if (!empty($this->search)) {
            $args['s'] = $this->search;
        }

        $countries = $filters['countries'];
        $types = $filters['types'];
        $min_price = $filters['min_price'];
        $max_price = $filters['max_price'];
        $min_stars = $filters['min_stars'];
        $min_reviews = $filters['min_reviews'];
        $max_reviews = $filters['max_reviews'];


        // Add countries filter if countries are selected
        if (!empty($countries)) {
            $args['meta_query'][] = [
                'key' => 'country',
                'value' => $countries,
                'compare' => 'IN'
            ];
        }

        // Add types filter if types are selected
        if (!empty($types)) {
            $args['tax_query'][] = [
                'taxonomy' => 'product_cat',
                'field' => 'slug',
                'terms' => $types,
                'operator' => 'IN'
            ];
        }


        // Add price filter if min or max price is set
        if ($min_price !== null || $max_price !== null) {
            $args['meta_query'][] = [
                'key' => '_price',
                'value' => [
                    $min_price ?? 0,
                    $max_price ?? Product::get_max_price_of_cat("consult-service")
                ],
                'type' => 'NUMERIC',
                'compare' => 'BETWEEN'
            ];
        }

        // Add rating filter if min_stars is set
        if ($min_stars !== null) {
            $args['meta_query'][] = [
                'key' => '_wc_average_rating',
                'value' => $min_stars,
                'type' => 'NUMERIC',
                'compare' => '>='
            ];
        }

        // Add reviews filter if min or max reviews is set
        if ($min_reviews !== null || $max_reviews !== null) {
            $args['meta_query'][] = [
                'key' => '_wc_review_count',
                'value' => [
                    $min_reviews ?? 0,
                    $max_reviews ?? Product::get_max_reviews_count()
                ],
                'type' => 'NUMERIC',
                'compare' => 'BETWEEN'
            ];
        }

        // Add sorting
        switch ($this->sort) {
            case 'rating_desc':
                $args['meta_key'] = '_wc_average_rating';
                $args['orderby'] = 'meta_value_num';
                $args['order'] = 'DESC';
                break;
            case 'rating_asc':
                $args['meta_key'] = '_wc_average_rating';
                $args['orderby'] = 'meta_value_num';
                $args['order'] = 'ASC';
                break;
            case 'price_desc':
                $args['meta_key'] = '_price';
                $args['orderby'] = 'meta_value_num';
                $args['order'] = 'DESC';
                break;
            case 'price_asc':
                $args['meta_key'] = '_price';
                $args['orderby'] = 'meta_value_num';
                $args['order'] = 'ASC';
                break;
            default:
                $args['orderby'] = 'date';
                $args['order'] = 'DESC';
        }

        $query = new \WP_Query($args);
        $products = [];

        while ($query->have_posts()) {
            $query->the_post();
            $products[] = new Product(get_the_ID());
        }

        wp_reset_postdata();

        return [
            'items' => $products,
            'total' => $query->found_posts,
            'per_page' => $filters["per_page"],
            'current_page' => $filters["page"],
            'last_page' => ceil($query->found_posts / $filters["per_page"])
        ];
    }

    public function has_active_filters()
    {
        return  !empty($this->countries)
            || !empty($this->types)
            || $this->min_stars !== null
            || $this->min_reviews !== null
            || $this->max_reviews !== null
            || $this->max_price !== null
            || $this->min_price !== null;
    }

    /**
     * Get all subcategories of consult-service
     *
     * @return array
     */
    static public function get_service_types()
    {
        $service_types = [];

        // Get the parent category ID for consult-service
        $parent_term = get_term_by('slug', 'consult-service', 'product_cat');

        if ($parent_term) {
            // Get all subcategories of the parent
            $subcategories = get_terms([
                'taxonomy' => 'product_cat',
                'hide_empty' => false,
                'parent' => $parent_term->term_id
            ]);

            if (!is_wp_error($subcategories) && !empty($subcategories)) {
                foreach ($subcategories as $subcategory) {
                    $service_types[] = [
                        'value' => $subcategory->slug,
                        'label' => $subcategory->name
                    ];
                }
            }
        }

        return $service_types;
    }

    public function render()
    {
        return view('livewire.product.service-listing', [
            'products' => $this->get_products(),
            'has_active_filters' => $this->has_active_filters(),
            'service_types' => self::get_service_types()
        ]);
    }

    public function updatedSearch()
    {
        $this->resetPage();
    }
}
