<?php

namespace App\Livewire\Product;

use Livewire\Component;
use App\Models\Product;
use Livewire\WithPagination;
use Livewire\Attributes\Url;

class ProductListing extends Component
{
    use WithPagination;
    public $perPage = 9;
    
    #[Url]
    public $search = '';
    
    #[Url]
    public $sort = 'default';
    
    #[Url]
    public $project_types = [];
    
    #[Url]
    public $sdgs = [];

    #[Url]
    public $countries = [];

    #[Url]
    public $min_price = null;

    #[Url]
    public $max_price = null;

    #[Url]
    public $min_quantity = null;

    #[Url]
    public $max_quantity = null;

    #[Url]
    public $min_stars = null;

    #[Url]
    public $min_reviews = null;

    #[Url]
    public $max_reviews = null;

    #[Url]
    public $certificates = [];

    public $loading = true;


    public function mount()
    {
        
        // Get search from URL on initial load
        $search = request()->query('search', '');
        if (!empty($search)) {
            $this->search = $search;
        }

        // Get sort from URL on initial load
        $sort = request()->query('sort', 'default');
        if (!empty($sort)) {
            $this->sort = $sort;
        }

        // Get project types from URL on initial load
        $types = request()->query('project_types', []);
        if (!empty($types)) {
            $this->project_types = is_array($types) ? $types : explode(',', $types);
        }

        // Get SDGs from URL on initial load
        $sdgs = request()->query('sdgs', []);
        if (!empty($sdgs)) {
            $this->sdgs = is_array($sdgs) ? $sdgs : explode(',', $sdgs);
        }

        // Get countries from URL on initial load
        $countries = request()->query('countries', []);
        if (!empty($countries)) {
            $this->countries = is_array($countries) ? $countries : explode(',', $countries);
        }

        // Get certificates from URL on initial load
        $certificates = request()->query('certificates', []);
        if (!empty($certificates)) {
            $this->certificates = is_array($certificates) ? $certificates : explode(',', $certificates);
        }

        // Get price filters from URL
        $min_price = request()->query('min_price');
        $max_price = request()->query('max_price');
        
        if ($min_price !== null) {
            $this->min_price = (float) $min_price;
        }
        
        if ($max_price !== null) {
            $this->max_price = (float) $max_price;
        }

        // Get quantity filters from URL
        $min_quantity = request()->query('min_quantity');
        $max_quantity = request()->query('max_quantity');
        
        if ($min_quantity !== null) {
            $this->min_quantity = (int) $min_quantity;
        }
        
        if ($max_quantity !== null) {
            $this->max_quantity = (int) $max_quantity;
        }

        // Get star rating filter from URL
        $min_stars = request()->query('min_stars');
        if ($min_stars !== null) {
            $this->min_stars = (int) $min_stars;
        }

        // Get reviews filter from URL
        $min_reviews = request()->query('min_reviews');
        $max_reviews = request()->query('max_reviews');

        if ($min_reviews !== null) {
            $this->min_reviews = (int) $min_reviews;
        }

        if ($max_reviews !== null) {
            $this->max_reviews = (int) $max_reviews;
        }
    }

    public function toggle_project_type($type)
    {
        if (in_array($type, $this->project_types)) {
            $this->project_types = array_values(array_filter(
                $this->project_types, 
                fn($t) => $t !== $type
            ));
        } else {
            $this->project_types[] = $type;
        }
        
        $this->resetPage();
    }

    public function toggle_sdg($sdg)
    {
        if (in_array($sdg, $this->sdgs)) {
            $this->sdgs = array_values(array_filter(
                $this->sdgs, 
                fn($s) => $s !== $sdg
            ));
        } else {
            $this->sdgs[] = $sdg;
        }
        
        $this->resetPage();
    }
    public function update_price_range($min, $max)
    {
        $this->min_price = $min;
        $this->max_price = $max;
        $this->resetPage();
    }

    public function update_quantity_range($min, $max)
    {
        $this->min_quantity = $min;
        $this->max_quantity = $max;
        $this->resetPage();
    }

    public function update_min_stars($stars)
    {
        // Toggle off if clicking the same star rating
        if ($this->min_stars === $stars) {
            $this->min_stars = null;
        } else {
            $this->min_stars = $stars;
        }
        
        $this->resetPage();
    }

    public function update_reviews_range($min, $max)
    {
        $this->min_reviews = $min;
        $this->max_reviews = $max;
        $this->resetPage();
    }

    public function toggle_certificate($certificate)
    {
        if (in_array($certificate, $this->certificates)) {
            $this->certificates = array_values(array_filter(
                $this->certificates, 
                fn($c) => $c !== $certificate
            ));
        } else {
            $this->certificates[] = $certificate;
        }
        
        $this->resetPage();
    }

    public function get_filters(){
        return [
            'min_stars' => $this->min_stars,
            'min_reviews' => $this->min_reviews,
            'max_reviews' => $this->max_reviews,
            'min_price' => $this->min_price,
            'max_price' => $this->max_price,
            'min_quantity' => $this->min_quantity,
            'max_quantity' => $this->max_quantity,
            'certificates' => $this->certificates,
            'project_types' => $this->project_types,
            'sdgs' => $this->sdgs,
            'countries' => $this->countries,
            'per_page' => $this->perPage,
            'page' => $this->getPage()
        ];
    }

    public function clear_all_filters()
    {
        $this->project_types = [];
        $this->sdgs = [];
        $this->countries = [];
        $this->certificates = [];
        $this->min_price = null;
        $this->max_price = null;
        $this->min_quantity = null;
        $this->max_quantity = null;
        $this->min_stars = null;
        $this->min_reviews = null;
        $this->max_reviews = null;
        
        $this->resetPage();
    }

    protected function get_products()
    {
        $filters = $this->get_filters();
        
        $args = [
            'post_type' => 'product',
            'posts_per_page' => $filters["per_page"],
            'paged' => $filters["page"],
            'tax_query' => [
                [
                    'taxonomy' => 'product_cat',
                    'field' => 'slug',
                    'terms' => 'compensation-project',
                    'operator' => 'IN'
                ]
            ]
        ];

        // Add search if present
        if (!empty($this->search)) {
            $args['s'] = $this->search;
        }

        $project_types = $filters['project_types'];
        $sdgs = $filters['sdgs'];
        $countries = $filters['countries'];
        $certificates = $filters['certificates'];
        $min_price = $filters['min_price'];
        $max_price = $filters['max_price'];
        $min_stars = $filters['min_stars'];
        $min_reviews = $filters['min_reviews'];
        $max_reviews = $filters['max_reviews'];
        $min_quantity = $filters["max_quantity"];
        $max_quantity = $filters["max_quantity"];
        



        // Add project type filter if types are selected
        if (!empty($project_types)) {
            $args['tax_query'][] = [
                'taxonomy' => 'comp_project_type',
                'field' => 'slug',
                'terms' => $project_types,
                'operator' => 'IN'
            ];
        }

        // Add SDG filter if SDGs are selected
        if (!empty($sdgs)) {
            $args['tax_query'][] = [
                'taxonomy' => 'sdg',
                'field' => 'slug',
                'terms' => $sdgs,
                'operator' => 'IN'
            ];
        }

        // Add countries filter if countries are selected
        if (!empty($countries)) {
            $args['meta_query'][] = [
                'key' => 'country',
                'value' => $countries,
                'compare' => 'IN'
            ];
        }

        // Add certificates filter if certificates are selected
        if (!empty($certificates)) {
            $args['meta_query'] = $args['meta_query'] ?? [];
            
            $certificationQueries = ['relation' => 'OR'];

            foreach ($certificates as $certification) {
                // Add query for serialized certificate
                $certificationQueries[] = [
                    'key' => 'certificate',
                    'value' => '"' . $certification . '"',
                    'compare' => 'LIKE'
                ];

                // Add query for non-serialized certificate
                $certificationQueries[] = [
                    'key' => 'certificate',
                    'value' => $certification,
                    'compare' => '='
                ];
            }

            $args['meta_query'][] = $certificationQueries;
        }

        // Add price filter if min or max price is set
        if ($min_price !== null || $max_price !== null) {
            $args['meta_query'][] = [
                'key' => '_price',
                'value' => [
                    $min_price ?? 0,
                    $max_price ?? Product::get_max_price_of_cat("compensation-project")
                ],
                'type' => 'NUMERIC',
                'compare' => 'BETWEEN'
            ];
        }

        // Add quantity filter if min or max quantity is set
        if ($min_quantity !== null || $max_quantity !== null) {
            $args['meta_query'][] = [
                'key' => '_stock',
                'value' => [
                    $min_quantity ?? 0,
                    $max_quantity ?? Product::get_max_available_tonnes()
                ],
                'type' => 'NUMERIC',
                'compare' => 'BETWEEN'
            ];
        }

        // Add rating filter if min_stars is set
        if ($min_stars !== null) {
            $args['meta_query'][] = [
                'key' => '_wc_average_rating',
                'value' => $min_stars,
                'type' => 'NUMERIC',
                'compare' => '>='
            ];
        }

        // Add reviews filter if min or max reviews is set
        if ($min_reviews !== null || $max_reviews !== null) {
            $args['meta_query'][] = [
                'key' => '_wc_review_count',
                'value' => [
                    $min_reviews ?? 0,
                    $max_reviews ?? Product::get_max_reviews_count()
                ],
                'type' => 'NUMERIC',
                'compare' => 'BETWEEN'
            ];
        }

        // Add sorting
        switch ($this->sort) {
            case 'rating_desc':
                $args['meta_key'] = '_wc_average_rating';
                $args['orderby'] = 'meta_value_num';
                $args['order'] = 'DESC';
                break;
            case 'rating_asc':
                $args['meta_key'] = '_wc_average_rating';
                $args['orderby'] = 'meta_value_num';
                $args['order'] = 'ASC';
                break;
            case 'price_desc':
                $args['meta_key'] = '_price';
                $args['orderby'] = 'meta_value_num';
                $args['order'] = 'DESC';
                break;
            case 'price_asc':
                $args['meta_key'] = '_price';
                $args['orderby'] = 'meta_value_num';
                $args['order'] = 'ASC';
                break;
            default:
                $args['orderby'] = 'date';
                $args['order'] = 'DESC';
        }

        $query = new \WP_Query($args);
        $products = [];

        while ($query->have_posts()) {
            $query->the_post();
            $products[] = new Product(get_the_ID());
        }

        wp_reset_postdata();

        return [
            'items' => $products,
            'total' => $query->found_posts,
            'per_page' => $filters["per_page"],
            'current_page' => $filters["page"],
            'last_page' => ceil($query->found_posts / $filters["per_page"])
        ];
    }

    public function has_active_filters()
    {
        return !empty($this->project_types) 
            || !empty($this->sdgs)
            || !empty($this->countries)
            || !empty($this->certificates)
            || $this->min_quantity !== null
            || $this->max_quantity !== null
            || $this->min_stars !== null
            || $this->min_reviews !== null
            || $this->max_reviews !== null;
    }

    public function render()
    {
        return view('livewire.product.product-listing', [
            'products' => $this->get_products(),
            'has_active_filters' => $this->has_active_filters()
        ]);
    }

    public function updatedSearch()
    {
        $this->resetPage();
    }
}
