<?php

namespace App\View\Composers;

use Roots\Acorn\View\Composer;
use App\Models\User;

class GlobalComposer extends Composer
{
    /**
     * List of views served by this composer.
     *
     * @var string[]
     */
    protected static $views = ['*'];

    public function with()
    {

        if (is_admin()) {
            return [
                "user" => User::getCurrentUser(),
                "processes" => [],
            ];
        }

        return [
            "user" => User::getCurrentUser(),
            "processes" => User::getProcesses(),
        ];
    }
}
