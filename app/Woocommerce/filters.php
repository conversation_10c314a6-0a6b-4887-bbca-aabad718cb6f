<?php

// No namespace to ensure global scope for WordPress hooks

use App\Models\OffsetProcess;
use App\Models\User;

use Illuminate\Support\Facades\Log;

add_action('woocommerce_add_to_cart', function($cart_item_key, $product_id) {
  session()->flash('wc_cart_success', __('Product has been added to your cart.'));
}, 10, 2);

add_action('woocommerce_add_notice', function($message, $notice_type) {
  if ($notice_type === 'error') {
      session()->flash('wc_cart_error', $message);
  }
}, 10, 2);

add_action('woocommerce_cart_item_removed', function($cart_item_key, $instance) {
  session()->flash('wc_cart_error', __('Product has been removed from your cart.'));
}, 10, 2);


/**
 * Add offset process ID to order meta data when an order is placed
 */
add_action('woocommerce_store_api_checkout_order_processed', function(WC_Order $order) {
  $selectedProcessId = null;
    try {
        // Get the selected process ID from cookie
        $selectedProcessId = User::getSelectedProcessId();

        $context = [
          'offset-process-id' => $selectedProcessId,
          'order' => $order
        ];

        if (!$selectedProcessId) {
            Log::warning("No selected process ID found in cookie", $context);
            return;
        }
        // Create a new OffsetProcess instance
        $offsetProcess = OffsetProcess::get($selectedProcessId);

        if (!$offsetProcess) {
            Log::warning("Could not find offset process", $context);
            return;
        }

        // Add the order ID to the offset process
        $order->add_meta_data('offset_process_id', $selectedProcessId, true);
        Log::info("Added process to order", $context);

        $offsetProcess->addOrder($order->ID);
        Log::info("Added order to offset process", $context);

        // We'll add the order to the offset process after the order is created and has an ID
    } catch (\Exception $e) {
        // Log error but don't interrupt checkout
        $message = 'Error adding offset process to order: ' . $e->getMessage();
        Log::critical($message, ['id'=> $selectedProcessId,'order'=> $order]);
    }
});



add_action('woocommerce_admin_order_data_after_order_details', 'show_all_order_meta_admin_page');

function show_all_order_meta_admin_page($order){
    echo '<div class="order_data_column">';
    echo '<h4>' . __('All Order Meta', 'woocommerce') . '</h4>';

    $meta_data = $order->get_meta_data();

    if (!empty($meta_data)) {
        echo '<ul>';
        foreach ($meta_data as $meta) {
            echo '<li><strong>' . esc_html($meta->key) . ':</strong> ' . esc_html(print_r($meta->value, true)) . '</li>';
        }
        echo '</ul>';
    } else {
        echo '<p>No custom meta data found.</p>';
    }

    echo '</div>';
}
