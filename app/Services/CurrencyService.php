<?php

namespace App\Services;

use Exception;

/**
 * Currency Service
 * 
 * Handles currency conversion and price updates
 */
class CurrencyService
{
    /**
     * Update all product prices in EUR based on native currency prices
     * 
     * @return array Statistics about the update process
     */
    public function updateAllProductPrices(): array
    {
        $stats = [
            'total' => 0,
            'updated' => 0,
            'eur_updated' => 0,
            'currency_changed' => 0,
            'skipped' => 0,
            'errors' => 0,
            'protected_rates' => [],
            'details' => []
        ];

        // Check for rate fluctuations before processing products
        $current_rates = $this->getWPMCExchangeRates();
        $previous_rates = get_option('co2market_previous_exchange_rates', []);
        
        // Identify currencies with excessive fluctuations
        if (!empty($previous_rates) && !empty($current_rates)) {
            foreach ($current_rates as $currency => $rate) {
                if (isset($previous_rates[$currency]) && $previous_rates[$currency] > 0) {
                    $previous_rate = $previous_rates[$currency];
                    $fluctuation = abs(($rate - $previous_rate) / $previous_rate);
                    
                    if ($fluctuation > 0.10) {
                        $stats['protected_rates'][$currency] = [
                            'previous_rate' => $previous_rate,
                            'current_rate' => $rate,
                            'fluctuation_percent' => round($fluctuation * 100, 2),
                            'protected' => true
                        ];
                    }
                }
            }
        }

        // Get all products
        $args = [
            'post_type' => 'product',
            'posts_per_page' => -1,
            'fields' => 'ids',
        ];

        $query = new \WP_Query($args);
        $product_ids = $query->posts;
        $stats['total'] = count($product_ids);

        foreach ($product_ids as $product_id) {
            try {
                $native_currency = get_field('native_currency', $product_id);
                $previous_currency = get_post_meta($product_id, '_co2market_previous_currency', true);
                
                // Check if currency has changed
                $currency_changed = !empty($previous_currency) && $previous_currency !== $native_currency;
                
                // Always force update to ensure all products are processed
                $updated = $this->handleProductSave($product_id, true);
                
                if ($updated) {
                    if ($currency_changed) {
                        $stats['currency_changed']++;
                        $stats['details'][] = [
                            'product_id' => $product_id,
                            'name' => get_the_title($product_id),
                            'old_currency' => $previous_currency,
                            'new_currency' => $native_currency
                        ];
                    } elseif ($native_currency === 'EUR') {
                        $stats['eur_updated']++;
                    } else {
                        $stats['updated']++;
                    }
                } else {
                    $stats['skipped']++;
                }
            } catch (Exception $e) {
                $stats['errors']++;
                $stats['details'][] = [
                    'product_id' => $product_id,
                    'error' => $e->getMessage()
                ];
            }
        }

        // Store current time as last batch update time
        update_option('co2market_last_price_update', current_time('mysql'));

        wp_reset_postdata();
        return $stats;
    }

    /**
     * Update a single product's price in EUR based on its native currency price
     * 
     * @param int $product_id The product ID
     * @return bool True if the price was updated, false if no update was needed
     */
    public function updateProductPrice(int $product_id): bool
    {
        // Get ACF fields for native price and currency
        $native_price = get_field('native_price', $product_id);
        $native_currency = get_field('native_currency', $product_id);

        // Skip if native price or currency is not set
        if (!$native_price || !$native_currency) {
            return false;
        }

        // Get current WooCommerce price
        $product = wc_get_product($product_id);
        if (!$product) {
            throw new Exception("Product not found: {$product_id}");
        }

        // Always update the timestamp
        $current_time = current_time('mysql');
        update_field('price_last_updated', $current_time, $product_id);
        
        // For non-EUR currencies, always save the exchange rate
        if ($native_currency !== 'EUR') {
            $exchange_rate = $this->getExchangeRate($native_currency, 'EUR');
            update_field('exchange_rate_at_save', $exchange_rate, $product_id);
        }

        $current_price = (float) $product->get_price();
        
        // If the currency is EUR, directly use the native price
        if ($native_currency === 'EUR') {
            // Only update if there's a significant difference
            if (abs($current_price - $native_price) / max($current_price, 1) > 0.01) {
                $product->set_price($native_price);
                $product->set_regular_price($native_price);
                $product->save();
                
                // Additionally, update price meta directly to ensure it's set
                update_post_meta($product_id, '_price', $native_price);
                update_post_meta($product_id, '_regular_price', $native_price);
                
                return true;
            }
            return false;
        }
        
        // For non-EUR currencies, convert to EUR
        $eur_price = $this->convertToEUR($native_price, $native_currency);
        
        // If the price difference is significant (more than 1%), update it
        if (abs($current_price - $eur_price) / max($current_price, 1) > 0.01) {
            // Update WooCommerce price
            $product->set_price($eur_price);
            $product->set_regular_price($eur_price);
            $product->save();
            
            // Additionally, update price meta directly to ensure it's set
            update_post_meta($product_id, '_price', $eur_price);
            update_post_meta($product_id, '_regular_price', $eur_price);
            
            return true;
        }
        
        return false;
    }

    /**
     * Handle product creation or update - updates WooCommerce price in EUR based on native currency
     * This function should be called when a product is created or updated
     * 
     * @param int $product_id The product ID
     * @param bool $force Force update regardless of price difference
     * @return bool True if the price was updated, false if no update was needed
     */
    public function handleProductSave(int $product_id, bool $force = false): bool
    {
        // Get ACF fields for native price and currency
        $native_price = get_field('native_price', $product_id);
        $native_currency = get_field('native_currency', $product_id);

        // Skip if native price or currency is not set
        if (!$native_price || !$native_currency) {
            return false;
        }

        // Get the product
        $product = wc_get_product($product_id);
        if (!$product) {
            return false;
        }

        // Check if currency has changed by comparing with the previous record
        $currency_changed = false;
        $previous_currency = get_post_meta($product_id, '_co2market_previous_currency', true);
        
        if (!empty($previous_currency) && $previous_currency !== $native_currency) {
            $currency_changed = true;
            // Log the currency change for debugging
            error_log("Currency changed for product {$product_id}: from {$previous_currency} to {$native_currency}");
        }
        
        // Store current currency for future reference
        update_post_meta($product_id, '_co2market_previous_currency', $native_currency);
        
        // Determine the price in EUR
        $price_in_eur = ($native_currency === 'EUR') 
            ? $native_price 
            : $this->convertToEUR($native_price, $native_currency);
        
        // Get current price for comparison
        $current_price = (float) $product->get_price();
        
        // Always update the timestamp and exchange rate
        $current_time = current_time('mysql');
        update_field('price_last_updated', $current_time, $product_id);
        
        // For non-EUR currencies, always save the exchange rate
        if ($native_currency !== 'EUR') {
            $exchange_rate = $this->getExchangeRate($native_currency, 'EUR');
            update_field('exchange_rate_at_save', $exchange_rate, $product_id);
        }
        
        // Update if forced, currency changed, or price difference is significant
        if ($force || $currency_changed || abs($current_price - $price_in_eur) / max($current_price, 1) > 0.01) {
            // Update WooCommerce price fields
            $product->set_price($price_in_eur);
            $product->set_regular_price($price_in_eur);
            $product->save();
            
            // Additionally, update price meta directly to ensure it's set
            update_post_meta($product_id, '_price', $price_in_eur);
            update_post_meta($product_id, '_regular_price', $price_in_eur);
            
            return true;
        }
        
        return false;
    }

    /**
     * Convert an amount to EUR
     * 
     * @param float $amount The amount to convert
     * @param string $from_currency The source currency code
     * @return float The converted amount in EUR
     */
    public function convertToEUR(float $amount, string $from_currency): float
    {
        if ($from_currency === 'EUR') {
            return $amount;
        }

        $rate = $this->getExchangeRate($from_currency, 'EUR');
        return $amount * $rate;
    }

    /**
     * Get the exchange rate between two currencies
     * 
     * @param string $from_currency The source currency code
     * @param string $to_currency The target currency code
     * @return float The exchange rate
     */
    public function getExchangeRate(string $from_currency, string $to_currency): float
    {
        // Get exchange rates from WPMC plugin
        $exchange_rates = $this->getWPMCExchangeRates();
        
        // Check for excessive rate fluctuations
        $exchange_rates = $this->checkRateFluctuations($exchange_rates);
        
        if (empty($exchange_rates)) {
            // Fallback exchange rates if WPMC is not available
            $fallback_rates = [
                'USD' => 0.92,   // 1 USD = 0.92 EUR
                'GBP' => 1.18,   // 1 GBP = 1.18 EUR
                'SEK' => 0.086,  // 1 SEK = 0.086 EUR (approx. 11.6 SEK = 1 EUR)
                'NOK' => 0.085,  // 1 NOK = 0.085 EUR
                'DKK' => 0.134,  // 1 DKK = 0.134 EUR
                'JPY' => 0.006,  // 1 JPY = 0.006 EUR
                'INR' => 0.011,  // 1 INR = 0.011 EUR
            ];
            
            if ($from_currency === 'EUR') {
                // EUR to other currency
                return 1 / ($fallback_rates[$to_currency] ?? 1);
            } elseif ($to_currency === 'EUR') {
                // Other currency to EUR
                return $fallback_rates[$from_currency] ?? 1;
            } else {
                // Cross-currency conversion (via EUR)
                $from_rate = $fallback_rates[$from_currency] ?? 1;
                $to_rate = $fallback_rates[$to_currency] ?? 1;
                return $to_rate / $from_rate;
            }
        }
        
        if ($from_currency === 'EUR') {
            // EUR to other currency
            return 1 / ($exchange_rates[$to_currency] ?? 1);
        } elseif ($to_currency === 'EUR') {
            // Other currency to EUR
            return 1 / ($exchange_rates[$from_currency] ?? 1);
        } else {
            // Cross-currency conversion (via EUR)
            $from_rate = $exchange_rates[$from_currency] ?? 1;
            $to_rate = $exchange_rates[$to_currency] ?? 1;
            
            if ($from_rate > 0 && $to_rate > 0) {
                return $to_rate / $from_rate;
            }
            
            return 1;
        }
    }
    
    /**
     * Check for excessive rate fluctuations and apply fallback if needed
     * 
     * @param array $current_rates Current exchange rates
     * @return array Validated exchange rates
     */
    private function checkRateFluctuations(array $current_rates): array
    {
        if (empty($current_rates)) {
            return $current_rates;
        }
        
        // Get previous rates from database
        $previous_rates = get_option('co2market_previous_exchange_rates', []);
        $last_update = get_option('co2market_exchange_rates_last_update', 0);
        $now = time();
        
        // Store current rates for future comparison
        update_option('co2market_previous_exchange_rates', $current_rates);
        update_option('co2market_exchange_rates_last_update', $now);
        
        // If no previous rates or it's been more than a day, just use current rates
        if (empty($previous_rates) || ($now - $last_update) > (24 * 60 * 60)) {
            return $current_rates;
        }
        
        $validated_rates = [];
        
        // Check each rate for excessive fluctuation (more than 10%)
        foreach ($current_rates as $currency => $rate) {
            if (!isset($previous_rates[$currency]) || $previous_rates[$currency] <= 0) {
                $validated_rates[$currency] = $rate;
                continue;
            }
            
            $previous_rate = $previous_rates[$currency];
            $fluctuation = abs(($rate - $previous_rate) / $previous_rate);
            
            // If fluctuation is more than 10%, use the previous rate
            if ($fluctuation > 0.10) {
                $validated_rates[$currency] = $previous_rate;
                
                // Log this anomaly
                error_log(sprintf(
                    'Excessive exchange rate fluctuation detected for %s: Previous rate: %f, Current rate: %f, Fluctuation: %f%%',
                    $currency,
                    $previous_rate,
                    $rate,
                    $fluctuation * 100
                ));
            } else {
                $validated_rates[$currency] = $rate;
            }
        }
        
        return $validated_rates;
    }
    
    /**
     * Get exchange rates from WPMC plugin
     * 
     * @return array Exchange rates with currency codes as keys and rates as values
     */
    private function getWPMCExchangeRates(): array
    {
        // Try the wcml_exchange_rates filter (WPML WooCommerce Multilingual)
        $exchange_rates = apply_filters('wcml_exchange_rates', []);
        
        if (!empty($exchange_rates)) {
            return $exchange_rates;
        }
        
        // Try other possible filters for different multi-currency plugins
        try {
            // WooCommerce Multi-Currency
            if (function_exists('wmc_get_exchange_rates')) {
                $rates = call_user_func('wmc_get_exchange_rates');
                if (is_array($rates) && !empty($rates)) {
                    return $rates;
                }
            }
            
            // CURCY - Multi Currency for WooCommerce
            if (class_exists('WOOMULTI_CURRENCY_Data')) {
                $class_name = 'WOOMULTI_CURRENCY_Data';
                $method_name = 'get_ins';
                
                if (method_exists($class_name, $method_name)) {
                    $wmc_settings = call_user_func([$class_name, $method_name]);
                    
                    if ($wmc_settings && method_exists($wmc_settings, 'get_list_currencies')) {
                        $currencies = $wmc_settings->get_list_currencies();
                        $rates = [];
                        
                        if (is_array($currencies)) {
                            foreach ($currencies as $code => $currency) {
                                if (isset($currency['rate'])) {
                                    $rates[$code] = $currency['rate'];
                                }
                            }
                            
                            if (!empty($rates)) {
                                return $rates;
                            }
                        }
                    }
                }
            }
            
            // WP Currencies plugin
            if (function_exists('wpc_get_currencies')) {
                $currencies = call_user_func('wpc_get_currencies');
                $rates = [];
                
                if (is_array($currencies)) {
                    foreach ($currencies as $code => $currency) {
                        if (isset($currency['rate'])) {
                            $rates[$code] = $currency['rate'];
                        }
                    }
                    
                    if (!empty($rates)) {
                        return $rates;
                    }
                }
            }
        } catch (\Exception $e) {
            // Log the error if needed
            // error_log('Error fetching exchange rates: ' . $e->getMessage());
        }
        
        return [];
    }

    /**
     * Format a currency amount with the currency code
     * 
     * @param float $amount The amount to format
     * @param string $currency_code The currency code
     * @return string Formatted currency amount with currency code
     */
    public function formatCurrency(float $amount, string $currency_code): string
    {
        // Format with appropriate decimals based on currency
        $decimals = 2;
        if ($currency_code === 'JPY') {
            $decimals = 0;
        }
        
        $formatted_amount = number_format($amount, $decimals, '.', ',');
        
        // Return with currency code
        return $formatted_amount . ' ' . $currency_code;
    }
    
    /**
     * Static helper method to format currency
     * 
     * @param float $amount The amount to format
     * @param string $currency_code The currency code
     * @return string Formatted currency amount with symbol
     */
    public static function format(float $amount, string $currency_code): string
    {
        $service = new self();
        return $service->formatCurrency($amount, $currency_code);
    }

    /**
     * Convert an amount from one currency to another
     * 
     * This function first converts the amount from the source currency to EUR,
     * then from EUR to the target currency. This approach is used because
     * most free currency APIs only provide rates relative to a base currency (EUR).
     * 
     * @param float $amount The amount to convert
     * @param string $from_currency The source currency code
     * @param string $to_currency The target currency code
     * @param int $decimals Number of decimal places for the result (default: 2)
     * @return float The converted amount
     */
    public function convertCurrency(float $amount, string $from_currency, string $to_currency, int $decimals = 2): float
    {
        // If source and target currencies are the same, no conversion needed
        if ($from_currency === $to_currency) {
            return round($amount, $decimals);
        }
        
        // If source currency is EUR, convert directly to target currency
        if ($from_currency === 'EUR') {
            $rate = $this->getExchangeRate('EUR', $to_currency);
            return round($amount * $rate, $decimals);
        }
        
        // If target currency is EUR, convert directly from source currency
        if ($to_currency === 'EUR') {
            $rate = $this->getExchangeRate($from_currency, 'EUR');
            return round($amount * $rate, $decimals);
        }
        
        // For cross-currency conversion, go through EUR
        // 1. Convert from source currency to EUR
        $eur_amount = $amount * $this->getExchangeRate($from_currency, 'EUR');
        
        // 2. Convert from EUR to target currency
        $target_amount = $eur_amount * $this->getExchangeRate('EUR', $to_currency);
        
        return round($target_amount, $decimals);
    }

    /**
     * Static helper method to convert currency
     * 
     * Provides an easy way to convert currencies from anywhere in the application
     * without having to instantiate the CurrencyService
     * 
     * @param float $amount The amount to convert
     * @param string $from_currency The source currency code
     * @param string $to_currency The target currency code
     * @param int $decimals Number of decimal places for the result (default: 2)
     * @return float The converted amount
     */
    public static function convert(float $amount, string $from_currency, string $to_currency, int $decimals = 2): float
    {
        $service = new self();
        return $service->convertCurrency($amount, $from_currency, $to_currency, $decimals);
    }
} 