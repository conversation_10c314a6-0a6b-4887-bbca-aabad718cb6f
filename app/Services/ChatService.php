<?php

namespace App\Services;

use WP_Error;
use \Illuminate\Support\Facades\Log;

class ChatService
{
    public $base_url;
    private $auth_token = "wcfk0yLAxb0Zk10ojhUPFmq9O1nXBFNJ-R_tOZoI7yd";
    private $user_id = "DkPSS4KgT3z3Q73nu";

    public function __construct()
    {
        $this->base_url = 'https://chat.onyxsolutions.fi/api/v1';
        // Constructor can be empty or used for future initialization if necessary
    }

    // Function to perform a cURL request
    private function curl_request($url, $data, $method = 'POST')
    {
        $response = wp_remote_post($url, array(
            'headers' => array(
                'X-Auth-Token' => $this->auth_token,
                'X-User-Id' => $this->user_id,
                'Content-Type' => 'application/json'
            ),
            'body' => $data,
            'method' => $method,
            'data_format' => 'body'
        ));

        if (is_wp_error($response)) {
            error_log('WP Error in curl_request: ' . $response->get_error_message());
            return $response;
        }

        $body = wp_remote_retrieve_body($response);
        return json_decode($body, true);
    }

    private function curl_user_request($url, $data, $method = 'GET')
    {
        $user_token = $this->get_user_auth_token();
        if (is_wp_error($user_token)) {
            return $user_token;
        }
        $response = wp_remote_post($url, array(
            'headers' => array(
                'X-Auth-Token' => $user_token['authToken'],
                'X-User-Id' => $user_token['userId'],
                'Content-Type' => 'application/json'
            ),
            'body' => $data,
            'method' => $method,
            'data_format' => 'body'
        ));

        if (is_wp_error($response)) {
            error_log('WP Error in curl_request: ' . $response->get_error_message());
            return $response;
        }

        $body = wp_remote_retrieve_body($response);
        return json_decode($body, true);
    }

    // Function to generate user authentication token
    public function get_user_auth_token()
    {
        $current_user = wp_get_current_user();
        if (!$current_user->exists()) {
            return new WP_Error('rest_not_logged_in', 'You are not logged in.', array('status' => 401));
        }

        $token_url =  $this->base_url . '/users.createToken';
        $token_data = json_encode(array('username' => $current_user->user_login));
        $token_response = $this->curl_request($token_url, $token_data);


        if (isset($token_response['success']) && $token_response['success']) {
            return $token_response['data']; // Returning userId and authToken
        } elseif (isset($token_response['errorType']) && $token_response['errorType'] == 'error-invalid-user') {
            // Create a new user in Rocket.Chat
            $create_user_url = $this->base_url . '/users.create';
            $create_user_data = json_encode(array(
                'name' => $current_user->display_name,
                'email' => $current_user->user_email,
                'username' => $current_user->user_login,
                'password' => wp_generate_password(),
                'roles' => array('user')
            ));
            $create_user_response = $this->curl_request($create_user_url, $create_user_data);

            if (isset($create_user_response['success']) && $create_user_response['success']) {
                // After successful user creation, try getting the token again
                $new_token_response = $this->curl_request($token_url, $token_data);
                if (isset($new_token_response['success']) && $new_token_response['success']) {
                    return $new_token_response['data']; // Returning new userId and authToken
                }
            }
        }

        Log::error("Error in get_user_auth_token: " . print_r($token_response, true));
        return null;
    }

    // Add custom capabilities
    public function add_custom_capabilities()
    {
        $roles = array('compensation_project_owner', 'consultant');

        foreach ($roles as $role_name) {
            $role = get_role($role_name);
            if ($role) {
                $role->add_cap('view_chat_page', true);
            }
        }
    }

    // Add Chat menu
    public function add_chat_menu()
    {
        if (current_user_can('manage_options') || current_user_can('view_chat_page')) {
            add_menu_page(
                'Chat', // Page title
                'Chat', // Menu title
                'read', // Capability
                'chat-menu-page', // Menu slug
                [$this, 'chat_submenu_page_content'], // Function
                'dashicons-format-chat', // Icon URL (Dashicon)
                6 // Position
            );
        }
    }

    // Callback function for the submenu page content
    public function chat_submenu_page_content()
    {
        $auth_result = $this->get_user_auth_token();

        if (is_wp_error($auth_result)) {
            echo '<p>Error obtaining authentication token: ' . esc_html($auth_result->get_error_message()) . '</p>';
            return;
        }

        $authToken = isset($auth_result['authToken']) ? $auth_result['authToken'] : '';

        $chat_id = isset($_GET['offer-chat']) ? sanitize_text_field($_GET['offer-chat']) : '';
        if (!empty($chat_id)) {
            $form_fields_data = get_post_meta($chat_id, 'form_fields_data', true);
            $current_user = wp_get_current_user();
            $current_user_login = $current_user->user_login;
            $user_who_submittedId = $form_fields_data['submitted_by'];
            $user_who_submitted = get_user_by('id', $user_who_submittedId)->user_login;
            $members = array($current_user_login, $user_who_submitted);

            $chatName = "Offer" . $chat_id . "-" .  $current_user -> ID . "-" . $user_who_submittedId;
            $this->check_chat_and_create($chatName, $members);

            $chat_url = "https://chat.onyxsolutions.fi/group/" . urlencode($chatName);

            echo '<iframe id="rocketchat-iframe" 
                src="' . esc_url($chat_url . '?resumeToken=' . $authToken . "&layout=embedded") . '"
                width="100%" height="700px" frameborder="0" 
                sandbox="allow-same-origin allow-scripts allow-popups allow-forms">
              </iframe>';

            return;
        }

        ?>
    <style>
        html, body {
            margin: 0;
            padding: 0;
            height: 100%;
        }

        #wpcontent, #wpbody, #wpbody-content {
            padding-left: 0 !important;
        }

        #rocketchat-iframe {
            width: 100%;
            height: 100vh;
            border: none;
            box-sizing: border-box;
        }
    </style>

    <iframe id="rocketchat-iframe" 
            src="https://chat.onyxsolutions.fi/home?resumeToken=<?php echo esc_attr($authToken); ?>"
            sandbox="allow-same-origin allow-scripts allow-popups allow-forms">
    </iframe>

    <script>
        function adjustIframeHeight() {
            var iframe = document.getElementById('rocketchat-iframe');
            var adminBarHeight = document.getElementById('wpadminbar') ? document.getElementById('wpadminbar').offsetHeight : 0;
            var viewportHeight = document.documentElement.clientHeight;
            var iframeHeight = viewportHeight - adminBarHeight;
            iframe.style.height = iframeHeight + 'px';
        }

        window.onload = adjustIframeHeight;
        window.onresize = adjustIframeHeight;
    </script>
    <?php
    }

    public function create_offer_chat($offer, $user_ids)
    {
        $offer_id = $offer["id"];
        $chatName = "Offer" . $offer_id . "-" . $user_ids[0] . "-" . $user_ids[1];
        $user_names = array_map(function ($user_id) {
            $user = get_user_by('id', $user_id);
            return $user->user_login;
        }, $user_ids);
        $this->check_chat_and_create($chatName, $user_names);
        return $chatName;
    }

    public function create_rocket_chat_group_after_order($order_id)
    {
        if (!$order_id) {
            return;
        }

        $order = wc_get_order($order_id);
        if (!$order) {
            return;
        }

        $customer_username = $order->get_user()->user_login;
        $this->check_user($customer_username);

        foreach ($order->get_items() as $item_id => $item) {
            $product_id = $item->get_data()["product_id"];
            $contact_persons = get_field('contact_persons', $product_id);

            if (!empty($contact_persons)) {
                $usernames = array_map(function ($user) {
                    $this->check_user($user['nickname']);
                    return $user['nickname'];
                }, $contact_persons);

                array_push($usernames, $customer_username);
                $this->create_rocket_chat_group("Order$order_id", $usernames);
            }
        }
    }

    private function create_rocket_chat_group($group_name, $members)
    {
        $url = $this->base_url . '/groups.create';

        $data = json_encode(array(
            'name' => $group_name,
            'excludeSelf' => true,
            'members' => $members
        ));

        $response = wp_remote_post($url, array(
            'headers' => array(
                'X-Auth-Token' => get_field('auth_token', 'option'),
                'X-User-Id' => get_field('rocket_chat_user_id', 'option'),
                'Content-Type' => 'application/json'
            ),
            'body' => $data,
            'method' => 'POST',
            'data_format' => 'body'
        ));

        return $response;
    }

    private function check_user($username)
    {
        $user = get_user_by('login', $username);

        if (!$user) {
            echo 'WordPress user not found for username: ' . $username;
            return false;
        }

        $url = $this->base_url . '/users.info?username=' . urlencode($username);

        $response = wp_remote_get($url, array(
            'headers' => array(
                'X-Auth-Token' => get_field('auth_token', 'option'),
                'X-User-Id' => get_field('rocket_chat_user_id', 'option'),
                'Content-Type' => 'application/json'
            )
        ));

        if (is_wp_error($response)) {
            echo 'Error checking Rocket.Chat user: ' . $response->get_error_message();
            return false;
        }

        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        if (isset($data['success']) && !$data['success'] && isset($data['error']) && $data['error'] === "User not found.") {
            return $this->create_rocket_chat_user($user->user_login, $user->display_name, $user->user_email);
        }

        return true;
    }

    private function create_rocket_chat_user($username, $displayName, $email)
    {
        $create_user_url = $this->base_url . '/users.create';
        $create_user_data = json_encode(array(
            'name' => $displayName,
            'email' => $email,
            'username' => $username,
            'password' => wp_generate_password(),
            'roles' => array('user')
        ));

        $response = wp_remote_post($create_user_url, array(
            'headers' => array(
                'X-Auth-Token' => get_field('auth_token', 'option'),
                'X-User-Id' => get_field('rocket_chat_user_id', 'option'),
                'Content-Type' => 'application/json'
            ),
            'body' => $create_user_data,
            'method' => 'POST',
            'data_format' => 'body'
        ));

        if (is_wp_error($response)) {
            echo 'Error creating user: ' . $response->get_error_message();
            return false;
        }

        $body = wp_remote_retrieve_body($response);
        echo 'User creation response: ' . $body;
        return true;
    }

    private function check_if_chat_exist($chat_name)
    {
        $url = $this->base_url . '/groups.info?roomName=' . urlencode($chat_name);

        $response = wp_remote_get($url, array(
            'headers' => array(
                'X-Auth-Token' => get_field('auth_token', 'option'),
                'X-User-Id' => get_field('rocket_chat_user_id', 'option'),
                'Content-Type' => 'application/json'
            )
        ));

        if (is_wp_error($response)) {
            echo 'Error checking Rocket.Chat group: ' . $response->get_error_message();
            return false;
        }

        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        if (isset($data['success']) && !$data['success'] && isset($data['error'])) {
            return false;
        }

        return true;
    }

    private function check_chat_and_create($chat_name, $members)
    {
        if (!$this->check_if_chat_exist($chat_name)) {
            $this->create_rocket_chat_group($chat_name, $members);
        }
    }

    /**
     * Fetch new messages for the current user in group/private chats.
     *
     * @param int $limit Number of chats to retrieve (default 5).
     * @return array|WP_Error Array of chats with message previews and new message counts or WP_Error on failure.
     */
    public function get_new_messages_for_user($limit = 5)
    {
        $current_user = wp_get_current_user();
        if (!$current_user->exists()) {
            return new WP_Error('rest_not_logged_in', 'You are not logged in.', array('status' => 401));
        }

        // Step 1: Get the user's joined groups and private chats
        $chats_url = $this->base_url . '/subscriptions.get';
        $chats_response = $this->curl_user_request($chats_url, null, 'GET');

        if (is_wp_error($chats_response) || !isset($chats_response['success']) || !$chats_response['success']) {
            return new WP_Error('rest_chat_fetch_failed', 'Failed to fetch user chats.', array(
                'status' => 500,
                'response' => $chats_response,
                'url' => $chats_url,
                'user' => get_field('auth_token', 'option'),
                'user_id' => get_field('rocket_chat_user_id', 'option')
            ));
        }

        // Step 2: Extract chats from the response
        $chats = isset($chats_response['update']) ? $chats_response['update'] : array();

        if (empty($chats)) {
            return array(); // Return empty array if no chats found
        }

        // Step 3: Sort chats by the most recent timestamp ('ts' or 'ls')
        usort($chats, function ($a, $b) {
            // Determine timestamp for chat A
            if (isset($a['ts']) && !empty($a['ts'])) {
                $a_ts = strtotime($a['ts']);
            } elseif (isset($a['ls']) && !empty($a['ls'])) {
                $a_ts = strtotime($a['ls']);
            } else {
                $a_ts = 0; // Default to epoch if no timestamp available
            }

            // Determine timestamp for chat B
            if (isset($b['ts']) && !empty($b['ts'])) {
                $b_ts = strtotime($b['ts']);
            } elseif (isset($b['ls']) && !empty($b['ls'])) {
                $b_ts = strtotime($b['ls']);
            } else {
                $b_ts = 0; // Default to epoch if no timestamp available
            }

            // Compare timestamps for descending order
            return $b_ts - $a_ts;
        });

        // Step 4: Limit the result to the newest $limit chats
        $limited_chats = array_slice($chats, 0, $limit);

        // Step 5: Extract only 'name' and 'unread' fields
        $result = array();
        foreach ($limited_chats as $chat) {
            // Determine the chat name
            if (isset($chat['fname']) && !empty($chat['fname'])) {
                $name = $chat['fname'];
            } elseif (isset($chat['name']) && !empty($chat['name'])) {
                $name = $chat['name'];
            } else {
                $name = 'Unnamed Chat'; // Fallback name
            }

            $user = $chat['u'];
            $user_name = isset($user['username']) ? $user['username'] : null;

            // Determine the unread count
            $unread = isset($chat['unread']) ? intval($chat['unread']) : 0;

            $timestamp = isset($chat['ts']) ? strtotime($chat['ts']) : null;



            $result[] = array(
                'name' => $name,
                'unread' => $unread,
                'user_name' => $user_name,
                'timestamp' => $timestamp
            );
        }

        return $result;
    }

}
