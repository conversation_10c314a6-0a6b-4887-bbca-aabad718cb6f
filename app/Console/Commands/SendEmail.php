<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;

class SendEmail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:send-email email';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send a test email to the specified email address';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');
        
        $this->info("Sending email to: {$email}");

        Mail::to($email)->send(new \App\Mail\TestMail([
            'title' => 'Test Email',
            'content' => 'This is a test email sent from the console command.'
        ]));

        $this->info('Email sent successfully!');
    }
}
