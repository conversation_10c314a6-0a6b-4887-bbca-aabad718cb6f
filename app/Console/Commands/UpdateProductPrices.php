<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\CurrencyService;

class UpdateProductPrices extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'products:update-prices';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update all product prices based on native currency exchange rates';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Starting product price update...');

        try {
            $currencyService = new CurrencyService();
            $stats = $currencyService->updateAllProductPrices();

            // Output statistics
            $this->info('Price update completed successfully!');
            $this->table(
                ['Metric', 'Value'],
                [
                    ['Total Products', $stats['total']],
                    ['Updated Products', $stats['updated']],
                    ['EUR Products Updated', $stats['eur_updated']],
                    ['Currency Changed', $stats['currency_changed']],
                    ['Skipped Products', $stats['skipped']],
                    ['Errors', $stats['errors']],
                ]
            );

            // If there are protected rates due to fluctuations, show them
            if (!empty($stats['protected_rates'])) {
                $this->warn('Some currencies had significant exchange rate fluctuations:');
                
                $rateData = [];
                foreach ($stats['protected_rates'] as $currency => $data) {
                    $rateData[] = [
                        $currency,
                        $data['previous_rate'],
                        $data['current_rate'],
                        $data['fluctuation_percent'] . '%'
                    ];
                }
                
                $this->table(
                    ['Currency', 'Previous Rate', 'Current Rate', 'Fluctuation'],
                    $rateData
                );
            }

            // Log the update to WordPress options
            update_option('co2market_last_scheduled_update', current_time('mysql'));
            update_option('co2market_last_scheduled_update_stats', $stats);

            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error('Error updating product prices: ' . $e->getMessage());
            
            // Log the error to WordPress options
            update_option('co2market_last_scheduled_update_error', [
                'message' => $e->getMessage(),
                'time' => current_time('mysql')
            ]);
            
            return Command::FAILURE;
        }
    }
} 