<?php

/**
 * Theme setup.
 */

namespace App;

use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Blade;
use App\Wordpress\ProductPriceHooks;

// Autoload all PostTypes
$postTypesDir = __DIR__ . '/PostTypes';
if (is_dir($postTypesDir)) {
    foreach (scandir($postTypesDir) as $file) {
        if (pathinfo($file, PATHINFO_EXTENSION) === 'php') {
            require_once $postTypesDir . '/' . $file;
        }
    }
}

// Initialize the ProductPriceHooks class
add_action('init', function() {
    new ProductPriceHooks();
}, 20, 0);

// Initialize WordPress-specific functionality
require_once __DIR__ . '/Wordpress/price-updater.php';

/**
 * Inject the Vite assets into the head.
 *
 * @return void
 */
add_action('wp_head', function () {
    echo Str::wrap(app('assets.vite')([
        'resources/js/app.tsx'
    ]), "\n");
}, 10, 0);

/**
 * Inject assets into the block editor.
 *
 * @return void
 */
add_action('admin_head', function () {
    $screen = get_current_screen();


    if (! $screen?->is_block_editor()) {
        return;
    }

    $dependencies = File::json(public_path('build/editor.deps.json')) ?? [];

    foreach ($dependencies as $dependency) {
        if (! wp_script_is($dependency)) {
            wp_enqueue_script($dependency);
        }
    }

    echo Str::wrap(app('assets.vite')([
        'resources/js/editor.js',
    ]), "\n");
}, 10, 0);

/**
 * Use theme.json from the build directory
 *
 * @param  string  $path
 * @param  string  $file
 * @return string
 */
add_filter('theme_file_path', function (string $path, string $file): string {
    if ($file === 'theme.json') {
        return public_path() . '/build/assets/theme.json';
    }

    return $path;
}, 10, 2);

/**
 * Register the initial theme setup.
 *
 * @return void
 */
add_action('after_setup_theme', function () {
    
    /**
     * Disable full-site editing support.
     *
     * @link https://wptavern.com/gutenberg-10-5-embeds-pdfs-adds-verse-block-color-options-and-introduces-new-patterns
     */
    remove_theme_support('block-templates');

    /**
     * Register the navigation menus.
     *
     * @link https://developer.wordpress.org/reference/functions/register_nav_menus/
     */
    register_nav_menus([
        'primary_navigation' => __('Primary Navigation', 'sage'),
    ]);

    /**
     * Disable the default block patterns.
     *
     * @link https://developer.wordpress.org/block-editor/developers/themes/theme-support/#disabling-the-default-block-patterns
     */
    remove_theme_support('core-block-patterns');

    /**
     * Enable plugins to manage the document title.
     *
     * @link https://developer.wordpress.org/reference/functions/add_theme_support/#title-tag
     */
    add_theme_support('title-tag');

    /**
     * Enable post thumbnail support.
     *
     * @link https://developer.wordpress.org/themes/functionality/featured-images-post-thumbnails/
     */
    add_theme_support('post-thumbnails');

    /**
     * Enable responsive embed support.
     *
     * @link https://developer.wordpress.org/block-editor/how-to-guides/themes/theme-support/#responsive-embedded-content
     */
    add_theme_support('responsive-embeds');

    /**
     * Enable HTML5 markup support.
     *
     * @link https://developer.wordpress.org/reference/functions/add_theme_support/#html5
     */
    add_theme_support('html5', [
        'caption',
        'comment-form',
        'comment-list',
        'gallery',
        'search-form',
        'script',
        'style',
    ]);

    /**
     * Enable selective refresh for widgets in customizer.
     *
     * @link https://developer.wordpress.org/reference/functions/add_theme_support/#customize-selective-refresh-widgets
     */
    add_theme_support('customize-selective-refresh-widgets');
}, 20, 0);

/**
 * Register the theme sidebars.
 *
 * @return void
 */
add_action('widgets_init', function () {
    $config = [
        'before_widget' => '<section class="widget %1$s %2$s">',
        'after_widget' => '</section>',
        'before_title' => '<h3>',
        'after_title' => '</h3>',
    ];

    register_sidebar([
        'name' => __('Primary', 'sage'),
        'id' => 'sidebar-primary',
    ] + $config);

    register_sidebar([
        'name' => __('Footer', 'sage'),
        'id' => 'sidebar-footer',
    ] + $config);
}, 10, 0);

/**
 * Enqueue scripts for vendor details form
 */
add_action('wp_enqueue_scripts', function () {
    // Only enqueue on the vendor details page
    if (is_page_template('views/pages/additional-vendor-details.blade.php')) {
        // Country select library
        wp_enqueue_script('country-select-js', 'https://cdnjs.cloudflare.com/ajax/libs/country-select-js/2.1.0/js/countrySelect.min.js', ['jquery'], null, true);
        wp_enqueue_style('country-select-css', 'https://cdnjs.cloudflare.com/ajax/libs/country-select-js/2.1.0/css/countrySelect.min.css');
        
        // Signature pad library
        wp_enqueue_script('signature-pad-js', 'https://cdn.jsdelivr.net/npm/signature_pad@2.3.2/dist/signature_pad.min.js', [], null, true);
    }
}, 10, 0);

/**
 * Load custom initialization files
 */

// Laravel scheduler is now used for price updates
// The schedule is defined in app/Console/Kernel.php
// To run the scheduler, set up a system cron job:
// * * * * * cd /path/to/your/theme && wp acorn schedule:run >> /dev/null 2>&1
