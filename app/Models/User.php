<?php

namespace App\Models;

use Exception;
use WP_Query;
use App\Enums\Role;
use WP_Post;

class User
{
    public int $id;
    public string $name;
    public string $first_name;
    public string $last_name;
    public string $email;
    public string $phone;
    public string $company;
    public string|false $avatar;
    /** @var (Role|null)[] */
    public array $roles;
    public Organization|null $organization;

    public static function getCurrentUser(): User|null
    {
        $user_id = get_current_user_id();
        if ($user_id === 0) {
            return null;
        }
        return new User($user_id);
    }

    public function __construct(int|null $user_id)
    {
        if ($user_id === null) {
            $user_id = get_current_user_id();
        }
        if ($user_id === 0) {
            throw new Exception('User not found');
        }

        $user = get_user_by('id', $user_id);
        if (!$user) {
            throw new Exception('User not found');
        }

        $this->id = $user->ID;
        $this->name = $user->display_name;
        $this->first_name = $user->first_name;
        $this->last_name = $user->last_name;
        $this->email = $user->user_email;
        $this->phone = get_user_meta($user->ID, 'phone', true) ?: '';
        $this->company = get_user_meta($user->ID, 'company', true) ?: '';
        $this->avatar = get_avatar_url($user->ID);
        $this->roles = array_map(
            fn(string $role) => Role::fromString($role),
            $user->roles
        );

        $this->organization = Organization::getUserOrganization($user_id);
    }

    public function updateDetails(array $data): array
    {
        $userdata = ['ID' => $this->id];

        if (isset($data['firstName'])) {
            $userdata['first_name'] = sanitize_text_field($data['firstName']);
        }
        if (isset($data['last_name'])) {
            $userdata['last_name'] = sanitize_text_field($data['lastName']);
        }
        if (isset($data['email'])) {
            $userdata['user_email'] = sanitize_email($data['email']);
        }

        $user_id = wp_update_user($userdata);
        if (is_wp_error($user_id)) {
            throw new Exception($user_id->get_error_message());
        }

        if (isset($data['phone'])) {
            update_user_meta($this->id, 'phone', sanitize_text_field($data['phone']));
        }
        if (isset($data['company'])) {
            update_user_meta($this->id, 'company', sanitize_text_field($data['company']));
        }

        return $this->toArray();
    }

    public function getOrders(int $page = 1, int $limit = 10): array
    {

        if (!function_exists('\wc_get_orders')) {
            throw new Exception('WooCommerce is not active');
        }

        $orders = wc_get_orders([
            'customer_id' => $this->id,
            'page' => $page,
            'paginate' => true,
            'limit' => $limit,
        ]);

        $total = is_object($orders) ? $orders->total : count($orders);
        $max_num_pages = is_object($orders) ? $orders->max_num_pages : 1;

        $result = [
            'total' => $total,
            'total_pages' => $max_num_pages,
            'orders' => [],
        ];

        foreach ((is_object($orders) ? $orders->orders : $orders) as $order) {
            $result['orders'][] = (new Order($order->get_id()))->toArray();
        }

        return $result;
    }

    public function getOwnedProducts(int $page = 1, int $limit = 10): array
    {
        $args = [
            'post_type' => 'product',
            'author' => $this->id,
            'posts_per_page' => $limit,
            'paged' => $page,
            'post_status' => 'publish', // Only show published products, exclude archived/draft
        ];

        $query = new WP_Query($args);
        $products = [];

        while ($query->have_posts()) {
            $query->the_post();
            $id = get_the_ID();
            if ($id == false) {
                continue;
            }
            $products[] = (new Product($id))->to_array();
        }

        wp_reset_postdata();

        return [
            'total' => $query->found_posts,
            'total_pages' => $query->max_num_pages,
            'products' => $products,
        ];
    }

    /**
     * Get all compensation processes created by the user
     * @return array<OffsetProcess>
     */
    static function getProcesses(): array
    {
        $id = get_current_user_id();
        $args = [
            'post_type' => OffsetProcess::$post_type,
            'meta_query' => array(
                array(
                    'key' => 'user',
                    'value' => $id,
                    'compare' => '='
                )
            ),
            'posts_per_page' => -1,
        ];

        $posts = get_posts($args);
        $processes = [];

        foreach ($posts as $post) {
            try {
                $post_id = $post instanceof WP_Post ? $post->ID : $post;
                $processes[] = new OffsetProcess($post_id);
            } catch (\Throwable $th) {
                continue;
            }
        }
        wp_reset_postdata();
        return $processes;
    }

    public static function getSelectedProcessId()
    {
        $selectedProcessId = isset($_COOKIE['selected_process_id']) ? $_COOKIE['selected_process_id'] : null;
        return $selectedProcessId;
    }

    public static function getSelectedProcess()
    {
        $id = self::getSelectedProcessId();
        return new OffsetProcess($id);
    }

    public static function get(?int $user_id): User|null
    {
        if ($user_id === null) {
            return null;
        }
        try {
            return new User($user_id);
        } catch (\Throwable $th) {
            return null;
        }
    }

    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'first_name' => $this->first_name,
            'last_name' => $this->last_name,
            'email' => $this->email,
            'phone' => $this->phone,
            'company' => $this->company,
            'avatar' => $this->avatar,
            'roles' => array_map(
                fn(Role $role) => $role->toArray(),
                array_filter($this->roles, fn($role) => $role !== null)
            ),
            'organization' => $this->organization ? $this->organization->toArray() : null,
        ];
    }
    public function toJson(): string|false
    {
        return json_encode($this->toArray());
    }
}
