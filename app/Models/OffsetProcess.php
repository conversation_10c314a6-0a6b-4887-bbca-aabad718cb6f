<?php

namespace App\Models;

use Exception;
use App\Enums\CalculationType;
use App\Enums\OffsetProcessStep;

class OffsetProcess
{
    public static string $post_type = 'offset-process';

    public int $id;
    public string $name;
    public array $orders;
    public array $communicate_files;
    public OffsetProcessStep $step;
    public ?int $organization_id;
    public int $user_id;
    public ?int $verified_by_user_id;
    public ?int $creator_id;

    // Calculation properties
    public array $ghg_scope;
    public array $emissions;
    public CalculationType $type;

    public array $files;

    public function __construct(int $process_id)
    {
        $post = get_post($process_id);
        if (!$post || $post->post_type !== OffsetProcess::$post_type) {
            throw new Exception('Offset process not found');
        }

        $this->id = $process_id;
        $this->name = $post->post_title;

        $fields = get_fields($process_id);

        $this->step = OffsetProcessStep::fromString($fields["step"] ?? "Calculate") ?? OffsetProcessStep::CALCULATE;
        $this->type = CalculationType::fromString($fields['calculation_type'] ?? 'unapproved') ?? CalculationType::UNAPPROVED;
        $this->creator_id = $post->post_author;

        $order_ids = $fields['orders'] ?: [];
        $this->orders = $this->getOrders(is_array($order_ids) ? $order_ids : []);
        $this->communicate_files = $fields['communicate_files'] ?: [];

        $this->organization_id = $fields['organization_id'] ?: null;
        $this->user_id = $fields['user'];


        $this->ghg_scope = $fields['ghg_scope'] ?: [];
        $this->emissions = $fields['emissions'] ?: [];
        $this->verified_by_user_id = $fields['verified_by'] ?: null;
        $this->files = $fields['files'] ?: [];
    }

    public function getOrganization(): Organization|null
    {
        if ($this->organization_id == null) {
            return null;
        }
        return new Organization($this->organization_id);
    }

    private function getOrders(?array $order_ids = []): array
    {
        if (!is_array($order_ids) || empty($order_ids)) {
            return [];
        }
        $orders = [];
        foreach ($order_ids as $order_id) {
            try {
                $orders[] = new Order($order_id);
            } catch (\Throwable $th) {
                continue;
            }
        }
        return $orders;
    }

    /**
     *  Adds order to acf field orders
     */
    public function addOrder(int $id)
    {
        $field = "orders";
        $orders = get_field($field, $this->id);
        if (!is_array($orders)) {
            $orders = [];
        }
        $orders[] = [
            'id' => (string) $id
        ];

        // Update the field
        update_field($field, $orders, $this->id);
    }

    public function getOffer(int $offer_id): ?Offer
    {
        return Offer::get($offer_id);
    }

    public function getCreator(): User|null
    {
        return User::get($this->creator_id);
    }

    public function getUser(): User|null
    {
        return User::get($this->user_id);
    }

    public function calculateTotalEmissions(): float
    {
        return array_reduce(
            $this->emissions,
            fn(float $total, $emission) => $total + floatval($emission['value'] ?? 0),
            0.0
        );
    }

    public static function get(int $process_id): OffsetProcess|null
    {
        try {
            return new OffsetProcess($process_id);
        } catch (\Throwable $th) {
            return null;
        }
    }

    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'orders' => $this->orders,
            'communicate_filed' => $this->communicate_files,
            'step' => $this->step->toArray(),
            'ghg_scope' => $this->ghg_scope,
            'emissions' => $this->emissions,
            'verified_by' => $this->getCreator()?->toArray(),
            'user' => $this->getUser()?->toArray(),
            'status' => $this->type->toString(),
            'total_emissions' => $this->calculateTotalEmissions(),
        ];
    }

    public function toJson(): string|false
    {
        return json_encode($this->toArray());
    }

    public static function create(int $process_id, string $name, int $for_user, array $data = []): OffsetProcess|null
    {
        $post = [
            'post_title' => $name,
            'post_type' => OffsetProcess::$post_type,
            'post_status' => 'publish',
            'post_author' => get_current_user_id(),
        ];
        $process_id = wp_insert_post($post);
        if ($process_id === 0 || is_wp_error($process_id)) {
            throw new Exception('Failed to create offset process');
        }

        // Set ACF meta field 'user' to $for_user
        update_field('user', $for_user, $process_id);

        // Set additional ACF fields if provided
        if (!empty($data)) {
            foreach ($data as $field => $value) {
                update_field($field, $value, $process_id);
            }
        }

        return new OffsetProcess($process_id);
    }
}
