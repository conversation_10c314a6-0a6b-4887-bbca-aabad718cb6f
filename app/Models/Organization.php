<?php

namespace App\Models;

use Exception;
use WP_Query;


class OrganisationUser
{
    public int $id;
    public ?string $name = null;
    public ?string $email = null;
    public string|false $avatar = false;

    public function __construct(int $id)
    {
        $this->id = $id;
        $user = get_user_by('id', $id);
        if ($user) {
            $this->name = $user->display_name;
            $this->email = $user->user_email;
            $this->avatar = get_avatar_url($id);
        }
    }

    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'avatar' => $this->avatar
        ];
    }
}

class Organization
{
    public int $id;
    public string $name;
    public array $users;
    public ?string $image;

    static public string $post_type = 'organization';

    public string $creator_id;

    public function __construct(int $organization_id)
    {
        $post = get_post($organization_id);
        if (!$post || $post->post_type !== Organization::$post_type) {
            throw new Exception('Organization not found');
        }

        $this->id = $organization_id;
        $this->name = $post->post_title;
        $users = get_field('users', $organization_id) ?: [];
        $this->users = array_map(fn(int $user_id) => new OrganisationUser($user_id), $users);
        $this->creator_id = $post->post_author;
        $this->image = get_field('image', $organization_id);
    }

    public static function getUserOrganization(int $user_id): ?self
    {
        $args = array(
            'post_type' => 'organization',
            'meta_query' => array(
                array(
                    'key' => 'users',
                    'value' => '"' . $user_id . '"',
                    'compare' => 'LIKE'
                )
            )
        );
        $query = new WP_Query($args);
        if ($query->have_posts()) {
            return new self($query->posts[0]->ID);
        }
        wp_reset_postdata();
        return null;
    }

    public static function create(string $name, array $users): self
    {
        $organization_id = wp_insert_post([
            'post_type' => 'organization',
            'post_title' => $name,
            'post_status' => 'publish'
        ]);

        update_field('name', $name, $organization_id);
        update_field('users', $users, $organization_id);

        return new self($organization_id);
    }

    public function addUserToOrganization(int $user_id): void
    {
        $current_user_ids = array_map(fn(OrganisationUser $user) => $user->id, $this->users);
        if (!in_array($user_id, $current_user_ids)) {
            $current_user_ids[] = $user_id;
            update_field('users', $current_user_ids, $this->id);
        }
    }

    public function removeUserFromOrganization(int $user_id): void
    {
        $current_user_ids = array_map(fn(OrganisationUser $user) => $user->id, $this->users);
        if (in_array($user_id, $current_user_ids)) {
            $updated_user_ids = array_filter($current_user_ids, fn($id) => $id !== $user_id);
            update_field('users', $updated_user_ids, $this->id);
        }
    }

    public function doesUserBelongToOrganization(int $user_id): bool
    {
        $current_user_ids = array_map(fn(OrganisationUser $user) => $user->id, $this->users);
        return in_array($user_id, $current_user_ids);
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getUsers(): array
    {
        return $this->users;
    }

    /**
     * Get all compensation processes created by the user
     * @return array<OffsetProcess>
     */
    public function getOffsetProcesses(): array
    {
        $args = array(
            'post_type' => OffsetProcess::$post_type,
            'meta_query' => array(
                array(
                    'key' => 'organization_id',
                    'value' => $this->id,
                    'compare' => '='
                )
            )
        );
        $query = new WP_Query($args);
        $processes = [];
        while ($query->have_posts()) {
            $query->the_post();
            $id = get_the_ID();
            if ($id == false) {
                continue;
            }
            $processes[] = new OffsetProcess($id);
        }
        wp_reset_postdata();
        return $processes;
    }

    /**
     * Get all compensation processes created by the user
     * @return array<OffsetProcess>
     */
    public function getAllUsersCompensationProjects(): array
    {
        $args = [
            'post_type' =>  OffsetProcess::$post_type,
            'meta_query' => [
                [
                    'key' => 'user',
                    'value' => $this->users,
                    'compare' => 'IN'
                ]
            ]
        ];
        $query = new WP_Query($args);
        $processes = [];
        while ($query->have_posts()) {
            $query->the_post();
            $id = get_the_ID();
            if ($id == false) {
                continue;
            }
            $processes[] = new OffsetProcess($id);
        }
        wp_reset_postdata();

        return $processes;
    }

    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'users' => array_map(fn(OrganisationUser $user) => $user->toArray(), $this->users),
            'creator_id' => $this->creator_id
        ];
    }

    public function toJson(): string|false
    {
        return json_encode($this->toArray());
    }
}
