<?php

namespace App\Models;
use Exception;
use WP_Query;
use App\Models\Order;

class Vendor
{
    public int $id;
    public string $name;
    public string $email;
    public string $phone;
    public string $company;
    public string|false $avatar;
    public array $roles;
    public array $statistics;

    public function __construct(int $vendor_id)
    {
        $user = get_user_by('id', $vendor_id);
        if (!$user) {
            throw new Exception('Vendor not found');
        }

        $this->id = $user->ID;
        $this->name = $user->display_name;
        $this->email = $user->user_email;
        $this->phone = get_user_meta($user->ID, 'phone', true) ?: '';
        $this->company = get_user_meta($user->ID, 'company', true) ?: '';
        $this->avatar = get_avatar_url($user->ID);
        $this->roles = array_map(fn($role) => ['label' => ucfirst($role), 'type' => $role], $user->roles);
        $this->statistics = $this->get_statistics($vendor_id);
    }

    private function get_statistics(int $vendor_id): array
    {
        return [];
    }


    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'phone' => $this->phone,
            'company' => $this->company,
            'avatar' => $this->avatar,
            'roles' => $this->roles,
            'statistics' => $this->statistics,
        ];
    }

    public function toJson(): string|false
    {
        return json_encode($this->toArray());
    }
}
