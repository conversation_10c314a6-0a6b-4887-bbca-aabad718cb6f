<?php

namespace App\Models;

use Exception;

class Offer
{
    static public string $post_type = 'offer';

    public int $id;
    public string $status;
    public array $product;
    public array $creator;
    public array $offer_for;
    public array $accepted_by;
    public array $declined;
    public string $message;
    public string $date_created;

    public function __construct(int $offer_id)
    {
        $offer = get_post($offer_id);

        if (empty($offer) || $offer->post_type !== Offer::$post_type) {
            throw new Exception('Offer not found');
        }

        $acf_fields = get_fields($offer_id);

        if (!isset($acf_fields['product_id'])) {
            throw new Exception('Product ID not found in offer');
        }

        $product_id = $acf_fields['product_id'];
        $product = wc_get_product($product_id);

        if (!$product) {
            throw new Exception('Product not found');
        }

        $this->id = $offer_id;
        $this->status = $this->determine_status($acf_fields);
        $this->product = [
            'id' => $product->get_id(),
            'original_price' => $product->get_regular_price(),
            'link' => get_permalink($product->get_id()),
            'name' => $product->get_name(),
            'categories' => get_the_terms($product->get_id(), 'product_cat'),
            'quantity' => $product->get_stock_quantity(),
        ];
        $this->creator = get_field('creator', $offer_id);
        $this->offer_for = get_field('offer_for', $offer_id);
        $this->accepted_by = get_field('accepted_by', $offer_id);
        $this->declined = get_field('declined', $offer_id) ?? [];
        $this->message = get_field('message', $offer_id);
        $this->date_created = $offer->post_date;
    }

    private function determine_status(array|false $acf_fields = []): string
    {
        $accepted_by = $acf_fields['accepted_by'] ?? null;
        $offer_for = $acf_fields['offer_for'] ?? [];
        $declined = $acf_fields['declined'] ?? [];

        if (!empty($accepted_by)) {
            return 'accepted';
        }

        $all_declined = true;
        foreach ($offer_for as $user) {
            if (!in_array($user['id'], $declined)) {
                $all_declined = false;
                break;
            }
        }

        return $all_declined ? 'declined' : 'review';
    }

    public static function get(int $offer_id): Offer|null
    {
        try {
            return new Offer($offer_id);
        } catch (\Throwable $th) {
            return null;
        }
    }

    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'status' => $this->status,
            'product' => $this->product,
            'creator' => $this->creator,
            'offer_for' => $this->offer_for,
            'accepted_by' => $this->accepted_by,
            'declined' => $this->declined,
            'message' => $this->message,
            'date_created' => $this->date_created,
        ];
    }

    public function toJson(): string | false
    {
        return json_encode($this->toArray());
    }
}
