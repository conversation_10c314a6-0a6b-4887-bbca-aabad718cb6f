<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Post extends Model
{
    protected $table      = 'posts';
    protected $primaryKey = 'ID';
    public    $timestamps = false;       // WP uses `post_date` columns instead

    // Only expose the fields you need
    protected $fillable = [
        'post_title',
        'post_content',
        'post_status',
        'post_type',
        'post_date',
        'post_date_gmt',
    ];
}
