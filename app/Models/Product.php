<?php

namespace App\Models;

use Exception;
use WC_Product;
use WP_Term;

class Product extends WC_Product
{
    public string $name;
    public string $description;
    public string $short_description;
    public float $price;
    public array $images;
    public string $status;
    public array $acf_fields;

    public ?string $country;

    public function __construct(int|WC_Product $_product)
    {
        if (!function_exists('wc_get_product')) {
            throw new Exception('WooCommerce is not installed');
        }

        if ($_product instanceof WC_Product) {
            $product = $_product;
        } else {
            $product = wc_get_product(the_product: $_product);
        }

        if (!$product) {
            throw new Exception('Product not found');
        }


        $this->id = $product->get_id();
        $this->name = $product->get_name();
        $this->description = $product->get_description();
        $this->short_description = $product->get_short_description();
        $this->price = (float) $product->get_price();
        $this->images = $this->get_images($product);
        $this->status = $product->get_status();
        $fields = get_fields($product->get_id());
        $this->acf_fields = is_array($fields) ? $fields : [];
        $this->country = $this->acf_fields['country'] ?? null;

        parent::__construct($product);
    }

    private function get_images(WC_Product $product): array
    {
        $image_id = $product->get_image_id();

        $gallery_image_urls = array_map(function (int $image_id) {
            return wp_get_attachment_url($image_id);
        }, $product->get_gallery_image_ids());

        $images = [];
        if ($image_id) {
            $main_image_url = wp_get_attachment_url((int) $image_id);
            if ($main_image_url !== false) {
                $images[] = $main_image_url;
            }
        }
        foreach ($gallery_image_urls as $image) {
            $images[] = $image;
        }
        return $images;
    }

    public function to_array(): array
    {
        $post = get_post($this->get_id());
        return [
            'id' => $this->id,
            'name' => $this->name,
            'description' => $this->description,
            'short_description' => $this->short_description,
            'price' => $this->price,
            'categories' => $this->get_terms(),
            'images' => $this->images,
            'status' => $this->status,
            'acf_fields' => $this->acf_fields,
            'author_id' => $post ? (int)$post->post_author : null,
        ];
    }

    public function get_terms_objects(): array
    {
        $terms = get_the_terms($this->get_id(), 'product_cat');
        if (is_wp_error($terms) || $terms === false) {
            return [];
        }
        return $terms;
    }

    /**
     * Get the terms as an array of slugs.
     *
     * @return array<string>
     */
    public function get_terms(): array
    {
        $terms = $this->get_terms_objects();
        return array_map(function (WP_Term $term) {
            return $term->slug;
        }, $terms);
    }

    public function is_compensation_project(): bool
    {
        $categories = $this->get_terms();
        return in_array('compensation-project', $categories);
    }
    public function is_consultation(): bool
    {
        $categories = $this->get_terms();
        return in_array('consult-service', $categories);
    }

    public function get_sdgs(): array
    {
        $sdg_terms = get_the_terms($this->get_id(), 'sdg');

        if (is_wp_error($sdg_terms) || $sdg_terms === false) {
            return [];
        }

        $sdgs = [];
        foreach ($sdg_terms as $term) {
            $icon = get_field("image", $term);
            array_push($sdgs, [
                "name" => $term->name,
                "slug" => $term->slug,
                "description" => $term->description,
                "icon" => $icon["url"],
            ]);
        }
        return $sdgs;
    }

    public function get_project_types(): array
    {
        $project_types = get_the_terms($this->get_id(), 'comp_project_type');
        if (is_wp_error($project_types) || $project_types === false) {
            return [];
        }
        $arr = [];
        foreach ($project_types as $term) {
            $icon = get_field("image", $term);
            array_push($arr, [
                "name" => $term->name,
                "slug" => $term->slug,
                "description" => $term->description,
                "icon" => $icon["url"],
            ]);
        }
        return $arr;
    }

    public function get_main_project_type(): array|null
    {
        $project_types = $this->get_project_types();
        return $project_types[0] ?? null;
    }

    public function get_link(): string|bool
    {
        return get_the_permalink($this->get_id());
    }

    public function get_score(): int|null
    {
        $score = get_field("score", $this->get_id());
        return $score !== null && $score !== '' ? (int) $score : null;
    }

    public function get_environmental_benefits_list(): array
    {
        $benefits = get_field("environmental_benefits_list", $this->get_id());
        return is_array($benefits) ? $benefits : [];
    }

    public function get_co_benefits(): array
    {
        $benefit_fields = [
            'affordable_energy' => 'Affordable and clean energy',
            'economic_growth' => 'Decent work and economic growth',
            'innovation' => 'Industry, innovation and infrastructure',
            'sustainable_communities' => 'Sustainable cities and communities',
            'climate_action' => 'Climate action'
        ];

        $benefits = [];
        foreach ($benefit_fields as $key => $label) {
            if ($this->acf_fields[$key] ?? false) {
                $benefits[$key] = [
                    'label' => $label,
                    'description' => $this->acf_fields["{$key}_description"] ?? '',
                ];
            }
        }
        return $benefits;
    }

    public function get_available_tonnes(): int
    {
        // Use standard WooCommerce product quantity
        if ($this->is_compensation_project() && $this->managing_stock()) {
            return (int) $this->get_stock_quantity();
        }
        return 0;
    }

    public function is_active(): bool
    {
        return (bool) ($this->acf_fields['is_active'] ?? false);
    }

    public function get_certification(): string|null
    {
        return $this->acf_fields['certification'] ?? null;
    }

    public function get_project_code(): string|null
    {
        return $this->acf_fields['project_code'] ?? null;
    }

    public function get_external_webpage(): string|null
    {
        return $this->acf_fields['external_webpage'] ?? null;
    }

    public function get_organization(): string|null
    {
        return $this->acf_fields['organization'] ?? null;
    }

    public function get_environmental_benefits(): string|null
    {
        return $this->acf_fields['environmental_benefits'] ?? null;
    }

    public function get_social_benefits(): string|null
    {
        return $this->acf_fields['social_benefits'] ?? null;
    }

    public function toJson(): string|false
    {
        return json_encode($this->to_array());
    }

    public function get_project_id(): string|null
    {
        return $this->acf_fields['project_id'] ?? null;
    }

    public function get_project_provider(): string|null
    {
        return $this->acf_fields['project_provider'] ?? null;
    }

    public function get_certificate(): string|null
    {
        return $this->acf_fields['certificate'] ?? null;
    }

    public function get_time_estimate(): ?string
    {
        return get_field('time_estimate', $this->id);
    }

    public function can_ask_for_offer(): bool
    {
        return (bool) ($this->acf_fields['can_ask_for_offer'] ?? false);
    }

    public function get_env_description(): string|null
    {
        return $this->acf_fields['env_description'] ?? null;
    }

    public function get_social_description(): string|null
    {
        return $this->acf_fields['social_description'] ?? null;
    }

    public function get_impact_description(): string|null
    {
        return $this->acf_fields['impact_description'] ?? null;
    }

    public function get_company_name(): ?string
    {
        return get_field('company_name', $this->id);
    }

    public function get_standard(): ?string
    {
        if (!$this->is_consultation()) {
            return null;
        }

        $standard = get_field('standard', $this->id);
        if (!$standard) {
            return null;
        }

        return is_array($standard) ? implode(', ', $standard) : $standard;
    }

    public function get_purchase_type(): ?string
    {
        if ($this->is_consultation()) {
            return get_field('purchase_type', $this->id);
        }

        $author_id = get_post_field('post_author', $this->id);
        $vendor = function_exists('dokan') ? dokan()->vendor->get($author_id) : null;

        if ($vendor && $vendor->get_meta('accepted_terms', true) === 'true') {
            return 'purchase';
        }

        return 'purchase order';
    }

    public function get_url_slug(): string
    {
        $company = $this->get_company_name();
        return $company ? str_replace(" ", "-", strtolower($company)) : '';
    }

    public function get_country_map_url(): string|null
    {
        $country = $this->country;
        if (!$country) return null;

        $stringWithPluses = str_replace(" ", "+", $country);
        return preg_replace('/,\\s+/', ',', $stringWithPluses);
    }

    static function get_all_sdgs(): array
    {
        $sdgs = [];
        $terms = get_terms([
            'taxonomy' => 'sdg',
            'hide_empty' => false,
        ]);
        foreach ($terms as $term) {
            $icon = get_field("image", $term);
            array_push($sdgs, [
                "name" => $term->name,
                "slug" => $term->slug,
                "description" => $term->description,
                "icon" => $icon["url"]
            ]);
        }
        return $sdgs;
    }
    static function get_all_projectTypes(): array
    {
        $project_types = [];
        $projects = get_terms([
            'taxonomy' => 'comp_project_type',
            'hide_empty' => false,
        ]);
        foreach ($projects as $project) {
            $icon = get_field("image", $project);
            array_push($project_types, [
                "name" => $project->name,
                "slug" => $project->slug,
                "description" => $project->description,
                "icon" => $icon["url"],
            ]);
        }
        return $project_types;
    }

    static function get_all_countries(): array
    {
        $field_key = "field_6711612a8bd98";
        $field = get_field_object($field_key);

        $countries = [];

        if ($field) {
            foreach ($field["choices"] as $_ => $value) {
                array_push($countries, $value);
            }
        }
        return $countries;
    }

    static function get_all_certifications(): array
    {
        $field_key = "field_665456633a384";
        $field = get_field_object($field_key);
        $certifications = [];
        if ($field && isset($field['choices'])) {
            foreach ($field['choices'] as $_ => $value) {
                $certifications[] = [
                    'slug' => sanitize_title($value),
                    'name' => $value
                ];
            }
        }
        return $certifications;
    }

    static function get_max_price_of_cat(string $category): float
    {
        $args = [
            'post_type' => 'product',
            'posts_per_page' => 1,
            'tax_query' => [
                [
                    'taxonomy' => 'product_cat',
                    'field' => 'slug',
                    'terms' => $category,
                    'operator' => 'IN'
                ]
            ],
            'meta_key' => '_price',
            'orderby' => 'meta_value_num',
            'order' => 'DESC',
            'fields' => 'ids'
        ];

        $query = new \WP_Query($args);

        if ($query->have_posts()) {
            $product_id = $query->posts[0];
            $price = get_post_meta($product_id, '_price', true);
            return (float) $price;
        }
        wp_reset_postdata();

        return 10000.0; // Default max price if no products found
    }

    static function get_max_available_tonnes(): int
    {
        $args = [
            'post_type' => 'product',
            'posts_per_page' => 1,
            'tax_query' => [
                [
                    'taxonomy' => 'product_cat',
                    'field' => 'slug',
                    'terms' => 'compensation-project',
                    'operator' => 'IN'
                ]
            ],
            'meta_key' => '_stock',
            'orderby' => 'meta_value_num',
            'order' => 'DESC',
            'fields' => 'ids'
        ];

        $query = new \WP_Query($args);

        if ($query->have_posts()) {
            $product_id = $query->posts[0];
            $product = wc_get_product($product_id);
            if ($product && $product->managing_stock()) {
                return (int) $product->get_stock_quantity();
            }
        }
        wp_reset_postdata();

        return 10000; // Default max tonnes if no products found
    }

    static function get_max_reviews_count()
    {
        $args = [
            'post_type' => 'product',
            'posts_per_page' => 1,
            'meta_key' => '_wc_review_count',
            'orderby' => 'meta_value_num',
            'order' => 'DESC',
            'fields' => 'ids'
        ];

        $query = new \WP_Query($args);

        if ($query->have_posts()) {
            $product_id = $query->posts[0];
            $product = wc_get_product($product_id);
            if ($product) {
                return (int) $product->get_review_count();
            }
        }
        wp_reset_postdata();

        return 1000;
    }


    // Consulting service specific methods
    public function get_service_id(): ?string
    {
        return get_field('service_id', $this->id);
    }

    public function get_consultant_id(): ?string
    {
        return get_field('consultant_id', $this->id);
    }

    public function get_country(): string
    {
        return $this->country ?: 'Global';
    }
}
