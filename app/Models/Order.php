<?php

namespace App\Models;

use Exception;
use WC_Order;
use WC_Order_Item_Product;
use WC_Product;

class Order
{
    public int $id;
    public string $status;
    public float $total;
    public ?string $date_created;
    public array $items;
    public array $billing;
    public array $shipping;
    public string $currency_symbol;
    public string $status_label;
    public string $status_color;
    public string $status_text_color;
    public string $customer_note;
    public int $customer_id;
    public bool $is_paid;
    public string $payment_link;

    public function __construct(int $order_id)
    {
        $order = wc_get_order($order_id);

        if (!$order instanceof WC_Order) {
            throw new Exception('Order not found');
        }

        $this->id = $order->get_id();
        $this->status = $order->get_status();
        $this->total = $order->get_total();
        $this->date_created = $order->get_date_created()?->date('Y-m-d H:i:s');
        $this->items = $this->get_items($order);
        $this->billing = $this->get_billing($order);
        $this->shipping = $this->get_shipping($order);
        $this->currency_symbol = get_woocommerce_currency_symbol($order->get_currency());
        $this->status_label = wc_get_order_status_name($this->status);
        $this->status_color = $this->get_order_status_color($this->status);
        $this->status_text_color = $this->get_order_status_text_color($this->status);
        $this->customer_note = $order->get_customer_note();
        $this->customer_id = $order->get_customer_id();
        $this->is_paid = $order->is_paid();
        $this->payment_link = $order->get_checkout_payment_url();
    }

    private function get_items(WC_Order $order): array
    {
        $items = [];
        foreach ($order->get_items() as $item_id => $item) {
            if ($item instanceof WC_Order_Item_Product) {
                $product = $item->get_product();

                if (!$product instanceof WC_Product) {
                    continue;
                }

                $categories = $product->get_category_ids();
                $categories_names = array_map(function ($category_id) {
                    $term = get_term_by('id', $category_id, 'product_cat');
                    return $term ? $term->name : '';
                }, $categories);

                $items[] = [
                    'item_id' => $item_id,
                    'product_id' => $item->get_product_id(),
                    'name' => $item->get_name(),
                    'quantity' => $item->get_quantity(),
                    'subtotal' => $item->get_subtotal(),
                    'total' => $item->get_total(),
                    'categories' => $categories_names,
                    'image' => wp_get_attachment_url($product->get_image_id()),
                    'link' => get_permalink($product->get_id()),
                    'product' => [
                        'name' => $product->get_name(),
                        'sku' => $product->get_sku(),
                        'price' => $product->get_price(),
                    ],
                ];
            }
        }
        return $items;
    }

    private function get_billing(WC_Order $order): array
    {
        return [
            'first_name' => $order->get_billing_first_name(),
            'last_name' => $order->get_billing_last_name(),
            'address_1' => $order->get_billing_address_1(),
            'address_2' => $order->get_billing_address_2(),
            'city' => $order->get_billing_city(),
            'state' => $order->get_billing_state(),
            'postcode' => $order->get_billing_postcode(),
            'country' => $order->get_billing_country(),
            'email' => $order->get_billing_email(),
            'phone' => $order->get_billing_phone(),
        ];
    }

    private function get_shipping(WC_Order $order): array
    {
        return [
            'first_name' => $order->get_shipping_first_name(),
            'last_name' => $order->get_shipping_last_name(),
            'address_1' => $order->get_shipping_address_1(),
            'address_2' => $order->get_shipping_address_2(),
            'city' => $order->get_shipping_city(),
            'state' => $order->get_shipping_state(),
            'postcode' => $order->get_shipping_postcode(),
            'country' => $order->get_shipping_country(),
        ];
    }

    private function get_order_status_color(string $status): string
    {
        $colors = $this->get_order_status_colors($status);
        return $colors['status_color'];
    }

    private function get_order_status_text_color(string $status): string
    {
        $colors = $this->get_order_status_colors($status);
        return $colors['status_text_color'];
    }

    function get_order_status_colors(string $order_status): array
    {
        // Normalize the order status by removing 'wc-' if it's present
        $normalized_status = str_replace('wc-', '', $order_status);

        // Retrieve the color settings from the ACF options page
        $colors = get_field($normalized_status . '_colors', 'option');

        $default_background_color = null;
        $default_text_color = null;

        if ($colors) {
            $background_color = isset($colors['background_color']) ? $colors['background_color'] : $default_background_color;
            $text_color = isset($colors['text_color']) ? $colors['text_color'] : $default_text_color;
        } else {
            // Fall back to default colors if the group field is empty
            $background_color = $default_background_color;
            $text_color = $default_text_color;
        }

        // Return the colors as an array
        return array(
            'status_color' => $background_color,
            'status_text_color' => $text_color,
        );
    }


    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'status' => $this->status,
            'total' => $this->total,
            'date_created' => $this->date_created,
            'items' => $this->items,
            'billing' => $this->billing,
            'shipping' => $this->shipping,
            'currency_symbol' => $this->currency_symbol,
            'status_label' => $this->status_label,
            'status_color' => $this->status_color,
            'status_text_color' => $this->status_text_color,
            'customer_note' => $this->customer_note,
            'customer_id' => $this->customer_id,
            'is_paid' => $this->is_paid,
            'payment_link' => $this->payment_link,
        ];
    }

    public function toJson(): string|false
    {
        return json_encode($this->toArray());
    }

    public static function create(array $data): Order
    {
        $order = wc_create_order();
        self::populate_order_data($order, $data);
        $order->save();

        return new self($order->get_id());
    }

    public static function update(int $order_id, array $data): Order
    {
        $order = wc_get_order($order_id);
        if (!$order) {
            throw new Exception('Order not found');
        }

        self::populate_order_data($order, $data);
        $order->save();

        return new self($order->get_id());
    }

    public static function delete(int $order_id): bool
    {
        $order = wc_get_order($order_id);
        if (!$order) {
            throw new Exception('Order not found');
        }

        wp_delete_post($order_id, true);

        return true;
    }

    private static function populate_order_data(WC_Order $order, array $data)
    {
        if (isset($data['status'])) {
            $order->set_status(sanitize_text_field($data['status']));
        }
        if (isset($data['total'])) {
            $order->set_total(floatval($data['total']));
        }
        if (isset($data['billing'])) {
            $order->set_billing_first_name(sanitize_text_field($data['billing']['first_name']));
            $order->set_billing_last_name(sanitize_text_field($data['billing']['last_name']));
            $order->set_billing_address_1(sanitize_text_field($data['billing']['address_1']));
            $order->set_billing_address_2(sanitize_text_field($data['billing']['address_2']));
            $order->set_billing_city(sanitize_text_field($data['billing']['city']));
            $order->set_billing_state(sanitize_text_field($data['billing']['state']));
            $order->set_billing_postcode(sanitize_text_field($data['billing']['postcode']));
            $order->set_billing_country(sanitize_text_field($data['billing']['country']));
            $order->set_billing_email(sanitize_email($data['billing']['email']));
            $order->set_billing_phone(sanitize_text_field($data['billing']['phone']));
        }
        if (isset($data['shipping'])) {
            $order->set_shipping_first_name(sanitize_text_field($data['shipping']['first_name']));
            $order->set_shipping_last_name(sanitize_text_field($data['shipping']['last_name']));
            $order->set_shipping_address_1(sanitize_text_field($data['shipping']['address_1']));
            $order->set_shipping_address_2(sanitize_text_field($data['shipping']['address_2']));
            $order->set_shipping_city(sanitize_text_field($data['shipping']['city']));
            $order->set_shipping_state(sanitize_text_field($data['shipping']['state']));
            $order->set_shipping_postcode(sanitize_text_field($data['shipping']['postcode']));
            $order->set_shipping_country(sanitize_text_field($data['shipping']['country']));
        }
    }
}
