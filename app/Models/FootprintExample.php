<?php

namespace App\Models;

use Exception;
use WP_Post;

class FootprintExample
{

  public ?string $image;
  public ?string $size;
  public ?string $description;

  public ?float $scope1;
  public ?float $scope2;
  public ?float $scope3;

  public ?float $total_carbon_footprint;

  public ?string $level1;
  public ?string $level2;
  public ?string $level3;
  public ?string $level4;
  public ?string $level5;

  public WP_Post $post;


    public function __construct(int|WP_Post $_post)
    {
        if ($_post instanceof WP_Post) {
            $post = $_post;
        } else {
            $post = get_post($_post);
        }

        if (!$post) {
            throw new Exception('Footprint example not found');
        }

        $this->post = $post;

        $fields = get_fields($post->ID);
        $featured_image = get_the_post_thumbnail_url($post->ID);
        $acf_image = $fields['image'] ?: null;
        if($acf_image) {
          $acf_image = $acf_image['url'];
        }
        $this->image = $acf_image ?: $featured_image ?: null;
        $this->size = $fields['size'] ?: null;
        $this->description = $fields['description'] ?: null;

        $this->scope1 = $fields['scope_1'] ?: null;
        $this->scope2 = $fields['scope_2'] ?: null;
        $this->scope3 = $fields['scope_3'] ?: null;

        $this->total_carbon_footprint = $fields['total_carbon_footprint'] ?: null;

        $this->level1 = $fields['level_1'] ?: null;
        $this->level2 = $fields['level_2'] ?: null;
        $this->level3 = $fields['level_3'] ?: null;
        $this->level4 = $fields['level_4'] ?: null;
        $this->level5 = $fields['level_5'] ?: null;
    }

    public function getImageUrl(): ?string
    {
       if ($this->image) {
        return $this->image;
       }
       return '';
    }

     /**
     * Get all compensation processes created by the user
     * @return array<Product>
     */
    public function getCompensationProjects(): array
    {
        $compensation_projects = get_field('compensation_projects', $this->post->ID);
        $projects = [];
        if ($compensation_projects) {
            foreach ($compensation_projects as $project) {
                $projects[] = new Product($project->ID);
            }
        }
        return $projects;
    }

    public function getAvailableCompensationProjects(): array 
    {
        try {
            $compensation_projects = get_field('compensation_projects', $this->post->ID);
            error_log('WP Debug - Post ID: ' . $this->post->ID);
            error_log('WP Debug - Compensation projects raw: ' . print_r($compensation_projects, true));
            
            $projects = [];
            
            if ($compensation_projects) {
                foreach ($compensation_projects as $project) {
                    try {
                        $product = new Product($project->ID);
                        $price = $product->get_price();
                        error_log('WP Debug - Processing project ID: ' . $project->ID . ' with price: ' . $price);
                        
                        if ($price) {
                            $projects[] = [
                                'id' => $project->ID,
                                'title' => $project->post_title,
                                'price' => floatval($price),
                                'quantity' => $product->get_stock_quantity()
                            ];
                        }
                    } catch (\Exception $e) {
                        error_log('WP Debug - Error processing project: ' . $e->getMessage());
                    }
                }
            }

            error_log('WP Debug - Final projects array: ' . print_r($projects, true));
            return array_slice($projects, 0, 5);

        } catch (\Exception $e) {
            error_log('WP Debug - Error in getAvailableCompensationProjects: ' . $e->getMessage());
            return [];
        }
    }

    public function getDefaultPricePerTonne(): float
    {
        return 12.0;
    }

    public function typicalOffsetCost(): float|null
    {
        $tonnes = $this->total_carbon_footprint;
        if(!$tonnes) {
            return null;
        }
        
        // Always start with default price of 10€
        $price_per_tonne = 10;
        
        // Only use project price if we have available projects
        $projects = $this->getAvailableCompensationProjects();
        if (count($projects) > 0) {
            $price_per_tonne = $projects[0]['price'];
        }
        
        error_log('WP Debug - Calculating typical offset cost:');
        error_log('WP Debug - Tonnes: ' . $tonnes);
        error_log('WP Debug - Price per tonne: ' . $price_per_tonne);
        error_log('WP Debug - Total: ' . ($price_per_tonne * $tonnes));
        
        return $price_per_tonne * $tonnes;
    }

    public function getScopePercentages(): array
    {
        $total = $this->scope1 + $this->scope2 + $this->scope3;
        if ($total === 0) return [0, 0, 0];
        
        return [
            round(($this->scope1 / $total) * 100),
            round(($this->scope2 / $total) * 100),
            round(($this->scope3 / $total) * 100)
        ];
    }

    public function getScopeTwoTotal(): float
    {
        return ($this->scope1 ?? 0) + ($this->scope2 ?? 0);
    }

    public function getScopeThreeTotal(): float
    {
        return $this->total_carbon_footprint ?? 0; // This should be scope 1+2+3
    }
}