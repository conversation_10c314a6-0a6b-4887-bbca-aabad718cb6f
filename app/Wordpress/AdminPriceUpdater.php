<?php

namespace App\Wordpress;

use App\Services\CurrencyService;

/**
 * Admin Price Updater
 * 
 * Adds a manual price update button to the WordPress admin interface
 */
class AdminPriceUpdater
{
    /**
     * Initialize the class
     */
    public function __construct()
    {
        // Handle form submission
        add_action('admin_post_update_product_prices', [$this, 'handleManualUpdate'], 10, 0);
        
        // Display admin notices
        add_action('admin_notices', [$this, 'displayAdminNotices'], 10, 0);
    }

    /**
     * Render the admin page
     */
    public function renderAdminPage()
    {
        // Check if a form was submitted directly on this page
        if (isset($_POST['update_prices_direct']) && $_POST['update_prices_direct'] === '1') {
            // Verify nonce
            if (isset($_POST['_wpnonce']) && wp_verify_nonce($_POST['_wpnonce'], 'update_product_prices_nonce')) {
                try {
                    // Run the update
                    $currencyService = new CurrencyService();
                    $stats = $currencyService->updateAllProductPrices();
                    
                    // Store success message in transient
                    set_transient('co2market_price_update_summary', [
                        'type' => 'success',
                        'stats' => $stats,
                        'time' => current_time('mysql')
                    ], DAY_IN_SECONDS); // Keep for 24 hours
                } catch (\Exception $e) {
                    // Store error message in transient
                    set_transient('co2market_price_update_summary', [
                        'type' => 'error',
                        'message' => $e->getMessage(),
                        'time' => current_time('mysql')
                    ], DAY_IN_SECONDS); // Keep for 24 hours
                }
            } else {
                // Store error message for failed nonce
                set_transient('co2market_price_update_summary', [
                    'type' => 'error',
                    'message' => __('Security check failed.', 'co2market'),
                    'time' => current_time('mysql')
                ], DAY_IN_SECONDS); // Keep for 24 hours
            }
            
            // Redirect to the same page to avoid form resubmission
            wp_redirect(admin_url('admin.php?page=co2market-update-prices&updated=true'));
            exit;
        }
        
        ?>
        <div class="wrap">
            <h1><?php echo __('Update Product Prices', 'co2market'); ?></h1>
            
            <?php
            // Display the persistent summary if it exists
            $summary = get_transient('co2market_price_update_summary');
            if ($summary) {
                if ($summary['type'] === 'success') {
                    $this->displaySuccessMessage($summary['stats'], $summary['time']);
                } else {
                    $this->displayErrorMessage($summary['message'], $summary['time']);
                }
                
                // Add a button to clear the summary
                ?>
                <div class="clear-summary">
                    <form method="post">
                        <?php wp_nonce_field('clear_summary_nonce'); ?>
                        <input type="hidden" name="clear_summary" value="1">
                        <button type="submit" class="button"><?php echo __('Clear Summary', 'co2market'); ?></button>
                    </form>
                </div>
                <?php
            }
            
            // Check if we should clear the summary
            if (isset($_POST['clear_summary']) && isset($_POST['_wpnonce']) && wp_verify_nonce($_POST['_wpnonce'], 'clear_summary_nonce')) {
                delete_transient('co2market_price_update_summary');
                wp_redirect(admin_url('admin.php?page=co2market-update-prices'));
                exit;
            }
            ?>
            
            <div class="card">
                <h2><?php echo __('Manual Price Update', 'co2market'); ?></h2>
                <p><?php echo __('Click the button below to update all product prices in EUR based on their native currency prices and current exchange rates.', 'co2market'); ?></p>
                
                <form method="post">
                    <?php wp_nonce_field('update_product_prices_nonce'); ?>
                    <input type="hidden" name="update_prices_direct" value="1">
                    
                    <button type="submit" class="button button-primary">
                        <?php echo __('Update Prices Now', 'co2market'); ?>
                    </button>
                </form>
            </div>
            
            <div class="card" style="margin-top: 20px;">
                <h2><?php echo __('Price Update Information', 'co2market'); ?></h2>
                <p><?php echo __('This tool updates WooCommerce product prices in EUR based on:', 'co2market'); ?></p>
                <ul style="list-style-type: disc; margin-left: 20px;">
                    <li><?php echo __('Native currency prices stored in ACF fields', 'co2market'); ?></li>
                    <li><?php echo __('Current exchange rates from WPMC (WooCommerce Multi-Currency)', 'co2market'); ?></li>
                </ul>
                
                <div style="margin-top: 15px; padding: 10px; background-color: #f8f8f8; border-left: 4px solid #0073aa;">
                    <h3><?php echo __('How EUR Products Are Handled', 'co2market'); ?></h3>
                    <p><?php echo __('Products with native currency set to EUR will have their WooCommerce price fields updated directly from the native price ACF field, without any currency conversion.', 'co2market'); ?></p>
                    <p><?php echo __('This ensures that all products have their WooCommerce prices synchronized with the ACF native price field, regardless of the currency.', 'co2market'); ?></p>
                </div>
                
                <div style="margin-top: 15px;">
                    <h3><?php echo __('Rate Fluctuation Protection', 'co2market'); ?></h3>
                    <p><?php echo __('Products are protected from excessive exchange rate fluctuations (>10% in a single day) to maintain price stability.', 'co2market'); ?></p>
                </div>
            </div>
            
            <?php
            // Display the last batch update time if available
            $last_update = get_option('co2market_last_price_update');
            if ($last_update) {
                ?>
                <div class="card" style="margin-top: 20px;">
                    <h2><?php echo __('Last Batch Update', 'co2market'); ?></h2>
                    <p><?php printf(__('Last batch price update was performed on: %s', 'co2market'), $last_update); ?></p>
                </div>
                <?php
            }

            // Display next scheduled update information
            $this->displayScheduleInfo();
            ?>
        </div>
        
        <style>
            .clear-summary {
                margin: 10px 0;
                text-align: right;
            }
            .update-summary {
                margin-bottom: 20px;
                padding: 15px;
                border-radius: 3px;
                position: relative;
            }
            .update-summary.success {
                background-color: #f0f6fc;
                border-left: 4px solid #00a32a;
            }
            .update-summary.error {
                background-color: #fcf0f1;
                border-left: 4px solid #d63638;
            }
            .update-summary h2 {
                margin-top: 0;
            }
            .update-summary .timestamp {
                font-size: 12px;
                color: #666;
                margin-top: 10px;
            }
        </style>
        <?php
    }

    /**
     * Handle manual update
     */
    public function handleManualUpdate()
    {
        // Check nonce
        if (!isset($_POST['update_prices_nonce']) || !wp_verify_nonce($_POST['update_prices_nonce'], 'update_product_prices_nonce')) {
            wp_die(__('Security check failed.', 'co2market'));
        }
        
        // Check capabilities
        if (!current_user_can('administrator')) {
            wp_die(__('You do not have sufficient permissions to access this page.', 'co2market'));
        }
        
        try {
            // Run the update
            $currencyService = new CurrencyService();
            $stats = $currencyService->updateAllProductPrices();
            
            // Store result in transient for admin notice
            set_transient('co2market_price_update_result', [
                'success' => true,
                'stats' => $stats,
                'time' => current_time('mysql')
            ], 60);
        } catch (\Exception $e) {
            // Store error in transient for admin notice
            set_transient('co2market_price_update_result', [
                'success' => false,
                'message' => $e->getMessage(),
                'time' => current_time('mysql')
            ], 60);
        }
        
        // Redirect back to the admin page
        wp_redirect(admin_url('admin.php?page=co2market-update-prices'));
        exit;
    }

    /**
     * Display admin notices
     */
    public function displayAdminNotices()
    {
        // Check if we're on our admin page
        if (!isset($_GET['page']) || $_GET['page'] !== 'co2market-update-prices') {
            return;
        }
        
        // Check for update result
        $result = get_transient('co2market_price_update_result');
        if (!$result) {
            return;
        }
        
        // Delete the transient
        delete_transient('co2market_price_update_result');
        
        if ($result['success']) {
            $stats = $result['stats'];
            $this->displaySuccessMessage($stats);
        } else {
            $this->displayErrorMessage($result['message']);
        }
    }

    /**
     * Display success message with stats
     * 
     * @param array $stats Update statistics
     * @param string $timestamp When the update was performed
     */
    private function displaySuccessMessage($stats, $timestamp = null)
    {
        if ($timestamp === null) {
            $timestamp = current_time('mysql');
        }
        ?>
        <div class="update-summary success">
            <h2><?php echo __('Price Update Summary', 'co2market'); ?></h2>
            <p><strong><?php echo __('Price update completed successfully!', 'co2market'); ?></strong></p>
            <ul>
                <li><?php printf(__('Total products: %d', 'co2market'), $stats['total']); ?></li>
                <li><?php printf(__('Updated (with currency conversion): %d', 'co2market'), $stats['updated']); ?></li>
                <li><?php printf(__('Updated (EUR products): %d', 'co2market'), $stats['eur_updated'] ?? 0); ?></li>
                <?php if (isset($stats['currency_changed']) && $stats['currency_changed'] > 0): ?>
                <li><?php printf(__('Products with currency changes: %d', 'co2market'), $stats['currency_changed']); ?></li>
                <?php endif; ?>
                <li><?php printf(__('Skipped: %d', 'co2market'), $stats['skipped']); ?></li>
                <li><?php printf(__('Errors: %d', 'co2market'), $stats['errors']); ?></li>
            </ul>
            
            <?php if (isset($stats['currency_changed']) && $stats['currency_changed'] > 0 && !empty($stats['details'])): ?>
            <div class="notice-warning" style="padding: 10px; margin: 5px 0; background-color: #FFF8E5; border-left: 4px solid #FFB900;">
                <p><strong><?php echo __('Currency Changes Detected', 'co2market'); ?></strong></p>
                <p><?php echo __('The following products had their currency changed:', 'co2market'); ?></p>
                <table class="widefat" style="margin-top: 10px;">
                    <thead>
                        <tr>
                            <th><?php echo __('Product', 'co2market'); ?></th>
                            <th><?php echo __('Old Currency', 'co2market'); ?></th>
                            <th><?php echo __('New Currency', 'co2market'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($stats['details'] as $detail): ?>
                            <?php if (isset($detail['old_currency']) && isset($detail['new_currency'])): ?>
                            <tr>
                                <td><a href="<?php echo get_edit_post_link($detail['product_id']); ?>"><?php echo esc_html($detail['name']); ?></a></td>
                                <td><?php echo esc_html($detail['old_currency']); ?></td>
                                <td><?php echo esc_html($detail['new_currency']); ?></td>
                            </tr>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            <?php endif; ?>
            
            <?php if (!empty($stats['protected_rates'])): ?>
            <div class="notice-warning" style="padding: 10px; margin: 5px 0;">
                <p><strong><?php echo __('Rate Fluctuation Protection Applied', 'co2market'); ?></strong></p>
                <p><?php echo __('The following exchange rates showed excessive fluctuation (>10%) and were protected:', 'co2market'); ?></p>
                <table class="widefat" style="margin-top: 10px;">
                    <thead>
                        <tr>
                            <th><?php echo __('Currency', 'co2market'); ?></th>
                            <th><?php echo __('Previous Rate', 'co2market'); ?></th>
                            <th><?php echo __('Attempted Rate', 'co2market'); ?></th>
                            <th><?php echo __('Fluctuation', 'co2market'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($stats['protected_rates'] as $currency => $data): ?>
                        <tr>
                            <td><?php echo esc_html($currency); ?></td>
                            <td><?php echo esc_html(number_format($data['previous_rate'], 6)); ?></td>
                            <td><?php echo esc_html(number_format($data['current_rate'], 6)); ?></td>
                            <td><?php echo esc_html($data['fluctuation_percent'] . '%'); ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            <?php endif; ?>
            
            <p class="timestamp"><?php printf(__('Updated at: %s', 'co2market'), $timestamp); ?></p>
        </div>
        <?php
    }

    /**
     * Display error message
     * 
     * @param string $message Error message
     * @param string $timestamp When the error occurred
     */
    private function displayErrorMessage($message, $timestamp = null)
    {
        if ($timestamp === null) {
            $timestamp = current_time('mysql');
        }
        ?>
        <div class="update-summary error">
            <h2><?php echo __('Price Update Error', 'co2market'); ?></h2>
            <p><strong><?php printf(__('Price update failed: %s', 'co2market'), esc_html($message)); ?></strong></p>
            <p class="timestamp"><?php printf(__('Error occurred at: %s', 'co2market'), $timestamp); ?></p>
        </div>
        <?php
    }

    /**
     * Display schedule information
     */
    private function displayScheduleInfo(): void
    {
        ?>
        <div class="card" style="margin-top: 20px;">
            <h2><?php echo __('Automatic Price Updates', 'co2market'); ?></h2>

            <?php
            // Calculate next midnight for display
            $next_midnight = strtotime('tomorrow midnight');
            if ($next_midnight === false) {
                $next_midnight = strtotime('+1 day midnight');
            }
            $next_run_formatted = ($next_midnight !== false) ? date('Y-m-d H:i:s', $next_midnight) : 'Unknown';
            $hours_until = ($next_midnight !== false) ? ceil(($next_midnight - time()) / 3600) : 0;
            ?>

            <div style="padding: 10px; background-color: #f0f6fc; border-left: 4px solid #00a32a; margin-bottom: 15px;">
                <p><strong><?php echo __('✅ Automatic updates are scheduled', 'co2market'); ?></strong></p>
                <p><?php printf(__('Next automatic price update: %s (in %d hours)', 'co2market'), esc_html($next_run_formatted), $hours_until); ?></p>
                <p><small><?php echo __('Updates run daily at midnight (00:00)', 'co2market'); ?></small></p>
            </div>

            <div style="padding: 10px; background-color: #fff8e5; border-left: 4px solid #ffb900; margin-bottom: 15px;">
                <p><strong><?php echo __('ℹ️ System Requirements', 'co2market'); ?></strong></p>
                <p><?php echo __('Automatic updates require a system cron job to be configured on the server.', 'co2market'); ?></p>
                <p><small><?php echo __('If automatic updates are not working, contact your system administrator to set up the cron job.', 'co2market'); ?></small></p>
            </div>

            <p><?php echo __('Automatic price updates use the same logic as manual updates:', 'co2market'); ?></p>
            <ul style="list-style-type: disc; margin-left: 20px;">
                <li><?php echo __('Convert prices from native currencies to EUR', 'co2market'); ?></li>
                <li><?php echo __('Apply rate fluctuation protection (>10% changes)', 'co2market'); ?></li>
                <li><?php echo __('Update WooCommerce price fields', 'co2market'); ?></li>
                <li><?php echo __('Log results for monitoring', 'co2market'); ?></li>
            </ul>

            <div style="margin-top: 15px; padding: 10px; background-color: #f8f8f8; border-left: 4px solid #0073aa;">
                <h3><?php echo __('For System Administrators', 'co2market'); ?></h3>
                <p><?php echo __('To enable automatic updates, add this cron job to your server:', 'co2market'); ?></p>
                <code style="display: block; padding: 5px; background-color: #fff; border: 1px solid #ddd; margin: 5px 0;">
                    * * * * * cd <?php echo esc_html(get_template_directory()); ?> && wp acorn schedule:run >> /dev/null 2>&1
                </code>
            </div>
        </div>
        <?php
    }
}