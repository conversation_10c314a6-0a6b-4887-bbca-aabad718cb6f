<?php

namespace App\Wordpress;

use App\Services\CurrencyService;

/**
 * Product Price Hooks
 * 
 * Hooks into WordPress product creation and update events to update prices
 */
class ProductPriceHooks
{
    /**
     * Initialize the class
     */
    public function __construct()
    {
        // Hook into ACF save post action to update prices when ACF fields are saved
        add_action('acf/save_post', [$this, 'updatePriceOnAcfSave'], 20, 1);
        
        // Hook into WooCommerce product save to ensure prices are updated
        add_action('woocommerce_process_product_meta', [$this, 'updatePriceOnProductSave'], 20, 1);
        
        // Hook into product creation
        add_action('woocommerce_new_product', [$this, 'updatePriceOnProductSave'], 20, 1);
        
        // Hook into product duplication
        add_action('woocommerce_product_duplicate', [$this, 'updatePriceOnProductDuplicate'], 10, 2);
        
        // Make exchange rate and timestamp fields read-only
        add_filter('acf/prepare_field', [$this, 'makeFieldsReadOnly']);
        
        // Add custom CSS to style read-only fields
        add_action('admin_head', [$this, 'addReadOnlyFieldStyles'], 10, 0);
        
        // Add late priority fix for WooCommerce product data tabs
        add_action('admin_footer', [$this, 'fixWooCommerceProductData'], 999, 0);
    }
    
    /**
     * Update price when ACF fields are saved
     * 
     * @param int $post_id The post ID
     */
    public function updatePriceOnAcfSave($post_id)
    {
        // Check if this is a product
        if (get_post_type($post_id) !== 'product') {
            return;
        }
        
        // Check if native price or currency fields were updated
        $updated_fields = $_POST['acf'] ?? [];
        $price_fields_updated = false;
        $currency_updated = false;
        
        // Look for our price fields in the updated fields
        if (!empty($updated_fields)) {
            foreach ($updated_fields as $key => $_) {
                if (strpos($key, 'native_price') !== false) {
                    $price_fields_updated = true;
                }
                if (strpos($key, 'native_currency') !== false) {
                    $currency_updated = true;
                    $price_fields_updated = true; // Always update price when currency changes
                }
            }
        }
        
        // If price fields were updated or we can't determine, update the price
        if ($price_fields_updated || empty($updated_fields)) {
            // Force immediate update when currency changes
            if ($currency_updated) {
                $this->forceProductPriceUpdate($post_id);
            } else {
                $this->updateProductPrice($post_id);
            }
        }
    }
    
    /**
     * Force update of product price regardless of previous values
     * This is especially important when currency changes
     * 
     * @param int $product_id The product ID
     */
    private function forceProductPriceUpdate($product_id)
    {
        try {
            $currencyService = new CurrencyService();
            
            // Use handleProductSave with force=true to update regardless of price difference
            $updated = $currencyService->handleProductSave($product_id, true);
            
            if (!$updated) {
                // Log if update failed
                error_log("Failed to update price for product {$product_id} after currency change");
            }
        } catch (\Exception $e) {
            // Log error if needed
            error_log('Error updating product price after currency change: ' . $e->getMessage());
        }
    }
    
    /**
     * Update price when product is saved in WooCommerce
     * 
     * @param int $product_id The product ID
     */
    public function updatePriceOnProductSave($product_id)
    {
        $this->updateProductPrice($product_id);
    }
    
    /**
     * Update price when product is duplicated
     * 
     * @param \WC_Product $new_product The new product
     * @param \WC_Product $old_product The original product
     */
    public function updatePriceOnProductDuplicate($new_product, $old_product)
    {
        $this->updateProductPrice($new_product->get_id());
    }
    
    /**
     * Make exchange rate and timestamp fields read-only
     * 
     * @param array $field The ACF field being prepared
     * @return array The modified field
     */
    public function makeFieldsReadOnly($field)
    {
        // ONLY target our specific ACF fields by key
        $protected_fields = ['exchange_rate_at_save', 'price_last_updated'];
        
        if (isset($field['name']) && in_array($field['name'], $protected_fields)) {
            // Option 1: Make the field read-only
            $field['readonly'] = true;
            $field['disabled'] = true;
            
            // Use a very specific class name to avoid conflicts
            $specific_class = 'co2market-acf-readonly-' . $field['name'];
            $field['wrapper']['class'] = isset($field['wrapper']['class']) ? 
                $field['wrapper']['class'] . ' ' . $specific_class : 
                $specific_class;
            
            // Add a description to explain why it's read-only
            $field['instructions'] = isset($field['instructions']) ? 
                $field['instructions'] . ' ' . __('This field is automatically managed by the system and cannot be modified manually.', 'co2market') : 
                __('This field is automatically managed by the system and cannot be modified manually.', 'co2market');
            
            // Option 3: Hide from non-admin users
            if (!current_user_can('manage_options')) {
                $field['wrapper']['class'] .= ' co2market-acf-hidden-field';
            }
        }
        
        return $field;
    }
    
    /**
     * Add custom CSS to style read-only fields
     */
    public function addReadOnlyFieldStyles()
    {
        echo '<style>
            /* Use very specific selectors to avoid affecting WooCommerce fields */
            .co2market-acf-readonly-exchange_rate_at_save input,
            .co2market-acf-readonly-price_last_updated input {
                background-color: #f8f8f8 !important;
                color: #666 !important;
                border-color: #ddd !important;
                cursor: not-allowed !important;
            }
            
            .co2market-acf-readonly-exchange_rate_at_save .acf-label label,
            .co2market-acf-readonly-price_last_updated .acf-label label {
                color: #666 !important;
                font-style: italic;
            }
            
            .co2market-acf-readonly-exchange_rate_at_save .acf-input-wrap:after,
            .co2market-acf-readonly-price_last_updated .acf-input-wrap:after {
                content: "' . __('Automatically managed', 'co2market') . '";
                display: block;
                font-size: 11px;
                color: #999;
                font-style: italic;
                margin-top: 5px;
            }
            
            /* Hidden fields */
            .co2market-acf-hidden-field {
                display: none !important;
            }
            
            /* Add more specificity to ensure WooCommerce price fields are not affected */
            body.post-type-product #general_product_data ._regular_price_field,
            body.post-type-product #general_product_data ._sale_price_field,
            body.post-type-product #general_product_data ._price_field {
                display: block !important;
                visibility: visible !important;
                opacity: 1 !important;
            }
            
            body.post-type-product #general_product_data ._regular_price_field input,
            body.post-type-product #general_product_data ._sale_price_field input {
                background-color: #fff !important;
                color: #2c3338 !important;
                cursor: text !important;
                opacity: 1 !important;
                pointer-events: auto !important;
            }
        </style>';
        
        // Only add JavaScript on product edit screens
        $screen = get_current_screen();
        if ($screen && $screen->id === 'product' && $screen->base === 'post') {
            echo '<script>
            jQuery(document).ready(function($) {
                // Make sure the WooCommerce product data panels are initialized
                if (typeof woocommerce_admin_meta_boxes !== "undefined") {
                    // Force show the General tab
                    $("#general_product_data").show();
                    
                    // Make sure price fields are visible
                    $("._regular_price_field, ._sale_price_field").show();
                    
                    // Fix any hidden inputs
                    $("#_regular_price, #_sale_price").css({
                        "display": "block",
                        "visibility": "visible",
                        "opacity": "1"
                    });
                    
                    // Watch for tab changes to ensure price fields stay visible
                    $(".product_data_tabs .general_options a").on("click", function() {
                        setTimeout(function() {
                            $("._regular_price_field, ._sale_price_field").show();
                        }, 100);
                    });
                }
            });
            </script>';
        }
    }
    
    /**
     * Fix WooCommerce product data tabs and ensure price fields are visible
     * This runs very late to override any other scripts that might be hiding fields
     */
    public function fixWooCommerceProductData()
    {
        // Only run on product edit screens
        $screen = get_current_screen();
        if (!$screen || $screen->id !== 'product' || $screen->base !== 'post') {
            return;
        }
        
        ?>
        <script type="text/javascript">
            jQuery(document).ready(function($) {
                // Force re-initialization of product tabs
                if (typeof woocommerce_admin_meta_boxes !== 'undefined') {
                    // Make sure the tabs are clickable
                    $('.product_data_tabs .product_data_tab').removeClass('active');
                    $('.product_data_tabs .general_options').addClass('active');
                    $('.woocommerce_options_panel').hide();
                    $('#general_product_data').show();
                    
                    // Make price fields visible with !important
                    $('#general_product_data ._regular_price_field, #general_product_data ._sale_price_field')
                        .attr('style', 'display: block !important');
                    
                    // Make sure price inputs are enabled and visible
                    $('#_regular_price, #_sale_price').prop('disabled', false)
                        .attr('style', 'display: block !important; opacity: 1 !important;')
                        .css({
                            'pointer-events': 'auto',
                            'background-color': '#fff'
                        });
                }
                
                // Watch for tab changes to ensure price fields stay visible
                $(document).on('click', '.product_data_tabs .product_data_tab a', function() {
                    // Short delay to let WooCommerce scripts run first
                    setTimeout(function() {
                        $('#general_product_data ._regular_price_field, #general_product_data ._sale_price_field')
                            .attr('style', 'display: block !important');
                            
                        $('#_regular_price, #_sale_price').prop('disabled', false)
                            .attr('style', 'display: block !important; opacity: 1 !important;');
                    }, 200);
                });
            });
        </script>
        <?php
    }
    
    /**
     * Update product price using CurrencyService
     * 
     * @param int $product_id The product ID
     */
    private function updateProductPrice($product_id)
    {
        try {
            $currencyService = new CurrencyService();
            $currencyService->handleProductSave($product_id);
        } catch (\Exception $e) {
            // Log error if needed
            error_log('Error updating product price: ' . $e->getMessage());
        }
    }
} 