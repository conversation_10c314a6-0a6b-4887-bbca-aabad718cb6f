<?php

namespace App\Providers;

use Roots\Acorn\Sage\SageServiceProvider;

class RouteServiceProvider extends SageServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        parent::register();
    }
    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        parent::boot();

        $this->loadRoutesFrom(
            $this->app->basePath('/routes/api.php'),
        );
        $this->loadRoutesFrom(
            $this->app->basePath('/routes/web.php')
        );
    }
}
