<?php

namespace App\Providers;

use Illuminate\Support\Facades\Vite;
use Roots\Acorn\Sage\SageServiceProvider;

class ThemeServiceProvider extends SageServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        parent::register();
    }

    public function asset($asset)
    {
        return Vite::asset($asset);
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        parent::boot();

        Vite::macro('image', fn(string $asset) => $this->asset("resources/images/{$asset}"));

        Vite::macro("svg", fn(string $asset) => $this->asset("resources/svg/{$asset}"));
    }
}
