<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Console\Kernel as ConsoleKernel;
use Illuminate\Console\Scheduling\Schedule;

class PriceUpdaterServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    #[\Override]
    public function register()
    {
        // Register the Console Kernel with our command
        $this->app->singleton('console.kernel', function ($app) {
            return $app->make(ConsoleKernel::class);
        });
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        // Register the Schedule handler
        $this->app->singleton(Schedule::class, function ($app) {
            return $app->make(Schedule::class);
        });
        
        // Register our command
        if ($this->app->runningInConsole()) {
            $this->commands([
                \App\Console\Commands\UpdateProductPrices::class,
            ]);
        }
    }
} 