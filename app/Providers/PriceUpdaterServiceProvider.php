<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Console\Commands\UpdateProductPrices;

class PriceUpdaterServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    #[\Override]
    public function register()
    {
        //
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        // Register our command
        if ($this->app->runningInConsole()) {
            $this->commands([
                UpdateProductPrices::class,
            ]);
        }
    }
}