<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Console\Commands\UpdateProductPrices;
use App\Console\Kernel as ConsoleKernel;

class PriceUpdaterServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    #[\Override]
    public function register()
    {
        // Register the Console Kernel to ensure scheduling works
        $this->app->singleton('Illuminate\Contracts\Console\Kernel', ConsoleKernel::class);
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        // Register our command
        if ($this->app->runningInConsole()) {
            $this->commands([
                UpdateProductPrices::class,
            ]);
        }
    }
}