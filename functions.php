<?php

use Roots\Acorn\Application;

/*
|--------------------------------------------------------------------------
| Register The Auto Loader
|--------------------------------------------------------------------------
|
| Composer provides a convenient, automatically generated class loader for
| our theme. We will simply require it into the script here so that we
| don't have to worry about manually loading any of our classes later on.
|
*/

if (! file_exists($composer = __DIR__ . '/vendor/autoload.php')) {
    wp_die(__('Error locating autoloader. Please run <code>composer install</code>.', 'sage'));
}

require $composer;

/*
|--------------------------------------------------------------------------
| Register The Bootloader
|--------------------------------------------------------------------------
|
| The first thing we will do is schedule a new Acorn application container
| to boot when WordPress is finished loading the theme. The application
| serves as the "glue" for all the components of Laravel and is
| the IoC container for the system binding all of the various parts.
|
*/


Application::configure()
    ->withProviders([
        App\Providers\ThemeServiceProvider::class,
        App\Providers\RouteServiceProvider::class,
        App\Providers\PriceUpdaterServiceProvider::class,
        Genero\Sage\WooCommerce\WooCommerceServiceProvider::class,
        Inertia\ServiceProvider::class
    ])
    ->withRouting(wordpress: true)
    ->boot();

/*
|--------------------------------------------------------------------------
| Register Sage Theme Files
|--------------------------------------------------------------------------
|
| Out of the box, Sage ships with categorically named theme files
| containing common functionality and setup to be bootstrapped with your
| theme. Simply add (or remove) files from the array below to change what
| is registered alongside Sage.
|
*/

collect(['setup', 'filters'])
    ->each(function ($file) {
        if (! locate_template($file = "app/{$file}.php", true, true)) {
            wp_die(
                /* translators: %s is replaced with the relative file path */
                sprintf(__('Error locating <code>%s</code> for inclusion.', 'sage'), $file)
            );
        }
    });

function init_acf_blocks(): void
{
    $block_json_files = glob(get_template_directory() . '/blocks/**/block.json');
    // auto register all blocks that were found.
    if ($block_json_files !== false) {
        foreach ($block_json_files as $block_json_file) {
            register_block_type($block_json_file);
        }
    }
}
add_action('init', 'init_acf_blocks', 10, 0);

function custom_register_url(string $url): string
{
    // Check if the current request is not in the admin dashboard
    if (!is_admin()) {
        // Return the new registration URL
        return home_url('/register/');
    }
    // Return the default URL if in the admin dashboard
    return $url;
}
// Add the custom function to the 'register_url' filter
add_filter('register_url', 'custom_register_url');

function custom_lostpassword_url(string $lostpassword_url, string $redirect): string
{
    // Return the new lost password URL with the redirect parameter if provided
    if (!empty($redirect)) {
        return home_url('/lost-password/?redirect_to=' . urlencode($redirect));
    } else {
        return home_url('/lost-password/');
    }
}
// Add the custom function to the 'lostpassword_url' filter
add_filter('lostpassword_url', 'custom_lostpassword_url', 10, 2);

function get_login_url(): string
{
    return home_url('/login/');
}


define('CO2MARKET_TEXT_DOMAIN', 'CO2market');

add_action('wp_loaded', function () {
    if (WP_DEBUG == false) {
        error_reporting(0);
    } else {
        if (WP_DEBUG == true) {
            error_reporting(E_ALL & ~E_WARNING & ~E_DEPRECATED & ~E_USER_DEPRECATED & ~E_NOTICE);
        }
    }
}, 10, 0);

// Save terms acceptance status in user metadata during registration
function save_terms_acceptance($user_id) {
    if (isset($_POST['accept_terms']) && $_POST['accept_terms'] === 'on') {
        update_user_meta($user_id, 'accepted_terms', 'true');
        update_user_meta($user_id, 'accepted_terms_time', current_time('mysql'));
    }
}
add_action('user_register', 'save_terms_acceptance', 10, 1);

// Validate that terms checkbox is checked during registration
function validate_terms_acceptance($errors) {
    if (!isset($_POST['accept_terms']) || $_POST['accept_terms'] !== 'on') {
        $errors->add('terms_not_accepted', __('You must accept the terms and conditions to register.', CO2MARKET_TEXT_DOMAIN));
    }
    return $errors;
}
add_filter('registration_errors', 'validate_terms_acceptance', 10, 3);
