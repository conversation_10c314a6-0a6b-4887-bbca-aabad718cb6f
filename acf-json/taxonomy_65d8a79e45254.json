{"key": "taxonomy_65d8a79e45254", "title": "Comp. project types", "menu_order": 0, "active": true, "taxonomy": "comp_project_type", "object_type": ["product"], "advanced_configuration": 1, "import_source": "", "import_date": "", "labels": {"name": "Comp. project types", "singular_name": "Comp. Project type", "menu_name": "", "all_items": "", "edit_item": "", "view_item": "", "update_item": "", "add_new_item": "", "new_item_name": "", "search_items": "", "popular_items": "", "separate_items_with_commas": "", "add_or_remove_items": "", "choose_from_most_used": "", "most_used": "", "not_found": "", "no_terms": "", "name_field_description": "", "slug_field_description": "", "desc_field_description": "", "items_list_navigation": "", "items_list": "", "back_to_items": "", "item_link": "", "item_link_description": ""}, "description": "", "capabilities": {"manage_terms": "manage_categories", "edit_terms": "manage_categories", "delete_terms": "manage_categories", "assign_terms": "edit_posts"}, "public": 1, "publicly_queryable": 1, "hierarchical": 0, "show_ui": 1, "show_in_menu": 1, "show_in_nav_menus": 1, "show_in_rest": 1, "rest_base": "", "rest_namespace": "wp/v2", "rest_controller_class": "WP_REST_Terms_Controller", "show_tagcloud": 1, "show_in_quick_edit": 1, "show_admin_column": 0, "rewrite": {"permalink_rewrite": "custom_permalink", "slug": "offset-projects", "with_front": "0", "rewrite_hierarchical": "1"}, "query_var": "post_type_key", "query_var_name": "", "default_term": {"default_term_enabled": "0"}, "sort": 0, "meta_box": "default", "meta_box_cb": "", "meta_box_sanitize_cb": "", "modified": 1733742465}