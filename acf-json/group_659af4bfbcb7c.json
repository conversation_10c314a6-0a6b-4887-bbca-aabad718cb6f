{"key": "group_659af4bfbcb7c", "title": "Consulting service attributes", "fields": [{"key": "field_668c024e4efff", "label": "Service ID", "name": "service_id", "aria-label": "", "type": "number", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "wpml_cf_preferences": 0, "default_value": "", "min": "", "max": "", "placeholder": "", "step": "", "prepend": "", "append": ""}, {"key": "field_668c02744f000", "label": "Consultant ID", "name": "consultant_id", "aria-label": "", "type": "number", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "wpml_cf_preferences": 0, "default_value": "", "min": "", "max": "", "placeholder": "", "step": "", "prepend": "", "append": ""}, {"key": "field_659af4c03acff", "label": "Company name", "name": "company_name", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "wpml_cf_preferences": 0, "show_in_graphql": 1, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}, {"key": "field_659af4ed3ad01", "label": "Time estimate", "name": "time_estimate", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "wpml_cf_preferences": 0, "show_in_graphql": 1, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}, {"key": "field_6609299e8254f", "label": "Purchase type", "name": "purchase_type", "aria-label": "", "type": "select", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "wpml_cf_preferences": 0, "choices": {"Buy now": "Buy now", "Ask for offer": "Ask for offer"}, "default_value": false, "return_format": "value", "multiple": 0, "allow_null": 0, "ui": 0, "ajax": 0, "placeholder": "", "create_options": 0, "save_options": 0, "allow_custom": 0, "search_placeholder": ""}, {"key": "field_66f27f8e088f0", "label": "Standard", "name": "standard", "aria-label": "", "type": "checkbox", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "wpml_cf_preferences": 0, "choices": {"Greenhouse Gas Protocol (GHG Protocol)": "Greenhouse Gas Protocol (GHG Protocol)", "ISO 14064": "ISO 14064", "ISO 14067": "ISO 14067", "Climate Disclosure Standards Board (CDSB) Framework": "Climate Disclosure Standards Board (CDSB) Framework", "EU Corporate Sustainability Reporting Directive (CSRD) (EU)": "EU Corporate Sustainability Reporting Directive (CSRD) (EU)", "PAS 2050 (UK)": "PAS 2050 (UK)", "Bilan Carbone (FR)": "Bilan Carbone (FR)", "Environmental Protection Agency (EPA) GHG Reporting Program (US)": "Environmental Protection Agency (EPA) GHG Reporting Program (US)", "National Greenhouse and Energy Reporting (NGER) Scheme (AU)": "National Greenhouse and Energy Reporting (NGER) Scheme (AU)", "温室气体排放核算方法与报告指南 (China's Greenhouse Gas Accounting Guidelines) (CN)": "温室气体排放核算方法与报告指南 (China's Greenhouse Gas Accounting Guidelines) (CN)", "温室効果ガス排出量算定・報告制度 (Mandatory Greenhouse Gas Accounting and Reporting System) (JP)": "温室効果ガス排出量算定・報告制度 (Mandatory Greenhouse Gas Accounting and Reporting System) (JP)", "カーボンフットプリント制度 (Carbon Footprint Program) (JP)": "カーボンフットプリント制度 (Carbon Footprint Program) (JP)"}, "default_value": [], "return_format": "value", "allow_custom": 1, "save_custom": 0, "allow_in_bindings": 1, "layout": "vertical", "toggle": 0, "custom_choice_button_text": "Add new choice"}], "location": [[{"param": "post_type", "operator": "==", "value": "product"}, {"param": "post_taxonomy", "operator": "==", "value": "product_cat:consult-service"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "", "show_in_rest": 0, "acfe_display_title": "", "acfe_autosync": ["php", "json"], "acfe_form": 0, "acfe_meta": "", "acfe_note": "", "modified": **********}