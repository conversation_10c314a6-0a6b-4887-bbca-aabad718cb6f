{"key": "group_679f529ee172e", "title": "Offset process", "fields": [{"key": "field_679f52a06dfbc", "label": "Orders", "name": "orders", "aria-label": "", "type": "repeater", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "acfe_repeater_stylised_button": 0, "layout": "table", "pagination": 0, "min": 0, "max": 0, "collapsed": "", "button_label": "Add Row", "rows_per_page": 20, "sub_fields": [{"key": "field_679f52e86dfbd", "label": "id", "name": "id", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "allow_in_bindings": 0, "placeholder": "", "prepend": "", "append": "", "parent_repeater": "field_679f52a06dfbc"}]}, {"key": "field_679f53106dfbe", "label": "Communicate files", "name": "communicate_files", "aria-label": "", "type": "repeater", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "acfe_repeater_stylised_button": 0, "layout": "table", "pagination": 0, "min": 0, "max": 0, "collapsed": "", "button_label": "Add Row", "rows_per_page": 20, "sub_fields": [{"key": "field_679f53196dfbf", "label": "json", "name": "json", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "allow_in_bindings": 0, "placeholder": "", "prepend": "", "append": "", "parent_repeater": "field_679f53106dfbe"}]}, {"key": "field_679f534e6dfc0", "label": "step", "name": "step", "aria-label": "", "type": "select", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "choices": {"calculate": "calculate", "compensate": "compensate", "communicate": "communicate", "complete": "complete"}, "default_value": "calculate", "return_format": "value", "multiple": 0, "allow_custom": 0, "placeholder": "", "search_placeholder": "", "allow_null": 0, "allow_in_bindings": 0, "ui": 0, "ajax": 0}, {"key": "field_679f554c6dfc1", "label": "Organization", "name": "organization_id", "aria-label": "", "type": "post_object", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "post_type": ["organization"], "post_status": "", "taxonomy": "", "return_format": "id", "multiple": 0, "save_custom": 0, "save_post_status": "publish", "acfe_bidirectional": {"acfe_bidirectional_enabled": "0"}, "allow_null": 0, "allow_in_bindings": 1, "bidirectional": 0, "ui": 1, "bidirectional_target": [], "save_post_type": ""}, {"key": "field_679f55786dfc2", "label": "User", "name": "user", "aria-label": "", "type": "user", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "role": "", "return_format": "id", "multiple": 0, "acfe_bidirectional": {"acfe_bidirectional_enabled": "0"}, "allow_null": 0, "allow_in_bindings": 0, "bidirectional": 0, "bidirectional_target": []}, {"key": "field_679f559b6dfc3", "label": "Verified by", "name": "verified_by", "aria-label": "", "type": "user", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "role": "", "return_format": "id", "multiple": 0, "acfe_bidirectional": {"acfe_bidirectional_enabled": "0"}, "allow_null": 0, "allow_in_bindings": 0, "bidirectional": 0, "bidirectional_target": []}, {"key": "field_679f55b06dfc4", "label": "Ghg scope", "name": "ghg_scope", "aria-label": "", "type": "checkbox", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "choices": {"1": "1", "2": "2", "3": "3"}, "default_value": [], "return_format": "value", "allow_custom": 0, "allow_in_bindings": 0, "layout": "vertical", "toggle": 0, "save_custom": 0, "custom_choice_button_text": "Add new choice"}, {"key": "field_679f55de6dfc5", "label": "Emissions", "name": "emissions", "aria-label": "", "type": "repeater", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "acfe_repeater_stylised_button": 0, "layout": "table", "pagination": 0, "min": 0, "max": 0, "collapsed": "", "button_label": "Add Row", "rows_per_page": 20, "sub_fields": [{"key": "field_679f55ed6dfc6", "label": "name", "name": "name", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "allow_in_bindings": 0, "placeholder": "", "prepend": "", "append": "", "parent_repeater": "field_679f55de6dfc5"}, {"key": "field_679f55f86dfc7", "label": "value", "name": "value", "aria-label": "", "type": "number", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "min": "", "max": "", "allow_in_bindings": 0, "placeholder": "", "step": "", "prepend": "", "append": "", "parent_repeater": "field_679f55de6dfc5"}]}, {"key": "field_679f567d6dfc8", "label": "Calculation Type", "name": "calculation_type", "aria-label": "", "type": "select", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "choices": {"audited": "audited", "approved": "approved", "unapproved": "unapproved", "generic": "generic", "professional": "professional"}, "default_value": "unapproved", "return_format": "value", "multiple": 0, "allow_null": 0, "allow_in_bindings": 0, "ui": 0, "ajax": 0, "placeholder": "", "allow_custom": 0, "search_placeholder": ""}, {"key": "field_67ef7ebb225e5", "label": "Files", "name": "files", "aria-label": "", "type": "repeater", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "acfe_repeater_stylised_button": 0, "layout": "table", "pagination": 0, "min": 0, "max": 0, "collapsed": "", "button_label": "Add Row", "rows_per_page": 20, "sub_fields": [{"key": "field_67ef7f855b212", "label": "File", "name": "file", "aria-label": "", "type": "file", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "uploader": "", "return_format": "array", "min_size": "", "max_size": "", "mime_types": "", "allow_in_bindings": 0, "library": "all", "parent_repeater": "field_67ef7ebb225e5"}]}], "location": [[{"param": "post_type", "operator": "==", "value": "offset-process"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "left", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "", "show_in_rest": 1, "acfe_display_title": "", "acfe_autosync": ["php", "json"], "acfe_form": 0, "acfe_meta": "", "acfe_note": "", "modified": 1744436094}