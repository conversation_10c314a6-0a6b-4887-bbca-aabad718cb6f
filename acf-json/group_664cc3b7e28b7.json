{"key": "group_664cc3b7e28b7", "title": "Template generator fields", "fields": [{"key": "field_664cc3b813b24", "label": "Preview", "name": "preview", "aria-label": "", "type": "image", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "wpml_cf_preferences": 1, "return_format": "array", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": "", "preview_size": "medium", "uploader": "", "acfe_thumbnail": 0}, {"key": "field_664ce1caed4ad", "label": "Json", "name": "json", "aria-label": "", "type": "textarea", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "wpml_cf_preferences": 2, "default_value": "", "maxlength": "", "rows": "", "placeholder": "", "new_lines": "", "acfe_textarea_code": 0}, {"key": "field_6671de5c783c1", "label": "Select fields", "name": "select_fields", "aria-label": "", "type": "group", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "wpml_cf_preferences": 1, "layout": "block", "acfe_seamless_style": 0, "acfe_group_modal": 0, "acfe_group_modal_close": 0, "acfe_group_modal_button": "", "acfe_group_modal_size": "large", "sub_fields": [{"key": "field_6671de77783c2", "label": "Main text", "name": "main_text", "aria-label": "", "type": "select", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "wpml_cf_preferences": 1, "choices": {"We're carbon neutral": "We're carbon neutral", "We're carbon negative": "We're carbon negative", "Lorem Ipsum": "<PERSON><PERSON>"}, "default_value": false, "return_format": "value", "multiple": 0, "allow_null": 0, "ui": 0, "ajax": 0, "placeholder": "", "allow_custom": 0, "search_placeholder": ""}, {"key": "field_6671eb1210b3b", "label": "test", "name": "test", "aria-label": "", "type": "select", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "wpml_cf_preferences": 1, "choices": {"test": "test", "lorem ipsum": "lorem ipsum"}, "default_value": false, "return_format": "value", "multiple": 0, "allow_null": 0, "ui": 0, "ajax": 0, "placeholder": "", "allow_custom": 0, "search_placeholder": ""}]}, {"key": "field_66857630a8819", "label": "Is preview", "name": "is_preview", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "wpml_cf_preferences": 1, "message": "", "default_value": 0, "ui": 0, "ui_on_text": "", "ui_off_text": ""}, {"key": "field_66b2722677fa1", "label": "variables", "name": "variables", "aria-label": "", "type": "group", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "wpml_cf_preferences": 1, "layout": "block", "acfe_seamless_style": 0, "acfe_group_modal": 0, "acfe_group_modal_close": 0, "acfe_group_modal_button": "", "acfe_group_modal_size": "large", "sub_fields": [{"key": "field_66b2838177fa2", "label": "acf.business_name", "name": "acfbusiness_name", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "wpml_cf_preferences": 2, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "Company name:", "append": ""}, {"key": "field_66b283b977fa3", "label": "acf.ghg_scope", "name": "acfghg_scope", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "wpml_cf_preferences": 2, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}, {"key": "field_66b283ca77fa4", "label": "step", "name": "step", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "wpml_cf_preferences": 2, "default_value": "", "maxlength": "", "allow_in_bindings": 1, "placeholder": "", "prepend": "", "append": ""}, {"key": "field_66b2840177fa6", "label": "calculation.verified_by", "name": "calculationverified_by", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "wpml_cf_preferences": 2, "default_value": "", "maxlength": "", "allow_in_bindings": 1, "placeholder": "", "prepend": "Verified by", "append": "Ok"}, {"key": "field_6732591c66711", "label": "calculation.carbonFootprint", "name": "calculationcarbonfootprint", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "wpml_cf_preferences": 2, "default_value": "", "maxlength": "", "allow_in_bindings": 0, "placeholder": "", "prepend": "Total compensated", "append": "t CO2e"}]}, {"key": "field_672791e55ad28", "label": "Conditions", "name": "conditions", "aria-label": "", "type": "repeater", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "wpml_cf_preferences": 1, "acfe_repeater_stylised_button": 0, "layout": "table", "pagination": 0, "min": 0, "max": 0, "collapsed": "", "button_label": "Add Row", "rows_per_page": 20, "sub_fields": [{"key": "field_672794d7876e3", "label": "value", "name": "value", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "wpml_cf_preferences": 2, "default_value": "", "maxlength": "", "allow_in_bindings": 0, "placeholder": "", "prepend": "", "append": "", "parent_repeater": "field_672791e55ad28"}, {"key": "field_672794de876e4", "label": "field", "name": "field", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "wpml_cf_preferences": 2, "default_value": "", "maxlength": "", "allow_in_bindings": 0, "placeholder": "", "prepend": "", "append": "", "parent_repeater": "field_672791e55ad28"}, {"key": "field_672794eb876e5", "label": "compare", "name": "compare", "aria-label": "", "type": "select", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "wpml_cf_preferences": 1, "choices": {"==": "==", "!=": "!=", ">": ">", "<": "<", ">=": ">=", "<=": "<=", "in": "in", "not-in": "not-in"}, "default_value": false, "return_format": "value", "multiple": 0, "allow_null": 0, "allow_in_bindings": 0, "ui": 0, "ajax": 0, "placeholder": "", "allow_custom": 0, "search_placeholder": "", "parent_repeater": "field_672791e55ad28"}]}], "location": [[{"param": "post_type", "operator": "==", "value": "footprint-template"}]], "menu_order": 0, "position": "acf_after_title", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "", "show_in_rest": 0, "acfe_display_title": "", "acfe_autosync": ["json"], "acfml_field_group_mode": "translation", "acfe_form": 0, "acfe_meta": "", "acfe_note": "", "modified": 1731354209}