{"key": "group_65c1032870a58", "title": "Calculation Attributes", "fields": [{"key": "field_65c10329f947b", "label": "Total emissions in tonnes", "name": "total", "aria-label": "", "type": "number", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "wpml_cf_preferences": 1, "show_in_graphql": 0, "default_value": "", "min": "", "max": "", "placeholder": "", "step": "", "prepend": "", "append": ""}, {"key": "field_6618168a3aaad", "label": "Status", "name": "status", "aria-label": "", "type": "select", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "wpml_cf_preferences": 1, "choices": {"audited": "audited", "approved": "approved", "unapproved": "unapproved", "table-based": "table-based"}, "default_value": "unapproved", "return_format": "value", "multiple": 0, "allow_null": 0, "ui": 0, "ajax": 0, "placeholder": "", "allow_custom": 0, "search_placeholder": ""}, {"key": "field_65c10372f947c", "label": "Additional", "name": "additional", "aria-label": "", "type": "wysiwyg", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "wpml_cf_preferences": 2, "show_in_graphql": 0, "default_value": "", "tabs": "all", "toolbar": "full", "media_upload": 1, "delay": 0}, {"key": "field_65c1038ef947d", "label": "File", "name": "file", "aria-label": "", "type": "file", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "wpml_cf_preferences": 1, "show_in_graphql": 0, "return_format": "array", "library": "all", "min_size": "", "max_size": "", "mime_types": "", "uploader": ""}, {"key": "field_65c103e1265e6", "label": "Order", "name": "order", "aria-label": "", "type": "post_object", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "wpml_cf_preferences": 1, "show_in_graphql": 0, "post_type": ["shop_order"], "post_status": "", "taxonomy": "", "return_format": "object", "multiple": 0, "allow_null": 0, "bidirectional": 0, "ui": 1, "bidirectional_target": [], "save_custom": 0, "save_post_type": "", "save_post_status": ""}, {"key": "field_6617fa65c83bc", "label": "Calculations", "name": "calculations", "aria-label": "", "type": "repeater", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "wpml_cf_preferences": 1, "layout": "table", "pagination": 0, "min": 0, "max": 0, "collapsed": "", "button_label": "Add Row", "rows_per_page": 20, "sub_fields": [{"key": "field_6617fa80c83bd", "label": "name", "name": "name", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "wpml_cf_preferences": 2, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": "", "parent_repeater": "field_6617fa65c83bc"}, {"key": "field_6617fa8cc83be", "label": "emissions", "name": "emissions", "aria-label": "", "type": "number", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "wpml_cf_preferences": 1, "default_value": "", "min": "", "max": "", "placeholder": "", "step": "", "prepend": "", "append": "", "parent_repeater": "field_6617fa65c83bc"}], "acfe_repeater_stylised_button": 0}, {"key": "field_668d922fba5db", "label": "Owner", "name": "owner", "aria-label": "", "type": "user", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "wpml_cf_preferences": 1, "role": "", "return_format": "array", "multiple": 0, "allow_null": 0, "bidirectional": 0, "bidirectional_target": []}, {"key": "field_668d9239ba5dc", "label": "Verified by", "name": "verified_by", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "wpml_cf_preferences": 2, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}, {"key": "field_668d92c5ba5de", "label": "compensation project certified by", "name": "compensation_project_certified_by", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "wpml_cf_preferences": 2, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}], "location": [[{"param": "post_type", "operator": "==", "value": "calculation"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "", "show_in_rest": 1, "acfe_display_title": "", "acfe_autosync": ["php", "json"], "acfe_form": 0, "acfe_meta": "", "acfe_note": "", "modified": 1738490970}