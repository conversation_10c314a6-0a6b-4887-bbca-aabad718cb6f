{"name": "sage", "private": true, "engines": {"node": ">=20.0.0"}, "type": "module", "scripts": {"dev": "vite", "start:ssr": "wp acorn inertia:start-ssr", "build": "vite build && vite build --ssr", "translate": "npm run translate:pot && npm run translate:update", "translate:pot": "wp i18n make-pot . ./resources/lang/sage.pot --include=\"theme.json,patterns,app,resources\"", "translate:update": "for file in ./resources/lang/*.po; do wp i18n update-po ./resources/lang/sage.pot $file; done", "translate:compile": "npm run translate:mo && npm run translate:js", "translate:js": "wp i18n make-json ./resources/lang --pretty-print", "translate:mo": "wp i18n make-mo ./resources/lang ./resources/lang", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "devDependencies": {"@inertiajs/react": "^2.0.8", "@inertiajs/vue3": "^2.0.8", "@storybook/addon-essentials": "^8.6.12", "@storybook/addon-interactions": "^8.6.12", "@storybook/addon-onboarding": "^8.6.12", "@storybook/blocks": "^8.6.12", "@storybook/react": "^8.6.12", "@storybook/react-vite": "^8.6.12", "@storybook/test": "^8.6.12", "@storybook/vue3": "^8.6.12", "@storybook/vue3-vite": "^8.6.12", "@types/alpinejs": "^3.13.11", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.3", "@types/react-textfit": "^1.1.4", "@vitejs/plugin-react": "^4.4.1", "@vitejs/plugin-vue": "^5.2.3", "@wordpress/dependency-extraction-webpack-plugin": "^6.22.0", "autoprefixer": "^10.4.21", "fast-glob": "^3.3.3", "laravel-vite-plugin": "^1.2.0", "postcss": "^8.5.3", "storybook": "^8.6.12", "tailwind-merge": "^3.2.0", "tailwindcss": "^3.4.17", "typescript": "^5.8.3", "vite": "^6.3.4", "vue": "^3.5.13"}, "packageManager": "pnpm@10.10.0", "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-slot": "^1.2.0", "@tanstack/react-query": "^5.76.1", "@tanstack/react-table": "^8.21.3", "alpinejs": "^3.14.8", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "html-to-image": "^1.11.13", "jspdf": "^3.0.1", "lucide-react": "^0.507.0", "motion": "^12.9.4", "react": "^19.1.0", "react-design-editor": "^0.0.61", "react-dom": "^19.1.0", "react-draggable": "^4.4.6", "react-hook-form": "^7.56.1", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-router": "^7.6.0", "react-router-dom": "^7.6.0", "react-textfit": "^1.1.1", "signature_pad": "^5.0.7", "swiper": "^11.2.6", "tw-animate-css": "^1.2.9", "zustand": "^5.0.4"}}