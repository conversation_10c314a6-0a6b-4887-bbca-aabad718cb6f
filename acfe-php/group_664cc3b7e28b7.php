<?php


if(function_exists('acf_add_local_field_group')):

    acf_add_local_field_group(array(
        'key' => 'group_664cc3b7e28b7',
        'title' => 'Template generator fields',
        'fields' => array(
            array(
                'key' => 'field_664cc3b813b24',
                'label' => 'Preview',
                'name' => 'preview',
                'aria-label' => '',
                'type' => 'image',
                'instructions' => '',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'wpml_cf_preferences' => 1,
                'return_format' => 'array',
                'library' => 'all',
                'min_width' => '',
                'min_height' => '',
                'min_size' => '',
                'max_width' => '',
                'max_height' => '',
                'max_size' => '',
                'mime_types' => '',
                'preview_size' => 'medium',
                'uploader' => '',
                'acfe_thumbnail' => 0,
            ),
            array(
                'key' => 'field_664ce1caed4ad',
                'label' => 'Json',
                'name' => 'json',
                'aria-label' => '',
                'type' => 'textarea',
                'instructions' => '',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'wpml_cf_preferences' => 2,
                'default_value' => '',
                'maxlength' => '',
                'rows' => '',
                'placeholder' => '',
                'new_lines' => '',
                'acfe_textarea_code' => 0,
            ),
            array(
                'key' => 'field_6671de5c783c1',
                'label' => 'Select fields',
                'name' => 'select_fields',
                'aria-label' => '',
                'type' => 'group',
                'instructions' => '',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'wpml_cf_preferences' => 1,
                'layout' => 'block',
                'acfe_seamless_style' => 0,
                'acfe_group_modal' => 0,
                'acfe_group_modal_close' => 0,
                'acfe_group_modal_button' => '',
                'acfe_group_modal_size' => 'large',
                'sub_fields' => array(
                    array(
                        'key' => 'field_6671de77783c2',
                        'label' => 'Main text',
                        'name' => 'main_text',
                        'aria-label' => '',
                        'type' => 'select',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => array(
                            'width' => '',
                            'class' => '',
                            'id' => '',
                        ),
                        'wpml_cf_preferences' => 1,
                        'choices' => array(
                            'We\'re carbon neutral' => 'We\'re carbon neutral',
                            'We\'re carbon negative' => 'We\'re carbon negative',
                            'Lorem Ipsum' => 'Lorem Ipsum',
                        ),
                        'default_value' => false,
                        'return_format' => 'value',
                        'multiple' => 0,
                        'allow_null' => 0,
                        'ui' => 0,
                        'ajax' => 0,
                        'placeholder' => '',
                        'allow_custom' => 0,
                        'search_placeholder' => '',
                    ),
                    array(
                        'key' => 'field_6671eb1210b3b',
                        'label' => 'test',
                        'name' => 'test',
                        'aria-label' => '',
                        'type' => 'select',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => array(
                            'width' => '',
                            'class' => '',
                            'id' => '',
                        ),
                        'wpml_cf_preferences' => 1,
                        'choices' => array(
                            'test' => 'test',
                            'lorem ipsum' => 'lorem ipsum',
                        ),
                        'default_value' => false,
                        'return_format' => 'value',
                        'multiple' => 0,
                        'allow_null' => 0,
                        'ui' => 0,
                        'ajax' => 0,
                        'placeholder' => '',
                        'allow_custom' => 0,
                        'search_placeholder' => '',
                    ),
                ),
            ),
            array(
                'key' => 'field_66857630a8819',
                'label' => 'Is preview',
                'name' => 'is_preview',
                'aria-label' => '',
                'type' => 'true_false',
                'instructions' => '',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'wpml_cf_preferences' => 1,
                'message' => '',
                'default_value' => 0,
                'ui' => 0,
                'ui_on_text' => '',
                'ui_off_text' => '',
            ),
            array(
                'key' => 'field_66b2722677fa1',
                'label' => 'variables',
                'name' => 'variables',
                'aria-label' => '',
                'type' => 'group',
                'instructions' => '',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'wpml_cf_preferences' => 1,
                'layout' => 'block',
                'acfe_seamless_style' => 0,
                'acfe_group_modal' => 0,
                'acfe_group_modal_close' => 0,
                'acfe_group_modal_button' => '',
                'acfe_group_modal_size' => 'large',
                'sub_fields' => array(
                    array(
                        'key' => 'field_66b2838177fa2',
                        'label' => 'acf.business_name',
                        'name' => 'acfbusiness_name',
                        'aria-label' => '',
                        'type' => 'text',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => array(
                            'width' => '',
                            'class' => '',
                            'id' => '',
                        ),
                        'wpml_cf_preferences' => 2,
                        'default_value' => '',
                        'maxlength' => '',
                        'placeholder' => '',
                        'prepend' => 'Company name:',
                        'append' => '',
                    ),
                    array(
                        'key' => 'field_66b283b977fa3',
                        'label' => 'acf.ghg_scope',
                        'name' => 'acfghg_scope',
                        'aria-label' => '',
                        'type' => 'text',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => array(
                            'width' => '',
                            'class' => '',
                            'id' => '',
                        ),
                        'wpml_cf_preferences' => 2,
                        'default_value' => '',
                        'maxlength' => '',
                        'placeholder' => '',
                        'prepend' => '',
                        'append' => '',
                    ),
                    array(
                        'key' => 'field_66b283ca77fa4',
                        'label' => 'acf.time',
                        'name' => 'acftime',
                        'aria-label' => '',
                        'type' => 'text',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => array(
                            'width' => '',
                            'class' => '',
                            'id' => '',
                        ),
                        'wpml_cf_preferences' => 2,
                        'default_value' => '',
                        'maxlength' => '',
                        'placeholder' => '',
                        'prepend' => '',
                        'append' => '',
                    ),
                    array(
                        'key' => 'field_66b283e277fa5',
                        'label' => 'acf.total',
                        'name' => 'acftotal',
                        'aria-label' => '',
                        'type' => 'text',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => array(
                            'width' => '',
                            'class' => '',
                            'id' => '',
                        ),
                        'wpml_cf_preferences' => 2,
                        'default_value' => '',
                        'maxlength' => '',
                        'placeholder' => '',
                        'prepend' => '',
                        'append' => '',
                    ),
                    array(
                        'key' => 'field_66b2840177fa6',
                        'label' => 'acf.verified_by',
                        'name' => 'acfverified_by',
                        'aria-label' => '',
                        'type' => 'text',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => array(
                            'width' => '',
                            'class' => '',
                            'id' => '',
                        ),
                        'wpml_cf_preferences' => 2,
                        'default_value' => '',
                        'maxlength' => '',
                        'placeholder' => '',
                        'prepend' => 'Verified by',
                        'append' => 'Ok',
                    ),
                ),
            ),
        ),
        'location' => array(
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'footprint-template',
                ),
            ),
        ),
        'menu_order' => 0,
        'position' => 'acf_after_title',
        'style' => 'default',
        'label_placement' => 'top',
        'instruction_placement' => 'label',
        'hide_on_screen' => '',
        'active' => true,
        'description' => '',
        'show_in_rest' => 0,
        'acfe_display_title' => '',
        'acfe_autosync' => array(
            0 => 'php',
        ),
        'acfml_field_group_mode' => 'translation',
        'acfe_form' => 0,
        'acfe_meta' => '',
        'acfe_note' => '',
        'modified' => 1724870623,
    ));

endif;
