<?php


if(function_exists('acf_add_local_field_group')):

    acf_add_local_field_group(array(
        'key' => 'group_668584e4c1452',
        'title' => 'Communicate file fields',
        'fields' => array(
            array(
                'key' => 'field_6685857d9cec9',
                'label' => 'Creator',
                'name' => 'creator',
                'aria-label' => '',
                'type' => 'user',
                'instructions' => '',
                'required' => false,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'wpml_cf_preferences' => 1,
                'role' => '',
                'return_format' => 'array',
                'multiple' => 0,
                'allow_null' => 0,
                'bidirectional_target' => array(
                ),
            ),
            array(
                'key' => 'field_668584f81ab17',
                'label' => 'json',
                'name' => 'json',
                'aria-label' => '',
                'type' => 'textarea',
                'instructions' => '',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'wpml_cf_preferences' => 2,
                'default_value' => '',
                'maxlength' => '',
                'rows' => '',
                'placeholder' => '',
                'new_lines' => '',
                'acfe_textarea_code' => 0,
            ),
            array(
                'key' => 'field_66859ecdbcef6',
                'label' => 'Inputs',
                'name' => 'inputs',
                'aria-label' => '',
                'type' => 'textarea',
                'instructions' => '',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'wpml_cf_preferences' => 2,
                'default_value' => '',
                'maxlength' => '',
                'rows' => '',
                'placeholder' => '',
                'new_lines' => '',
                'acfe_textarea_code' => 0,
            ),
        ),
        'location' => array(
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'communicate-file',
                ),
            ),
        ),
        'menu_order' => 0,
        'position' => 'normal',
        'style' => 'default',
        'label_placement' => 'top',
        'instruction_placement' => 'label',
        'hide_on_screen' => '',
        'active' => true,
        'description' => '',
        'show_in_rest' => 0,
        'acfe_display_title' => '',
        'acfe_autosync' => array(
            0 => 'php',
        ),
        'acfml_field_group_mode' => 'translation',
        'acfe_form' => 0,
        'acfe_meta' => '',
        'acfe_note' => '',
        'modified' => 1730644356,
    ));

endif;
