<?php


if(function_exists('acf_add_local_field_group')):

    acf_add_local_field_group(array(
        'key' => 'group_66be5eefb8f82',
        'title' => 'Offer fields',
        'fields' => array(
            array(
                'key' => 'field_66be5ef0812a5',
                'label' => 'Creator',
                'name' => 'creator',
                'aria-label' => '',
                'type' => 'user',
                'instructions' => '',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'wpml_cf_preferences' => 1,
                'role' => '',
                'return_format' => 'array',
                'multiple' => 0,
                'acfe_bidirectional' => array(
                    'acfe_bidirectional_enabled' => '0',
                ),
                'allow_null' => 0,
                'bidirectional' => 0,
                'bidirectional_target' => array(
                ),
            ),
            array(
                'key' => 'field_66be5f01812a6',
                'label' => 'Offer for',
                'name' => 'offer_for',
                'aria-label' => '',
                'type' => 'user',
                'instructions' => '',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'wpml_cf_preferences' => 1,
                'role' => '',
                'return_format' => 'array',
                'multiple' => 1,
                'acfe_bidirectional' => array(
                    'acfe_bidirectional_enabled' => '0',
                ),
                'allow_null' => 0,
                'bidirectional' => 0,
                'bidirectional_target' => array(
                ),
            ),
            array(
                'key' => 'field_66be5f12812a7',
                'label' => 'Product id',
                'name' => 'product_id',
                'aria-label' => '',
                'type' => 'text',
                'instructions' => '',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'wpml_cf_preferences' => 2,
                'default_value' => '',
                'maxlength' => '',
                'placeholder' => '',
                'prepend' => '',
                'append' => '',
            ),
            array(
                'key' => 'field_66be5f2f812a8',
                'label' => 'Message',
                'name' => 'message',
                'aria-label' => '',
                'type' => 'textarea',
                'instructions' => '',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'wpml_cf_preferences' => 2,
                'default_value' => '',
                'acfe_textarea_code' => 0,
                'maxlength' => '',
                'rows' => '',
                'placeholder' => '',
                'new_lines' => '',
            ),
            array(
                'key' => 'field_66c3b8f68f4fe',
                'label' => 'Accepted by',
                'name' => 'accepted_by',
                'aria-label' => '',
                'type' => 'user',
                'instructions' => '',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'wpml_cf_preferences' => 1,
                'role' => '',
                'return_format' => 'array',
                'multiple' => 0,
                'acfe_bidirectional' => array(
                    'acfe_bidirectional_enabled' => '0',
                ),
                'allow_null' => 0,
                'bidirectional' => 0,
                'bidirectional_target' => array(
                ),
            ),
            array(
                'key' => 'field_66c4c5d781d36',
                'label' => 'Chats',
                'name' => 'chats',
                'aria-label' => '',
                'type' => 'repeater',
                'instructions' => '',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'wpml_cf_preferences' => 1,
                'acfe_repeater_stylised_button' => 0,
                'layout' => 'table',
                'pagination' => 0,
                'min' => 0,
                'max' => 0,
                'collapsed' => '',
                'button_label' => 'Add Row',
                'rows_per_page' => 20,
                'sub_fields' => array(
                    array(
                        'key' => 'field_66c4c5e181d37',
                        'label' => 'user',
                        'name' => 'user',
                        'aria-label' => '',
                        'type' => 'user',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => array(
                            'width' => '',
                            'class' => '',
                            'id' => '',
                        ),
                        'wpml_cf_preferences' => 1,
                        'role' => '',
                        'return_format' => 'array',
                        'multiple' => 0,
                        'acfe_bidirectional' => array(
                            'acfe_bidirectional_enabled' => '0',
                        ),
                        'allow_null' => 0,
                        'bidirectional' => 0,
                        'bidirectional_target' => array(
                        ),
                        'parent_repeater' => 'field_66c4c5d781d36',
                    ),
                    array(
                        'key' => 'field_66c4c5f081d38',
                        'label' => 'chat id',
                        'name' => 'chat_id',
                        'aria-label' => '',
                        'type' => 'text',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => array(
                            'width' => '',
                            'class' => '',
                            'id' => '',
                        ),
                        'wpml_cf_preferences' => 2,
                        'default_value' => '',
                        'maxlength' => '',
                        'placeholder' => '',
                        'prepend' => '',
                        'append' => '',
                        'parent_repeater' => 'field_66c4c5d781d36',
                    ),
                ),
            ),
            array(
                'key' => 'field_66c4c63781d39',
                'label' => 'accepted',
                'name' => 'accepted',
                'aria-label' => '',
                'type' => 'user',
                'instructions' => '',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'wpml_cf_preferences' => 1,
                'role' => '',
                'return_format' => 'id',
                'multiple' => 1,
                'acfe_bidirectional' => array(
                    'acfe_bidirectional_enabled' => '0',
                ),
                'allow_null' => 0,
                'bidirectional' => 0,
                'bidirectional_target' => array(
                ),
            ),
            array(
                'key' => 'field_66c4c65c81d3b',
                'label' => 'seen',
                'name' => 'seen',
                'aria-label' => '',
                'type' => 'user',
                'instructions' => '',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'wpml_cf_preferences' => 1,
                'role' => '',
                'return_format' => 'id',
                'multiple' => 1,
                'acfe_bidirectional' => array(
                    'acfe_bidirectional_enabled' => '0',
                ),
                'allow_null' => 0,
                'bidirectional' => 0,
                'bidirectional_target' => array(
                ),
            ),
            array(
                'key' => 'field_66c4c67681d3d',
                'label' => 'declined',
                'name' => 'declined',
                'aria-label' => '',
                'type' => 'user',
                'instructions' => '',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'wpml_cf_preferences' => 1,
                'role' => '',
                'return_format' => 'id',
                'multiple' => 1,
                'acfe_bidirectional' => array(
                    'acfe_bidirectional_enabled' => '0',
                ),
                'allow_null' => 0,
                'bidirectional' => 0,
                'bidirectional_target' => array(
                ),
            ),
            array(
                'key' => 'field_66e6c88cf1a8f',
                'label' => 'Order',
                'name' => 'order',
                'aria-label' => '',
                'type' => 'text',
                'instructions' => '',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'wpml_cf_preferences' => 0,
                'default_value' => '',
                'maxlength' => '',
                'placeholder' => '',
                'prepend' => '',
                'append' => '',
            ),
            array(
                'key' => 'field_66e6c91af1a90',
                'label' => 'price',
                'name' => 'price',
                'aria-label' => '',
                'type' => 'number',
                'instructions' => '',
                'required' => false,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'wpml_cf_preferences' => 0,
                'default_value' => '',
                'min' => '',
                'max' => '',
                'step' => '',
                'placeholder' => '',
                'prepend' => '',
                'append' => '',
            ),
        ),
        'location' => array(
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'offer',
                ),
            ),
        ),
        'menu_order' => 0,
        'position' => 'normal',
        'style' => 'default',
        'label_placement' => 'left',
        'instruction_placement' => 'label',
        'hide_on_screen' => '',
        'active' => true,
        'description' => '',
        'show_in_rest' => 0,
        'acfe_display_title' => '',
        'acfe_autosync' => array(
            0 => 'php',
        ),
        'acfml_field_group_mode' => 'translation',
        'acfe_form' => 0,
        'acfe_meta' => '',
        'acfe_note' => '',
        'modified' => 1726400817,
    ));

endif;
