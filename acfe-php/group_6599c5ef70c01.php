<?php 

if( function_exists('acf_add_local_field_group') ):

acf_add_local_field_group(array(
	'key' => 'group_6599c5ef70c01',
	'title' => 'Product attributes',
	'fields' => array(
		array(
			'key' => 'field_65a6e2e46e96a',
			'label' => 'Contact persons',
			'name' => 'contact_persons',
			'aria-label' => '',
			'type' => 'user',
			'instructions' => '',
			'required' => 1,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'wpml_cf_preferences' => 0,
			'show_in_graphql' => 1,
			'role' => '',
			'return_format' => 'array',
			'multiple' => 1,
			'allow_null' => 0,
			'bidirectional' => 0,
			'bidirectional_target' => array(
			),
		),
		array(
			'key' => 'field_65aa6a0139036',
			'label' => 'Can ask for offer',
			'name' => 'can_ask_for_offer',
			'aria-label' => '',
			'type' => 'true_false',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'wpml_cf_preferences' => 0,
			'show_in_graphql' => 1,
			'message' => 'Can ask for offer?',
			'default_value' => 0,
			'ui' => 0,
			'ui_on_text' => '',
			'ui_off_text' => '',
		),
		array(
			'key' => 'field_6711612a8bd98',
			'label' => 'Country',
			'name' => 'country',
			'aria-label' => '',
			'type' => 'select',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'wpml_cf_preferences' => 2,
			'choices' => array(
				'Afghanistan' => 'Afghanistan',
				'Albania' => 'Albania',
				'Algeria' => 'Algeria',
				'Andorra' => 'Andorra',
				'Angola' => 'Angola',
				'Antigua & Deps' => 'Antigua & Deps',
				'Argentina' => 'Argentina',
				'Armenia' => 'Armenia',
				'Australia' => 'Australia',
				'Austria' => 'Austria',
				'Azerbaijan' => 'Azerbaijan',
				'Bahamas' => 'Bahamas',
				'Bahrain' => 'Bahrain',
				'Bangladesh' => 'Bangladesh',
				'Barbados' => 'Barbados',
				'Belarus' => 'Belarus',
				'Belgium' => 'Belgium',
				'Belize' => 'Belize',
				'Benin' => 'Benin',
				'Bhutan' => 'Bhutan',
				'Bolivia' => 'Bolivia',
				'Bosnia Herzegovina' => 'Bosnia Herzegovina',
				'Botswana' => 'Botswana',
				'Brazil' => 'Brazil',
				'Brunei' => 'Brunei',
				'Bulgaria' => 'Bulgaria',
				'Burkina' => 'Burkina',
				'Burundi' => 'Burundi',
				'Cambodia' => 'Cambodia',
				'Cameroon' => 'Cameroon',
				'Canada' => 'Canada',
				'Cape Verde' => 'Cape Verde',
				'Central African Rep' => 'Central African Rep',
				'Chad' => 'Chad',
				'Chile' => 'Chile',
				'China' => 'China',
				'Colombia' => 'Colombia',
				'Comoros' => 'Comoros',
				'Congo' => 'Congo',
				'Congo {Democratic Rep}' => 'Congo {Democratic Rep}',
				'Costa Rica' => 'Costa Rica',
				'Croatia' => 'Croatia',
				'Cuba' => 'Cuba',
				'Cyprus' => 'Cyprus',
				'Czech Republic' => 'Czech Republic',
				'Denmark' => 'Denmark',
				'Djibouti' => 'Djibouti',
				'Dominica' => 'Dominica',
				'Dominican Republic' => 'Dominican Republic',
				'East Timor' => 'East Timor',
				'Ecuador' => 'Ecuador',
				'Egypt' => 'Egypt',
				'El Salvador' => 'El Salvador',
				'Equatorial Guinea' => 'Equatorial Guinea',
				'Eritrea' => 'Eritrea',
				'Estonia' => 'Estonia',
				'Ethiopia' => 'Ethiopia',
				'Fiji' => 'Fiji',
				'Finland' => 'Finland',
				'France' => 'France',
				'Gabon' => 'Gabon',
				'Gambia' => 'Gambia',
				'Georgia' => 'Georgia',
				'Germany' => 'Germany',
				'Ghana' => 'Ghana',
				'Greece' => 'Greece',
				'Grenada' => 'Grenada',
				'Guatemala' => 'Guatemala',
				'Guinea' => 'Guinea',
				'Guinea-Bissau' => 'Guinea-Bissau',
				'Guyana' => 'Guyana',
				'Haiti' => 'Haiti',
				'Honduras' => 'Honduras',
				'Hungary' => 'Hungary',
				'Iceland' => 'Iceland',
				'India' => 'India',
				'Indonesia' => 'Indonesia',
				'Iran' => 'Iran',
				'Iraq' => 'Iraq',
				'Ireland {Republic}' => 'Ireland {Republic}',
				'Israel' => 'Israel',
				'Italy' => 'Italy',
				'Ivory Coast' => 'Ivory Coast',
				'Jamaica' => 'Jamaica',
				'Japan' => 'Japan',
				'Jordan' => 'Jordan',
				'Kazakhstan' => 'Kazakhstan',
				'Kenya' => 'Kenya',
				'Kiribati' => 'Kiribati',
				'Korea North' => 'Korea North',
				'Korea South' => 'Korea South',
				'Kosovo' => 'Kosovo',
				'Kuwait' => 'Kuwait',
				'Kyrgyzstan' => 'Kyrgyzstan',
				'Laos' => 'Laos',
				'Latvia' => 'Latvia',
				'Lebanon' => 'Lebanon',
				'Lesotho' => 'Lesotho',
				'Liberia' => 'Liberia',
				'Libya' => 'Libya',
				'Liechtenstein' => 'Liechtenstein',
				'Lithuania' => 'Lithuania',
				'Luxembourg' => 'Luxembourg',
				'Macedonia' => 'Macedonia',
				'Madagascar' => 'Madagascar',
				'Malawi' => 'Malawi',
				'Malaysia' => 'Malaysia',
				'Maldives' => 'Maldives',
				'Mali' => 'Mali',
				'Malta' => 'Malta',
				'Marshall Islands' => 'Marshall Islands',
				'Mauritania' => 'Mauritania',
				'Mauritius' => 'Mauritius',
				'Mexico' => 'Mexico',
				'Micronesia' => 'Micronesia',
				'Moldova' => 'Moldova',
				'Monaco' => 'Monaco',
				'Mongolia' => 'Mongolia',
				'Montenegro' => 'Montenegro',
				'Morocco' => 'Morocco',
				'Mozambique' => 'Mozambique',
				'Myanmar, {Burma}' => 'Myanmar, {Burma}',
				'Namibia' => 'Namibia',
				'Nauru' => 'Nauru',
				'Nepal' => 'Nepal',
				'Netherlands' => 'Netherlands',
				'New Zealand' => 'New Zealand',
				'Nicaragua' => 'Nicaragua',
				'Niger' => 'Niger',
				'Nigeria' => 'Nigeria',
				'Norway' => 'Norway',
				'Oman' => 'Oman',
				'Pakistan' => 'Pakistan',
				'Palau' => 'Palau',
				'Panama' => 'Panama',
				'Papua New Guinea' => 'Papua New Guinea',
				'Paraguay' => 'Paraguay',
				'Peru' => 'Peru',
				'Philippines' => 'Philippines',
				'Poland' => 'Poland',
				'Portugal' => 'Portugal',
				'Qatar' => 'Qatar',
				'Romania' => 'Romania',
				'Russian Federation' => 'Russian Federation',
				'Rwanda' => 'Rwanda',
				'St Kitts & Nevis' => 'St Kitts & Nevis',
				'St Lucia' => 'St Lucia',
				'Saint Vincent & the Grenadines' => 'Saint Vincent & the Grenadines',
				'Samoa' => 'Samoa',
				'San Marino' => 'San Marino',
				'Sao Tome & Principe' => 'Sao Tome & Principe',
				'Saudi Arabia' => 'Saudi Arabia',
				'Senegal' => 'Senegal',
				'Serbia' => 'Serbia',
				'Seychelles' => 'Seychelles',
				'Sierra Leone' => 'Sierra Leone',
				'Singapore' => 'Singapore',
				'Slovakia' => 'Slovakia',
				'Slovenia' => 'Slovenia',
				'Solomon Islands' => 'Solomon Islands',
				'Somalia' => 'Somalia',
				'South Africa' => 'South Africa',
				'South Sudan' => 'South Sudan',
				'Spain' => 'Spain',
				'Sri Lanka' => 'Sri Lanka',
				'Sudan' => 'Sudan',
				'Suriname' => 'Suriname',
				'Swaziland' => 'Swaziland',
				'Sweden' => 'Sweden',
				'Switzerland' => 'Switzerland',
				'Syria' => 'Syria',
				'Taiwan' => 'Taiwan',
				'Tajikistan' => 'Tajikistan',
				'Tanzania' => 'Tanzania',
				'Thailand' => 'Thailand',
				'Togo' => 'Togo',
				'Tonga' => 'Tonga',
				'Trinidad & Tobago' => 'Trinidad & Tobago',
				'Tunisia' => 'Tunisia',
				'Turkey' => 'Turkey',
				'Turkmenistan' => 'Turkmenistan',
				'Tuvalu' => 'Tuvalu',
				'Uganda' => 'Uganda',
				'Ukraine' => 'Ukraine',
				'United Arab Emirates' => 'United Arab Emirates',
				'United Kingdom' => 'United Kingdom',
				'United States' => 'United States',
				'Uruguay' => 'Uruguay',
				'Uzbekistan' => 'Uzbekistan',
				'Vanuatu' => 'Vanuatu',
				'Vatican City' => 'Vatican City',
				'Venezuela' => 'Venezuela',
				'Vietnam' => 'Vietnam',
				'Yemen' => 'Yemen',
				'Zambia' => 'Zambia',
				'Zimbabwe' => 'Zimbabwe',
			),
			'default_value' => false,
			'return_format' => 'value',
			'multiple' => 0,
			'allow_null' => 0,
			'allow_in_bindings' => 0,
			'ui' => 0,
			'ajax' => 0,
			'placeholder' => '',
			'allow_custom' => 0,
			'search_placeholder' => '',
		),
		array(
			'key' => 'field_65b5e8b168fc2',
			'label' => 'Offer',
			'name' => '',
			'aria-label' => '',
			'type' => 'accordion',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => array(
				array(
					array(
						'field' => 'field_65aa6a0139036',
						'operator' => '==',
						'value' => '1',
					),
				),
			),
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'wpml_cf_preferences' => 0,
			'open' => 1,
			'multi_expand' => 0,
			'endpoint' => 0,
		),
		array(
			'key' => 'field_65aa6a2a39037',
			'label' => 'Form',
			'name' => 'form',
			'aria-label' => '',
			'type' => 'post_object',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => array(
				array(
					array(
						'field' => 'field_65aa6a0139036',
						'operator' => '==',
						'value' => '1',
					),
				),
			),
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'wpml_cf_preferences' => 0,
			'show_in_graphql' => 1,
			'post_type' => array(
				0 => 'wpforms',
			),
			'post_status' => '',
			'taxonomy' => '',
			'return_format' => 'object',
			'multiple' => 0,
			'allow_null' => 0,
			'bidirectional' => 0,
			'ui' => 1,
			'bidirectional_target' => array(
			),
			'save_custom' => 0,
			'save_post_type' => '',
			'save_post_status' => '',
		),
		array(
			'key' => 'field_65aa6a8939038',
			'label' => 'For',
			'name' => 'for',
			'aria-label' => '',
			'type' => 'checkbox',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => array(
				array(
					array(
						'field' => 'field_65aa6a0139036',
						'operator' => '==',
						'value' => '1',
					),
				),
			),
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'wpml_cf_preferences' => 0,
			'show_in_graphql' => 1,
			'choices' => array(
				'vendor' => 'Vendor / product owner',
				'contact' => 'Contact persons',
				'universal' => 'For all consultants',
				'manual' => 'Chose manually',
				'group' => 'Chose group',
				'country' => 'Same country consultans',
			),
			'default_value' => array(
				0 => 'vendor',
			),
			'return_format' => 'value',
			'allow_custom' => 0,
			'layout' => 'vertical',
			'toggle' => 0,
			'save_custom' => 0,
			'custom_choice_button_text' => 'Add new choice',
		),
		array(
			'key' => 'field_65aa6b53a5267',
			'label' => 'Custom users',
			'name' => 'users',
			'aria-label' => '',
			'type' => 'user',
			'instructions' => 'Users who will get the offer',
			'required' => 0,
			'conditional_logic' => array(
				array(
					array(
						'field' => 'field_65aa6a0139036',
						'operator' => '==',
						'value' => '1',
					),
					array(
						'field' => 'field_65aa6a8939038',
						'operator' => '==',
						'value' => 'manual',
					),
				),
			),
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'wpml_cf_preferences' => 0,
			'show_in_graphql' => 1,
			'role' => '',
			'return_format' => 'array',
			'multiple' => 1,
			'allow_null' => 0,
			'bidirectional' => 0,
			'bidirectional_target' => array(
			),
		),
		array(
			'key' => 'field_678126a257758',
			'label' => 'VAT percentage',
			'name' => 'local_vat',
			'aria-label' => '',
			'type' => 'number',
			'instructions' => '',
			'required' => 1,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'wpml_cf_preferences' => 2,
			'default_value' => 24,
			'min' => 0,
			'max' => 100,
			'allow_in_bindings' => 0,
			'placeholder' => '',
			'step' => '',
			'prepend' => '',
			'append' => '',
		),
		array(
			'key' => 'field_68404bdc86cfb',
			'label' => 'Native price',
			'name' => 'native_price',
			'aria-label' => '',
			'type' => 'number',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'wpml_cf_preferences' => 0,
			'default_value' => 1,
			'min' => '',
			'max' => '',
			'allow_in_bindings' => 0,
			'placeholder' => '',
			'step' => '',
			'prepend' => '',
			'append' => '',
		),
		array(
			'key' => 'field_68404bfe86cfc',
			'label' => 'Native currency',
			'name' => 'native_currency',
			'aria-label' => '',
			'type' => 'select',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'wpml_cf_preferences' => 0,
			'choices' => array(
				'EUR' => 'EUR',
				'USD' => 'USD',
				'GBP' => 'GBP',
				'SEK' => 'SEK',
				'NOK' => 'NOK',
				'DKK' => 'DKK',
				'JPY' => 'JPY',
			),
			'default_value' => 'EUR',
			'return_format' => 'value',
			'multiple' => 0,
			'allow_null' => 0,
			'allow_in_bindings' => 0,
			'ui' => 0,
			'ajax' => 0,
			'placeholder' => '',
			'allow_custom' => 0,
			'search_placeholder' => '',
		),
		array(
			'key' => 'field_68404c2486cfd',
			'label' => 'Exchange rate at save',
			'name' => 'exchange_rate_at_save',
			'aria-label' => '',
			'type' => 'number',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'wpml_cf_preferences' => 0,
			'default_value' => '',
			'min' => '',
			'max' => '',
			'allow_in_bindings' => 0,
			'placeholder' => '',
			'step' => '',
			'prepend' => '',
			'append' => '',
		),
		array(
			'key' => 'field_68404c3986cfe',
			'label' => 'Price last updated',
			'name' => 'price_last_updated',
			'aria-label' => '',
			'type' => 'date_time_picker',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'wpml_cf_preferences' => 0,
			'display_format' => 'Y-m-d H:i:s',
			'return_format' => 'Y-m-d H:i:s',
			'first_day' => 1,
			'allow_in_bindings' => 0,
		),
	),
	'location' => array(
		array(
			array(
				'param' => 'post_type',
				'operator' => '==',
				'value' => 'product',
			),
		),
	),
	'menu_order' => 0,
	'position' => 'acf_after_title',
	'style' => 'default',
	'label_placement' => 'top',
	'instruction_placement' => 'label',
	'hide_on_screen' => '',
	'active' => true,
	'description' => '',
	'show_in_rest' => 0,
	'acfe_display_title' => '',
	'acfe_autosync' => array(
		0 => 'php',
		1 => 'json',
	),
	'acfml_field_group_mode' => 'advanced',
	'acfe_form' => 0,
	'acfe_meta' => '',
	'acfe_note' => '',
	'modified' => 1749044312,
));

endif;