<?php

if (function_exists('acf_add_local_field_group') && class_exists('WooCommerce')) :

    // Fetch all WooCommerce order statuses
    $order_statuses = wc_get_order_statuses();

    // Initialize fields array with existing fields
    $fields = array(
        array(
            'key' => 'field_66d600191f623',
            'label' => 'Rocket Chat User ID',
            'name' => 'rocket_chat_user_id',
            'type' => 'text',
            'instructions' => '',
            'required' => 0,
            'conditional_logic' => 0,
            'wrapper' => array(
                'width' => '',
                'class' => '',
                'id' => '',
            ),
            'default_value' => '',
            'maxlength' => '',
            'placeholder' => '',
            'prepend' => '',
            'append' => '',
        ),
        array(
            'key' => 'field_66d6003e1f624',
            'label' => 'Auth Token',
            'name' => 'auth_token',
            'type' => 'password',
            'instructions' => '',
            'required' => 0,
            'conditional_logic' => 0,
            'wrapper' => array(
                'width' => '',
                'class' => '',
                'id' => '',
            ),
            'default_value' => '',
            'placeholder' => '',
            'prepend' => '',
            'append' => '',
        ),
        array(
            'key' => 'field_custom_category_template',
            'label' => 'Custom Category Template',
            'name' => 'custom_category_template',
            'type' => 'post_object',
            'instructions' => 'Select a custom page to use as a template for WooCommerce product categories.',
            'required' => 0,
            'conditional_logic' => 0,
            'wrapper' => array(
                'width' => '',
                'class' => '',
                'id' => '',
            ),
            'post_type' => array('page'), // Allow selecting from pages
            'return_format' => 'id', // Return the selected page's ID
            'ui' => 1,
        ),
        array(
            'key' => 'field_custom_project_type_template',
            'label' => 'Custom Project Type Template',
            'name' => 'custom_project_type_template',
            'type' => 'post_object',
            'instructions' => 'Select a custom page to use as a template for Project Types (comp_project_type).',
            'required' => 0,
            'conditional_logic' => 0,
            'wrapper' => array(
                'width' => '',
                'class' => '',
                'id' => '',
            ),
            'post_type' => array('page'), // Allow selecting from pages
            'return_format' => 'id', // Return the selected page's ID
            'ui' => 1,
        ),

    );

    // Add Accordion Field for "Status Colors"
    $fields[] = array(
        'key' => 'field_status_colors_accordion',
        'label' => 'Status Colors',
        'name' => '',
        'type' => 'accordion',
        'instructions' => '',
        'required' => 0,
        'conditional_logic' => 0,
        'wrapper' => array(
            'width' => '',
            'class' => '',
            'id' => '',
        ),
        'open' => 0, // 0 = collapsed, 1 = open by default
        'multi_expand' => 0,
        'endpoint' => 0,
    );

    // Loop through each order status to add group fields with color pickers
    foreach ($order_statuses as $status_slug => $status_name) {
        // Normalize status slug (remove 'wc-' prefix)
        $normalized_slug = str_replace('wc-', '', $status_slug);

        // Add Group Field for each order status
        $fields[] = array(
            'key' => 'field_' . $normalized_slug . '_status_colors_group',
            'label' => $status_name . ' Colors',
            'name' => $normalized_slug . '_colors',
            'type' => 'group',
            'instructions' => 'Set the background and text colors for the "' . $status_name . '" order status.',
            'required' => 0,
            'conditional_logic' => 0,
            'wrapper' => array(
                'width' => '',
                'class' => '',
                'id' => '',
            ),
            'layout' => 'block',
            'sub_fields' => array(
                array(
                    'key' => 'field_' . $normalized_slug . '_background_color',
                    'label' => 'Background Color',
                    'name' => 'background_color',
                    'type' => 'color_picker',
                    'instructions' => 'Select the background color for the "' . $status_name . '" status.',
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '50',
                        'class' => '',
                        'id' => '',
                    ),
                    'default_value' => '',
                    'enable_opacity' => 0,
                ),
                array(
                    'key' => 'field_' . $normalized_slug . '_text_color',
                    'label' => 'Text Color',
                    'name' => 'text_color',
                    'type' => 'color_picker',
                    'instructions' => 'Select the text color for the "' . $status_name . '" status.',
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '50',
                        'class' => '',
                        'id' => '',
                    ),
                    'default_value' => '',
                    'enable_opacity' => 0,
                ),
            ),
        );
    }

    // Define the ACF field group with dynamic fields
    acf_add_local_field_group(array(
        'key' => 'group_66d60018744ee',
        'title' => 'Onyx Settings',
        'fields' => $fields, // Use the dynamically generated fields
        'location' => array(
            array(
                array(
                    'param' => 'options_page',
                    'operator' => '==',
                    'value' => 'onyx-settings',
                ),
            ),
        ),
        'menu_order' => 0,
        'position' => 'normal',
        'style' => 'default',
        'label_placement' => 'left',
        'instruction_placement' => 'label',
        'hide_on_screen' => '',
        'active' => true,
        'description' => '',
        'show_in_rest' => 0,
        'acfe_display_title' => '',
        'acfe_autosync' => array(
            0 => 'php',
        ),
        'acfml_field_group_mode' => 'translation',
        'acfe_form' => 0,
        'acfe_meta' => '',
        'acfe_note' => '',
        'modified' => time(), // Use current timestamp
    ));
endif;
