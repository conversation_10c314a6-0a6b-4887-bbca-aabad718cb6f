<?php


if(function_exists('acf_add_local_field_group')):

    acf_add_local_field_group(array(
        'key' => 'group_66be3ae6c1942',
        'title' => 'Compensation process fields',
        'fields' => array(
            array(
                'key' => 'field_66be3ae7ddf4c',
                'label' => 'Calculation',
                'name' => 'calculation',
                'aria-label' => '',
                'type' => 'post_object',
                'instructions' => '',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'wpml_cf_preferences' => 1,
                'post_type' => array(
                    0 => 'calculation',
                ),
                'post_status' => '',
                'taxonomy' => '',
                'return_format' => 'object',
                'multiple' => 0,
                'save_custom' => 0,
                'save_post_status' => 'publish',
                'acfe_bidirectional' => array(
                    'acfe_bidirectional_enabled' => '0',
                ),
                'allow_null' => 0,
                'bidirectional' => 0,
                'ui' => 1,
                'bidirectional_target' => array(
                ),
                'save_post_type' => '',
            ),
            array(
                'key' => 'field_66be3b30ddf4d',
                'label' => 'Name',
                'name' => 'name',
                'aria-label' => '',
                'type' => 'text',
                'instructions' => '',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'wpml_cf_preferences' => 2,
                'default_value' => '',
                'maxlength' => '',
                'placeholder' => '',
                'prepend' => '',
                'append' => '',
            ),
            array(
                'key' => 'field_66be4045ddf4e',
                'label' => 'Step',
                'name' => 'step',
                'aria-label' => '',
                'type' => 'select',
                'instructions' => '',
                'required' => 1,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'wpml_cf_preferences' => 1,
                'choices' => array(
                    'calculate' => 'calculate',
                    'offset' => 'offset',
                    'communicate' => 'communicate',
                    'complete' => 'complete',
                ),
                'default_value' => 'offset',
                'return_format' => 'value',
                'multiple' => 0,
                'allow_null' => 0,
                'ui' => 0,
                'ajax' => 0,
                'placeholder' => '',
                'allow_custom' => 0,
                'search_placeholder' => '',
            ),
            array(
                'key' => 'field_66be406fddf4f',
                'label' => 'Orders',
                'name' => 'orders',
                'aria-label' => '',
                'type' => 'repeater',
                'instructions' => '',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'wpml_cf_preferences' => 1,
                'acfe_repeater_stylised_button' => 0,
                'layout' => 'table',
                'pagination' => 0,
                'min' => 0,
                'max' => 0,
                'collapsed' => '',
                'button_label' => 'Add Row',
                'rows_per_page' => 20,
                'sub_fields' => array(
                    array(
                        'key' => 'field_66be56811e27f',
                        'label' => 'Order id',
                        'name' => 'order_id',
                        'aria-label' => '',
                        'type' => 'text',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => array(
                            'width' => '',
                            'class' => '',
                            'id' => '',
                        ),
                        'wpml_cf_preferences' => 2,
                        'default_value' => '',
                        'maxlength' => '',
                        'placeholder' => '',
                        'prepend' => '',
                        'append' => '',
                        'parent_repeater' => 'field_66be406fddf4f',
                    ),
                ),
            ),
            array(
                'key' => 'field_66be47e9ae622',
                'label' => 'User',
                'name' => 'user',
                'aria-label' => '',
                'type' => 'user',
                'instructions' => '',
                'required' => 1,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'wpml_cf_preferences' => 1,
                'role' => '',
                'return_format' => 'array',
                'multiple' => 0,
                'acfe_bidirectional' => array(
                    'acfe_bidirectional_enabled' => '0',
                ),
                'allow_null' => 0,
                'bidirectional' => 0,
                'bidirectional_target' => array(
                ),
            ),
            array(
                'key' => 'field_66cf783a623d9',
                'label' => 'Communicate files',
                'name' => 'communicate_files',
                'aria-label' => '',
                'type' => 'post_object',
                'instructions' => '',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'wpml_cf_preferences' => 1,
                'post_type' => array(
                    0 => 'communicate-file',
                ),
                'post_status' => '',
                'taxonomy' => '',
                'return_format' => 'object',
                'multiple' => 1,
                'save_custom' => 0,
                'save_post_status' => 'publish',
                'acfe_bidirectional' => array(
                    'acfe_bidirectional_enabled' => '0',
                ),
                'allow_null' => 0,
                'bidirectional' => 0,
                'ui' => 1,
                'bidirectional_target' => array(
                ),
                'save_post_type' => '',
            ),
        ),
        'location' => array(
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'compensation-process',
                ),
            ),
        ),
        'menu_order' => 0,
        'position' => 'normal',
        'style' => 'default',
        'label_placement' => 'left',
        'instruction_placement' => 'label',
        'hide_on_screen' => '',
        'active' => true,
        'description' => '',
        'show_in_rest' => 0,
        'acfe_display_title' => '',
        'acfe_autosync' => array(
            0 => 'php',
        ),
        'acfml_field_group_mode' => 'translation',
        'acfe_form' => 0,
        'acfe_meta' => '',
        'acfe_note' => '',
        'modified' => 1731268636,
    ));

endif;
