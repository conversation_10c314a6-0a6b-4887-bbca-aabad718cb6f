<?php

if (!class_exists('acf_field_post_value')) :

    class acf_field_post_value extends acf_field
    {
        public function initialize()
        {
            $this->name = 'post_value';
            $this->label = __('Post Value', 'acf');
            $this->category = 'relational';
            $this->defaults = array(
                'post_type' => '',
                'multiple' => 0,
                'return_format' => 'object',
                'post_field' => 'ID',
            );

            // AJAX actions
            add_action('wp_ajax_acf/fields/post_value/query', array($this, 'ajax_query'));
            add_action('wp_ajax_nopriv_acf/fields/post_value/query', array($this, 'ajax_query'));
            add_action('wp_ajax_acf/fields/post_value/get_post_fields', array($this, 'ajax_get_post_fields'));
            add_action('wp_ajax_nopriv_acf/fields/post_value/get_post_fields', array($this, 'ajax_get_post_fields'));
        }

        public function render_field($field)
        {
            // Change field into a select
            $field['type'] = 'select';
            $field['ui'] = 1;
            $field['ajax'] = 1;
            $field['choices'] = array();

            // Get selected posts
            $posts = $this->get_posts($field['value'], $field);

            if ($posts) {
                foreach ($posts as $post) {
                    $field['choices'][$post->ID] = $this->get_post_title($post, $field);
                }
            }

            // Render the field
            acf_render_field($field);
        }

        public function render_field_settings($field)
        {
            // Post Type Filter
            acf_render_field_setting($field, array(
                'label'        => __('Filter by Post Type', 'acf'),
                'instructions' => '',
                'type'         => 'select',
                'name'         => 'post_type',
                'choices'      => acf_get_pretty_post_types(),
                'multiple'     => 0,
                'ui'           => 1,
                'allow_null'   => 0,
                'placeholder'  => __('Select post type', 'acf'),
            ));

            // Return Format
            acf_render_field_setting($field, array(
                'label'        => __('Return Format', 'acf'),
                'instructions' => '',
                'type'         => 'radio',
                'name'         => 'return_format',
                'choices'      => array(
                    'object' => __('Post Object', 'acf'),
                    'id'     => __('Post ID', 'acf'),
                ),
                'layout'       => 'horizontal',
            ));

            // Select Multiple
            acf_render_field_setting($field, array(
                'label'        => __('Select Multiple', 'acf'),
                'instructions' => 'Allow content editors to select multiple values',
                'name'         => 'multiple',
                'type'         => 'true_false',
                'ui'           => 1,
            ));

            // Post Field Selection
            acf_render_field_setting($field, array(
                'label'        => __('Post Field', 'acf'),
                'instructions' => 'Select the field to retrieve from the post',
                'type'         => 'select',
                'name'         => 'post_field',
                'choices'      => $this->get_post_type_fields($field['post_type']),
                'ui'           => 1,
            ));
        }

        public function ajax_query()
        {
            // Verify nonce
            if (!acf_verify_ajax()) {
                die();
            }

            // Get posts
            $response = $this->get_ajax_query($_POST);
            acf_send_ajax_results($response);
        }

        public function get_ajax_query($options = array())
        {
            $options = acf_parse_args($options, array(
                'post_id'   => 0,
                's'         => '',
                'field_key' => '',
                'paged'     => 1,
                'include'   => '',
            ));

            // Load field
            $field = acf_get_field($options['field_key']);
            if (!$field) {
                return false;
            }

            // Vars
            $results = array();
            $args = array();
            $s = false;
            $is_search = false;

            // Paged
            $args['posts_per_page'] = 20;
            $args['paged'] = $options['paged'];

            // Search
            if ($options['s'] !== '') {
                $s = wp_unslash(strval($options['s']));
                $args['s'] = $s;
                $is_search = true;
            }

            // Post type
            if (!empty($field['post_type'])) {
                $args['post_type'] = $field['post_type'];
            } else {
                $args['post_type'] = acf_get_post_types();
            }

            // Filters
            $args = apply_filters('acf/fields/post_value/query', $args, $field, $options['post_id']);
            $args = apply_filters('acf/fields/post_value/query/name=' . $field['name'], $args, $field, $options['post_id']);
            $args = apply_filters('acf/fields/post_value/query/key=' . $field['key'], $args, $field, $options['post_id']);

            // Get posts
            $posts = get_posts($args);

            // Loop through posts
            if ($posts) {
                foreach ($posts as $post) {
                    $results[] = array(
                        'id'   => $post->ID,
                        'text' => $this->get_post_title($post, $field),
                    );
                }
            }

            // Return
            return array(
                'results' => $results,
                'limit'   => $args['posts_per_page'],
            );
        }

        public function get_post_title($post, $field, $post_id = 0, $is_search = 0, $unescape = false)
        {
            if (!$post_id) {
                $post_id = acf_get_form_data('post_id');
            }

            $title = acf_get_post_title($post, $is_search);

            if ($unescape) {
                $title = html_entity_decode($title);
            }

            return apply_filters('acf/fields/post_value/result', $title, $post, $field, $post_id);
        }

        public function get_posts($value, $field)
        {
            $value = acf_get_numeric($value);

            if (empty($value)) {
                return false;
            }

            return get_posts(array(
                'post__in' => $value,
                'post_type' => $field['post_type'],
            ));
        }

        public function get_post_type_fields($post_type)
        {
            global $wpdb;

            $fields = array(
                'ID' => __('ID', 'acf'),
                'post_author' => __('Post Author', 'acf'),
                'post_title' => __('Post Title', 'acf'),
                'post_content' => __('Post Content', 'acf'),
                'post_excerpt' => __('Post Excerpt', 'acf'),
                'post_date' => __('Post Date', 'acf'),
                'post_status' => __('Post Status', 'acf'),
            );

            // Get custom fields
            $custom_fields = $wpdb->get_results($wpdb->prepare(
                "SELECT DISTINCT meta_key FROM {$wpdb->postmeta} WHERE post_id IN (SELECT ID FROM {$wpdb->posts} WHERE post_type = %s)",
                $post_type
            ));

            if ($custom_fields) {
                foreach ($custom_fields as $field) {
                    $fields[$field->meta_key] = $field->meta_key;
                }
            }

            // Get ACF fields
            if (function_exists('acf_get_field_groups')) {
                $field_groups = acf_get_field_groups(array('post_type' => $post_type));
                if ($field_groups) {
                    foreach ($field_groups as $group) {
                        $acf_fields = acf_get_fields($group['key']);
                        if ($acf_fields) {
                            foreach ($acf_fields as $acf_field) {
                                $fields['acf.' . $acf_field['name']] = 'acf.' . $acf_field['name'];
                            }
                        }
                    }
                }
            }

            return $fields;
        }

        public function ajax_get_post_fields()
        {
            // Verify nonce
            if (!acf_verify_ajax()) {
                die();
            }

            // Get post type
            $post_type = !empty($_POST['post_type']) ? sanitize_text_field($_POST['post_type']) : 'post';

            // Get fields
            $fields = $this->get_post_type_fields($post_type);

            // Send response
            wp_send_json_success($fields);
        }
    }

// Initialize
acf_register_field_type('acf_field_post_value');

endif; // class_exists check
