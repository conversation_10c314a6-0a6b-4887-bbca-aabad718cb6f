# ACF Fields Migration

## Compensation Project Attributes

### Changes Made

1. **Available Tonnes**
   - Migrated from custom ACF field `available_tonnes` to standard WooCommerce product stock
   - Updated `get_available_tonnes()` method in `Product` model to use WooCommerce stock management
   - Kept filter functionality in project filters intact

2. **Removed Fields**
   - `webpage` - Removed getter method `get_webpage()`
   - `address` - Removed getter method `get_address()` and references in templates
   - `contact` - Removed getter method `get_contact()`
   - `external_webpage` - Removed references in templates

### Implementation Details

The `get_available_tonnes()` method now uses the standard WooCommerce product quantity:

```php
public function get_available_tonnes(): int
{
    // Use standard WooCommerce product quantity
    if ($this->is_compensation_project() && $this->managing_stock()) {
        return (int) $this->get_stock_quantity();
    }
    return 0;
}
```

### ACF Field Group Changes

When updating the ACF field group, the following fields should be removed:
- `address`
- `contact`
- `webpage` 
- `external_webpage`
- `available_tonnes` (as we now use WooCommerce stock)

### Data Migration

For existing products:
1. Ensure all compensation projects have stock management enabled
2. Migrate any values from `available_tonnes` ACF field to the product's stock quantity
3. Remove the old ACF fields 