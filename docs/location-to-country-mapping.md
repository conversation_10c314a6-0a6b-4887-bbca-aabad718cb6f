# Location to Country Field Mapping

## Overview

This document describes the migration from using `location` fields to `country` fields in the CO2Market theme.

## Changes Made

The following changes have been implemented to standardize on using `country` throughout the codebase:

1. **Product Model Updates**:
   - Replaced `public ?string $location` with `public ?string $country`
   - Updated constructor to read from `country` field instead of `location`
   - Renamed `get_location_map_url()` method to `get_country_map_url()`
   - Added `get_country()` method that returns the country with a "Global" fallback

2. **Component Updates**:
   - Updated templates to use the country field
   - Added "Global" as a fallback when country is not specified

## Data Mapping

When reimporting/migrating data:
- All data previously stored in `location` fields should be imported into `country` fields
- The `country` field should use values from the country dropdown (field_6711612a8bd98)
- The `get_all_countries()` method in the Product model provides the standard list of countries
- For global or cross-border projects, you can leave the country field empty and it will display as "Global"

## Implementation Notes

### Country Data Source

The `get_all_countries()` method retrieves country data from ACF field with key `field_6711612a8bd98`:

```php
static function get_all_countries(): array
{
    $field_key = "field_6711612a8bd98";
    $field = get_field_object($field_key);

    $countries = [];

    if ($field) {
        foreach ($field["choices"] as $key => $value) {
            array_push($countries, $value);
        }
    }
    return $countries;
}
```

### Global Fallback

The `get_country()` method provides a standardized way to access the country with a fallback:

```php
public function get_country(): string
{
    return $this->country ?: 'Global';
}
```

### Template Usage

Country field is used in template files such as:
- `single-company.blade.php` - Displays country in the address section
- Filter components for both services and projects
- Product cards display "Global" when no country is specified

## Benefits

1. **Standardization**: Using a single field for country information
2. **Consistency**: All country-related operations use the same field
3. **Better Data Quality**: Country values come from a predefined list rather than free-form text
4. **Improved UX**: Products without a specific country are consistently labeled as "Global" 