# Currency Conversion System

This document explains how the currency conversion system works in the CO2 Market platform.

## Overview

The currency conversion system allows vendors to input product prices in their native currency, which are then automatically converted to EUR (the platform's base currency) for display to customers. This system ensures that:

1. Vendors can work with their preferred currency
2. Prices are displayed consistently in EUR to customers
3. Exchange rates are tracked and protected against excessive fluctuations

## Components

### 1. ACF Fields

The following custom fields are added to each product:

- **native_price**: The price in the vendor's native currency
- **native_currency**: The currency code for the vendor's native currency
- **exchange_rate_at_save**: The exchange rate used when converting to EUR (system-managed)
- **price_last_updated**: Timestamp of when the price was last updated (system-managed)

### 2. CurrencyService

The `CurrencyService` class (`app/Services/CurrencyService.php`) handles all currency conversion operations:

- Converting between currencies
- Updating product prices
- Protecting against excessive exchange rate fluctuations
- Retrieving exchange rates from the WooCommerce Multi-Currency plugin

### 3. ProductPriceHooks

The `ProductPriceHooks` class (`app/ProductPriceHooks.php`) hooks into WordPress and WooCommerce events to automatically update prices when:

- A product is created
- A product is updated
- ACF fields are saved
- A product is duplicated

## How It Works

### Product Creation

1. Vendor enters the product price in their native currency
2. The system automatically converts this price to EUR using current exchange rates
3. The EUR price is saved as the WooCommerce product price
4. The exchange rate and timestamp are saved for future reference

### Price Updates

When a vendor updates a product's native price:

1. The system detects the change via WordPress hooks
2. The price is converted to EUR using current exchange rates
3. The WooCommerce product price is updated
4. The new exchange rate and timestamp are saved

### EUR Products

If a vendor's native currency is EUR:

1. The native price is directly used as the WooCommerce price
2. No conversion is needed
3. The timestamp is still updated for tracking purposes

### Rate Fluctuation Protection

To protect against excessive exchange rate fluctuations:

1. Current rates are compared with previous rates
2. If a rate has changed by more than 10% in a day, the previous rate is used
3. This ensures price stability for both vendors and customers

### Missing Data Handling

If exchange rates or timestamps are missing:

1. The system will fetch current exchange rates
2. New timestamps will be created
3. Prices will be updated accordingly

## Field Protection

To prevent users from manually modifying the exchange rate and timestamp fields, the following protections are in place:

### Option 1: Read-Only Fields

The exchange rate and timestamp fields are set to read-only mode with:
- Disabled inputs
- Visual styling to indicate they're system-managed
- Explanatory text

### Option 2: Hidden Fields

For enhanced protection, the fields can be completely hidden from:
- All users (by uncommenting the relevant code)
- Non-administrator users (default setting)

This ensures that only administrators can see these fields, while regular users cannot modify them.

## Manual Updates

Administrators can manually update all product prices via:

1. WooCommerce → Update Prices in the WordPress admin
2. This will update all product prices based on their native currencies and current exchange rates 