# Location to Country Migration Checklist

## Completed Changes

- [x] Updated `Product` model to use `country` property instead of `location`
- [x] Renamed `get_location_map_url()` to `get_country_map_url()`
- [x] Updated `consult-card.blade.php` component to reference `country` instead of `location`
- [x] Updated `project-product.blade.php` partial to use `get_country_map_url()` instead of `get_location_map_url()`
- [x] Updated `projects-slider.blade.php` to use country metadata
- [x] Added `get_country()` method with "Global" fallback for empty country values
- [x] Updated components to use the "Global" fallback for empty country values

## Remaining Tasks

### Database/Content Migration

- [ ] Migrate all existing product data from `location` field to `country` field
- [ ] Ensure all new products use the `country` field from the standard country list
- [ ] For global/international products, leave country blank to use the "Global" fallback

### Component Updates

- [ ] Update `project-card.blade.php` component to use prop name `country` instead of `location` in future revisions
- [ ] Review any JavaScript code that might be referencing `location` property

### Documentation

- [ ] Update developer documentation to indicate that `country` field should be used instead of `location`
- [ ] Communicate to content editors that the `country` field should be used for all geographical locations
- [ ] Explain that leaving the country field empty will display "Global" for products that are not country-specific

## Notes for Data Import

When importing data:
1. Use the standard country list provided by `Product::get_all_countries()`
2. Map any location data to the appropriate country
3. For products with multiple locations, consider:
   - Using the primary location's country
   - Leaving the country blank to display "Global"
   - Storing additional location data in a separate field if needed

## API Integration Notes

If any external API integrations depend on the `location` field:
1. Update the integration to use `country` field
2. If necessary, provide a translation layer during the transition period 