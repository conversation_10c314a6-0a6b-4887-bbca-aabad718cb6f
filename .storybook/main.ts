import { type StorybookConfig } from '@storybook/react-vite';

const config: StorybookConfig = {
  "stories": [
    "../resources/js/**/*.mdx",
    "../resources/js/**/*.stories.@(js|jsx|mjs|ts|tsx)"
  ],
  "addons": [
    {
      "name": "@storybook/addon-essentials",
      "options": {
        "docs": false
      }
    },
    "@storybook/addon-onboarding",
    "@storybook/addon-interactions"
  ],
  "framework": {
    "name": "@storybook/react-vite",
    "options": {}
  },
   staticDirs: ['../resources/images'],
  
};
export default config;