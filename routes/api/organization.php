<?php

use App\Http\Controllers\Api\OrganizationController;
use Illuminate\Support\Facades\Route;
use App\Http\Middleware\WordPress;

Route::middleware(['web', WordPress::class])->group(function () {
    Route::get('/organization', [OrganizationController::class, 'getUserOrganization'])->name('api.organization.get');
    Route::post('/organization', [OrganizationController::class, 'createOrganization'])->name('api.organization.create');
    Route::put('/organization', [OrganizationController::class, 'updateOrganization'])->name('api.organization.update');
    Route::post('/organization/users', [OrganizationController::class, 'addUserToOrganization'])->name('api.organization.add-user');
    Route::delete('/organization/users', [OrganizationController::class, 'removeUserFromOrganization'])->name('api.organization.remove-user');
});
