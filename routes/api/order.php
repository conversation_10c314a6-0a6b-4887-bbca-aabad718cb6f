<?php

use App\Http\Controllers\Api\OrderController;
use Illuminate\Support\Facades\Route;
use App\Http\Middleware\WordPress;

// Order API Routes
Route::middleware(['web', WordPress::class])->group(function () {
    // Get orders for the current logged in user
    Route::get('/orders', [OrderController::class, 'getUserOrders'])->name('api.orders.user');
    
    // Get single order by ID
    Route::get('/orders/{id}', [OrderController::class, 'getOrder'])->name('api.orders.show');
});