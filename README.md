
# WordPress Theme Project

A modern WordPress theme built with [<PERSON>](https://roots.io/sage/), [Acorn](https://roots.io/acorn/), and [Advanced Custom Fields PRO](https://www.advancedcustomfields.com/pro/).

## Requirements

- PHP >= 8.2
- Node.js >= 20.0.0
- Composer
- WordPress >= 6.0
- WP-CLI
- Advanced Custom Fields PRO plugin
- WooCommerce plugin
- WPML plugin

## Installation

1. Install WordPress and activate required plugins
2. Install theme dependencies:
   ```bash
   composer install
   pnpm install
   ```

3. Configure environment variables:
   ```bash
   wp acorn key:generate
   ```
   Add to `.env`:
   ```bash
   INERTIA_SSR_ENABLED=true
   APP_ENV=development
   ACORN_ENABLE_EXPERIMENTAL_ROUTER='true'
   APP_DEBUG=true
   APP_URL="https://${your_site}.test"
   ASSET_URL="https://${your_site}.test/wp-content/themes/co2market/public"
   ```

4. Build assets:
   ```bash
   pnpm build
   ```

## Development

Start development server:
```bash
pnpm dev
```

Start SSR server (optional):
```bash
pnpm start:ssr
```

### Creating New Blocks

To create a new ACF block:
```bash
wp acorn acf:block BLOCK_NAME
```

## Documentation

For detailed documentation, please refer to:
- [Sage Documentation](https://roots.io/sage/docs/installation/)
- [Acorn Documentation](https://roots.io/acorn/docs/installation/)

## Features

- Laravel Blade templating
- Tailwind CSS
- Vite for asset bundling
- React/Vue.js support
- WooCommerce integration
- WPML multilingual support
- Alpine.js for PHP templates/components
- Advanced Custom Fields PRO integration
- Static analysis tools (Psalm, PHP-CS-Fixer)
