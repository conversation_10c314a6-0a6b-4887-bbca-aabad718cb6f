import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';
import {
  wordpressPlugin,
  wordpressRollupPlugin,
  wordpressThemeJson,
} from './resources/js/build/wordpress';
import tailwindConfig from './tailwind.config.js';
import react from '@vitejs/plugin-react';
import fg from 'fast-glob';

const cssFiles = fg.sync('resources/css/**/*.css');

export default defineConfig({
  base: '/app/themes/sage/public/build/',
  plugins: [
    react(),
    laravel({
      input: [
        ...cssFiles,
        'resources/js/app.tsx',
        'resources/js/editor.js',
        'resources/js/components.tsx',
      ],
      ssr: 'resources/js/ssr.tsx',
      refresh: true,
    }),
    wordpressPlugin(),
    wordpressRollupPlugin(),

    // Generate the theme.json file in the public/build/assets directory
    // based on the Tailwind config and the theme.json file from base theme folder
    wordpressThemeJson({
      tailwindConfig,
      disableTailwindColors: false,
      disableTailwindFonts: false,
      disableTailwindFontSizes: false,
    }),
  ],
  resolve: {
    alias: {
      '@scripts': '/resources/js',
      '@styles': '/resources/css',
      '@fonts': '/resources/fonts',
      '@images': '/resources/images',
    },
  },
  envDir: './',
  define: {
   "import.meta.env.APP_URL": JSON.stringify(process.env.APP_URL),
  }
});

console.log('Vite config loaded', JSON.stringify(process.env.APP_URL));