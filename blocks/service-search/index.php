<?php
// Get service types from ServiceListing class
$service_types = [];
if (class_exists('\App\Livewire\Product\ServiceListing') && method_exists('\App\Livewire\Product\ServiceListing', 'get_service_types')) {
    $service_types = \App\Livewire\Product\ServiceListing::get_service_types();
}
?>

<script>
document.addEventListener('alpine:init', () => {
    Alpine.data('serviceSearch', () => ({
        countries: "",
        searchText: '',
        serviceTypes: {},
        init() {
            // Initialize service types from PHP
            <?php foreach ($service_types as $type): ?>
            this.serviceTypes['<?php echo $type['value']; ?>'] = false;
            <?php endforeach; ?>
        },
        toggleServiceType(type) {
            this.serviceTypes[type] = !this.serviceTypes[type];
        },
        getActiveServiceTypes() {
            return Object.keys(this.serviceTypes).filter(type => this.serviceTypes[type]);
        },
        submitSearch() {
            console.log('Search submitted');
            console.log('Countries:', this.countries);
            console.log('Service Types:', this.getActiveServiceTypes());
            console.log('Search Text:', this.searchText);

            const params = new URLSearchParams();

            // Add countries to URL params
            if (Array.isArray(this.countries)) {
                this.countries.forEach((country, index) => {
                    params.append(`countries[${index}]`, country);
                });
            } else if (this.countries && typeof this.countries === 'string') {
                // Handle case where countries might be a comma-separated string
                const countriesArray = this.countries.split(',').map(c => c.trim()).filter(Boolean);
                countriesArray.forEach((country, index) => {
                    params.append(`countries[${index}]`, country);
                });
            }

            // Add service types to URL params
            const activeTypes = this.getActiveServiceTypes();
            activeTypes.forEach((type, index) => {
                params.append(`types[${index}]`, type);
            });

            // Add search text if present
            if (this.searchText) {
                params.append('search', this.searchText);
            }

            // Navigate to services page with params
            const url = `/services?${params.toString()}`;
            console.log('Navigating to:', url);
            window.location.href = url;
        }
    }))
})
</script>

<div x-data="serviceSearch">

    <!-- Service Types Section -->
    <div class="max-w-3xl mx-auto mb-8">
        <h3 class="text-center text-ox-black font-bold text-xl uppercase mb-4">
            <?php echo __('Service Types', CO2MARKET_TEXT_DOMAIN); ?>
        </h3>

        <div class="flex flex-wrap justify-center gap-4">
            <?php foreach ($service_types as $type): ?>
            <button
                type="button"
                @click="toggleServiceType('<?php echo $type['value']; ?>')"
                :class="{ 'bg-ox-green-400 text-ox-green-600': serviceTypes['<?php echo $type['value']; ?>'] }"
                class="px-6 py-3 rounded-md transition-colors duration-200 hover:bg-ox-green-200 focus:outline-none">
                <?php echo $type['label']; ?>
            </button>
            <?php endforeach; ?>
        </div>
    </div>

    <!-- Search Form -->
    <form action="/services" method="get" class="max-w-3xl mx-auto">
        <div class="flex flex-col md:flex-row gap-4">
            <!-- Country Picker -->
            <div class="max-w-xs">
                <select
                    x-model="countries"
                    class="textField">
                    <option value="" disabled selected><?php echo __('Country', CO2MARKET_TEXT_DOMAIN); ?></option>
                    <?php
                    // Get countries from Product model
                    if (class_exists('\App\Models\Product') && method_exists('\App\Models\Product', 'get_all_countries')) {
                        $all_countries = \App\Models\Product::get_all_countries();
                        foreach ($all_countries as $country) {
                            echo '<option value="' . strtolower(str_replace(' ', '-', $country)) . '">' . $country . '</option>';
                        }
                    }
                    ?>
                </select>
            </div>
            <!-- Search Text Input -->
            <input
                type="text"
                id="searchText"
                placeholder="<?php echo __('Search services...', CO2MARKET_TEXT_DOMAIN); ?>"
                x-model="searchText"
                class="flex-1 px-4 py-2.5 rounded-md border-2 border-ox-green-200 focus:border-ox-green-600 focus:outline-none">

            <!-- Search Button -->
            <button
                type="submit"
                @click.prevent="submitSearch()"
                class="bg-ox-green-400 hover:bg-ox-green-500 text-ox-green-600 px-6 py-2.5 rounded-md transition-colors duration-200 focus:outline-none flex items-center justify-center">
                <?php echo __('Search', CO2MARKET_TEXT_DOMAIN); ?>
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
            </button>
        </div>
    </form>
</div>