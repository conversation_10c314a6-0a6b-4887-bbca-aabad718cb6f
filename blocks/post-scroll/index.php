<?php

/**
 * Post Scroll Block Template.
 */

// Create id attribute allowing for custom "anchor" value.
$id = 'post-scroll-' . $block['id'];
if (! empty($block['anchor'])) {
    $id = $block['anchor'];
}

// Create class attribute allowing for custom "className".
$className = 'post-scroll';
if (! empty($block['className'])) {
    $className .= ' ' . $block['className'];
}

// Query for latest 10 posts
$args = [
    'post_type' => 'post',
    'posts_per_page' => 10,
    'orderby' => 'date',
    'order' => 'DESC',
];

$latest_posts = new WP_Query($args);
?>

<div id="<?php echo esc_attr($id); ?>" class="<?php echo esc_attr($className); ?>">
    <div class="post-scroll__container">
        <?php if ($latest_posts->have_posts()) { ?>
            <div class="post-scroll__grid">
                <?php while ($latest_posts->have_posts()) {
                    $latest_posts->the_post(); ?>
                    <article class="post-card">
                        <div class="post-card__image">
                            <?php if (has_post_thumbnail()) { ?>
                                <?php the_post_thumbnail('medium_large', ['class' => 'post-card__thumbnail']); ?>
                            <?php } else { ?>
                                <div class="post-card__no-image"></div>
                            <?php } ?>
                        </div>
                        <div class="post-card__content">
                            <div class="post-card__meta">
                                <time datetime="<?php echo get_the_date('c'); ?>"><?php echo get_the_date(); ?></time>
                                <?php
                                $categories = get_the_category();
                                if ($categories) { ?>
                                    <span class="post-card__category">
                                        <?php echo esc_html($categories[0]->name); ?>
                                    </span>
                                <?php } ?>
                            </div>
                            <h3 class="post-card__title">
                                <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                            </h3>
                            <div class="post-card__excerpt">
                                <?php echo wp_trim_words(get_the_excerpt(), 20); ?>
                            </div>
                            <a href="<?php the_permalink(); ?>" class="post-card__read-more">
                                Read More
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <line x1="5" y1="12" x2="19" y2="12"></line>
                                    <polyline points="12 5 19 12 12 19"></polyline>
                                </svg>
                            </a>
                        </div>
                    </article>
                <?php } ?>
            </div>
        <?php } else { ?>
            <p class="post-scroll__no-posts">No posts found.</p>
        <?php } ?>
        <?php wp_reset_postdata(); ?>
    </div>
</div>