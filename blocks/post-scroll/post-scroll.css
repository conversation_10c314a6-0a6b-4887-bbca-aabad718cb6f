.post-scroll {
    padding: 2rem 0;
}

.post-scroll__container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.post-scroll__grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 2rem;
}

.post-card {
    background: #fff;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.post-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 12px rgba(0, 0, 0, 0.15);
}

.post-card__image {
    position: relative;
    padding-top: 60%;
    background: #f5f5f5;
}

.post-card__thumbnail {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.post-card__no-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #f5f5f5;
}

.post-card__content {
    padding: 1.5rem;
}

.post-card__meta {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
    font-size: 0.875rem;
    color: #666;
}

.post-card__category {
    background: #f0f0f0;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-weight: 500;
}

.post-card__title {
    margin: 0 0 1rem;
    font-size: 1.25rem;
    line-height: 1.4;
}

.post-card__title a {
    color: #333;
    text-decoration: none;
    transition: color 0.2s ease;
}

.post-card__title a:hover {
    color: #0066cc;
}

.post-card__excerpt {
    color: #666;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.post-card__read-more {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: #0066cc;
    text-decoration: none;
    font-weight: 500;
    transition: gap 0.2s ease;
}

.post-card__read-more:hover {
    gap: 0.75rem;
}

.post-card__read-more svg {
    transition: transform 0.2s ease;
}

.post-card__read-more:hover svg {
    transform: translateX(3px);
}

.post-scroll__no-posts {
    text-align: center;
    padding: 3rem;
    color: #666;
    font-size: 1.125rem;
}

@media (max-width: 768px) {
    .post-scroll__grid {
        grid-template-columns: 1fr;
    }
    
    .post-card__content {
        padding: 1.25rem;
    }
} 