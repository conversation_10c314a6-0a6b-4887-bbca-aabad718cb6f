{"compilerOptions": {"allowJs": true, "module": "ESNext", "moduleResolution": "bundler", "jsx": "react-jsx", "strict": true, "skipDefaultLibCheck": true, "skipLibCheck": true, "isolatedModules": true, "target": "ESNext", "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "paths": {"@/*": ["./resources/js/*"], "components/*": ["./resources/js/components/*"], "layouts": ["./resources/js/layouts/index.ts"], "ziggy-js": ["./vendor/tightenco/ziggy"], "ui": ["./resources/js/components/ui/index.ts"]}, "types": ["vite/client"]}, "include": ["resources/js/**/*.ts", "resources/js/**/*.tsx", "resources/js/**/*.d.ts", "resources/js/app.tsx", "resources/js/pages/users/index.jsx"], "exclude": ["node_modules", "public"]}