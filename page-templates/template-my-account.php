<?php
/**
 * Template Name: My Account Dashboard
 */
get_header();
?>

<div class="container mx-auto py-10">
  <div x-data="{}" x-init="() => {
    window.addEventListener('DOMContentLoaded', () => {
      window.renderMyAccount('#my-account-root');
    });
  }">
    <div id="my-account-root" class="min-h-[70vh]">
      <!-- React will render here -->
      <div class="flex justify-center items-center h-64">
        <div class="animate-spin rounded-full h-12 w-12 border-4 border-green-600 border-t-transparent"></div>
      </div>
    </div>
  </div>
</div>

<?php
// Enqueue the My Account React app
add_action('wp_footer', function() {
  wp_enqueue_script('my-account-app', get_stylesheet_directory_uri() . '/public/build/js/pages/my-account/index.js', [], filemtime(get_stylesheet_directory() . '/public/build/js/pages/my-account/index.js'), true);
  
  // Pass localized data to the script
  wp_localize_script('my-account-app', 'myAccountData', [
    'apiUrl' => rest_url('wp/v2'),
    'nonce' => wp_create_nonce('wp_rest'),
    'userId' => get_current_user_id(),
    'isLoggedIn' => is_user_logged_in(),
    'userRoles' => wp_get_current_user()->roles,
    'wpmlLanguage' => apply_filters('wpml_current_language', null),
  ]);
});

get_footer();
