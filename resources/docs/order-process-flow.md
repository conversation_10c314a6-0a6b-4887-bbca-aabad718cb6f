# CO2 Market Order Process Flow

This document outlines the order process flow in the CO2 Market platform, showing how different components interact during the ordering process.

## Order Process Flow Diagram

```mermaid
flowchart TD
    %% User Product Selection Flow
    subgraph "User Product Selection"
        A[User Selects Product] --> B{Product Type?}
        B -->|Compensation Project| C1[Standard WooCommerce Flow]
        B -->|Consultation Service| D{Purchase Type?}
        D -->|Direct Purchase| C1
        D -->|Ask for Offer| E[Create RFP]
        B -->|External Document Upload| EX1[Upload Document]
    end
    
    %% Standard WooCommerce Purchase Flow
    subgraph "Standard WooCommerce Flow"
        C1 --> C2[Add to Cart]
        C2 --> C3[View Cart]
        C3 --> F[Proceed to Checkout]
        F --> G[Complete Payment]
    end
    
    %% RFP Process Flow
    subgraph "RFP Process"
        E --> E1[Select Target Vendors]
        E1 --> E2[Fill RFP Form]
        E2 --> E3[Submit RFP]
        E3 --> E4[System Maps RFP to Vendors]
        E4 --> E5[Notify Selected Vendors]
    end
    
    %% Vendor Response Flow
    subgraph "Vendor Response"
        E5 --> H1[Vendors Review RFP]
        H1 --> H2{Vendor Decision?}
        H2 -->|Accept| H3[Create Offer]
        H2 -->|Decline| H4[Send Decline Notification]
        H3 --> H5[Submit Offer to Customer]
    end
    
    %% Customer Decision Flow
    subgraph "Customer Decision"
        H5 --> I1[Customer Reviews Offers]
        I1 --> I2{Accept Offer?}
        I2 -->|Yes| I3[Convert to Order]
        I3 --> F
        I2 -->|No| I4[Decline Offer]
        I4 --> I5[Notify Vendor]
    end
    
    %% System Processing
    subgraph "System Processing"
        G --> K[Create WooCommerce Order]
        K --> L[Update Stock/Inventory]
        K --> M[Record Carbon Footprint Data]
        K --> N[Generate Order Confirmation]
        N --> O[Send Email Notifications]
    end
    
    %% Post-Order Processing
    subgraph "Post-Order Processing"
        O --> P[Create Certificate]
        P --> Q[Update User Dashboard]
        Q --> R[Make Offset Available for Communication]
    end
    
    %% External Document Processing
    subgraph "External Document Processing"
        EX1 --> EX2[Pay Review Fee]
        EX2 --> EX3[Admin Reviews Document]
        EX3 --> EX4{Approved?}
        EX4 -->|Yes| EX5[Mark as Valid Calculation]
        EX4 -->|No| EX6[Reject Document]
        EX5 --> EX7[Set CO2e Amount]
        EX7 --> EX8[Add to Offset Process]
        EX8 --> EX9[Make Footprint Available for Communication]
    end
    
    %% Consulting Service Delivery
    subgraph "Consulting Service Delivery"
        CS1[Order Completed] --> CS2{Service Type?}
        CS2 -->|Calculate| CS3[Consultant Delivers Service]
        CS2 -->|Audit| CS4[Consultant Delivers Service]
        CS3 --> CS5[Submit Delivery Form]
        CS4 --> CS6[Submit Delivery Form]
        CS5 --> CS7[Set CO2e Amount]
        CS6 --> CS8[Mark Offset Process as Audited]
        CS7 --> CS9[Update Offset Process]
        CS8 --> CS9
        CS9 --> CS10[Make Footprint Available for Communication]
    end
    
    %% Compensation Project Processing
    subgraph "Compensation Project Processing"
        CP1[Order Placed] --> CP2{Vendor Has Contract?}
        CP2 -->|Yes| CP3[Standard Processing]
        CP2 -->|No| CP4[Order Set to On-Hold]
        CP3 --> CP5[Update Compensated Tonnes]
        CP4 --> CP6[Admin Review]
        CP6 --> CP7[Admin Completes Order]
        CP7 --> CP5
    end
    
    %% Admin Actions
    subgraph "Admin Actions"
        S[Admin Reviews Order] --> T{Status Update?}
        T -->|Completed| U[Mark Order Complete]
        T -->|Processing| V[Process Order]
        T -->|Cancelled| W[Cancel Order]
        U --> P
        
        %% Admin RFP Management
        AA[Admin RFP Dashboard] --> AB{Admin Action?}
        AB -->|Create RFP| AC[Create RFP on Behalf of Customer]
        AC --> E4
        AB -->|Modify RFP| AD[Edit RFP Details]
        AB -->|Contact Vendors| AE[Inquire Project Owners for Pricing]
        AE --> AF[Update Pricing in System]
        
        %% Admin Offer Management
        AG[Admin Offer Dashboard] --> AH{Admin Action?}
        AH -->|Create Offer| AI[Create Offer on Behalf of Vendor]
        AI --> H5
        AH -->|Modify Offer| AJ[Edit Offer Details]
        AJ --> AK[Update Product/Price/Terms]
        AH -->|Switch Project| AL[Replace Project in Offer]
        AL --> AK
    end
    
    %% Flow Connections
    K --> CS1
    K --> CP1
    G --> EX2
    O --> CS1
    
    %% Admin Intervention Points
    E3 -.-> AA
    H3 -.-> AG
    I1 -.-> AG
    CP4 -.-> S
    EX3 -.-> S
    
    %% Currency Handling
    subgraph "Currency Handling"
        CU1[Product Creation/Update] --> CU2{Currency?}
        CU2 -->|Native Currency| CU3[Save Native Price & Currency in ACF]
        CU2 -->|EUR| CU4[Save in WooCommerce]
        CU3 --> CU5[Convert to EUR]
        CU5 --> CU4
        CU4 --> CU6[Store Original Price History]
        
        CU7[Currency/Price Change] --> CU8[Save New Native Price & Currency]
        CU8 --> CU9[Archive Old Values]
        CU9 --> CU10[Convert to EUR]
        CU10 --> CU11[Update WooCommerce Price]
    end
    
    %% Data Relationships
    subgraph "Data Relationships" 
        Z1[RFP]
        Z2[Customer]
        Z3[Offset Process]
        Z4[Vendors]
        Z5[Products]
        Z6[Offers]
        
        Z1 --- Z2
        Z1 --- Z3
        Z1 --- Z4
        Z1 --- Z5
        Z6 --- Z1
        Z6 --- Z2
        Z6 --- Z3
        Z6 --- Z4
        Z6 --- Z5
    end

    style A fill:#a5e6aa,stroke:#286444
    style E fill:#a5e6aa,stroke:#286444
    style K fill:#a5e6aa,stroke:#286444
    style P fill:#a5e6aa,stroke:#286444
    style S fill:#a5e6aa,stroke:#286444
    style AA fill:#D2EBF0,stroke:#7DD2DC
    style AG fill:#D2EBF0,stroke:#7DD2DC
    style AE fill:#D2EBF0,stroke:#7DD2DC
    style AL fill:#D2EBF0,stroke:#7DD2DC
    style CU1 fill:#E1F0DC,stroke:#A5E6AA
    style CU7 fill:#E1F0DC,stroke:#A5E6AA
    style Z1 fill:#FFE7BF,stroke:#FFCD41
    style Z6 fill:#FFE7BF,stroke:#FFCD41
    style EX1 fill:#FFD6E0,stroke:#FF69B4
    style CS1 fill:#D8BFD8,stroke:#9370DB
    style CP1 fill:#FFFFE0,stroke:#FFD700
```

## Key Components

### Order Models and Controllers

1. **Order Model** (`app/Models/Order.php`)
   - Wrapper around WooCommerce order functionality
   - Handles order data representation and manipulation
   - Provides methods for order creation, updates, and deletion

2. **OrderController** (`app/Http/Controllers/Api/OrderController.php`)
   - Handles API requests related to orders
   - Provides endpoints for retrieving user orders and order details
   - Implements authentication and authorization checks

### RFP System Components

1. **RFP Model** (to be implemented)
   - Stores RFP data including:
     - Customer ID
     - Offset process ID
     - Target vendor IDs (array/multiple)
     - Target product IDs (array/multiple if applicable)
     - RFP details (requirements, timeline, budget, etc.)
     - Status (pending, sent, responded, etc.)
   - Relationships with customers, vendors, and products
   - One RFP can target multiple vendors

2. **Offer Model** (to be implemented)
   - Stores vendor offers in response to RFPs:
     - RFP ID
     - Vendor ID
     - Product ID (if applicable)
     - Pricing details
     - Timeline
     - Additional terms
     - Status (pending, accepted, declined)
   - When an offer is accepted, it can be converted to a WooCommerce order

### Currency Handling Components

1. **Product Price Management**
   - Extension to the Product model to handle multi-currency pricing
   - ACF fields to store native currency prices and currency codes
   - Price history tracking for audit and reference

2. **Currency Conversion Service**
   - Service for converting between native currencies and EUR
   - Exchange rate management and updates via WPMC (WooCommerce Multi-Currency)
   - Historical exchange rate tracking for accurate reporting
   - Laravel scheduler for daily price updates

3. **Price History Tracking**
   - System to maintain a history of price changes
   - Records both native currency and EUR values
   - Timestamps for when prices were changed

4. **Automated Price Update System**
   - Laravel scheduled task that runs every 24 hours
   - Updates WooCommerce EUR prices based on native currency prices and current exchange rates
   - Logs all automatic price adjustments
   - Sends notifications for significant price changes

### Frontend Components

1. **OrdersView** (`resources/js/pages/my-account/views/OrdersView.tsx`)
   - React component for displaying user orders
   - Uses the tanstack/react-table for data presentation
   - Handles pagination and filtering

2. **Order Item Components**
   - OrderItems.tsx, OrderNotes.tsx, OrderSummary.tsx
   - Display specific aspects of order information

3. **RFP Components** (to be implemented)
   - RFP creation form with vendor selection
   - RFP management dashboard for customers
   - Offer review interface for customers
   - RFP inbox and response interface for vendors

### Admin Components

1. **Admin RFP Management**
   - Dashboard for viewing all RFPs in the system
   - Ability to create RFPs on behalf of customers
   - Ability to modify existing RFPs
   - Interface for contacting project owners about pricing
   - Tools to update pricing information in the system

2. **Admin Offer Management**
   - Dashboard for viewing all offers in the system
   - Ability to create offers on behalf of vendors
   - Ability to modify existing offers:
     - Update pricing
     - Change terms
     - Switch products/projects when necessary
   - Ability to act as a middle-man between customers and vendors

3. **Admin Order Management**
   - Enhanced WooCommerce order management
   - Ability to modify orders derived from offers
   - Tools to track the relationship between RFPs, offers, and orders

4. **Admin Currency Management**
   - Dashboard for managing currency settings
   - Tools for updating exchange rates
   - Interface for viewing price history in multiple currencies

### Order Processing Flow

1. **Standard WooCommerce Purchase**
   - User selects product (compensation project or direct purchase consultation)
   - Adds to cart
   - Proceeds through standard WooCommerce checkout
   - Payment is processed
   - Order is created

2. **RFP-to-Order Flow**
   - User creates RFP for consultation service
   - Selects one or more target vendors
   - System maps RFP to selected vendors
   - Vendors receive notification
   - Vendors can accept (create offer) or decline
   - Customer reviews offers
   - If accepted, offer converts to WooCommerce order
   - Standard checkout process follows

3. **Admin Middle-Man Flow**
   - Admin can intervene at any point in the RFP process
   - Admin can contact compensation project owners for current pricing
   - Admin can update product prices in the system
   - Admin can switch projects in offers if original project cannot deliver
   - Admin can create/modify RFPs and offers on behalf of users
   - All changes are tracked for accountability

4. **Currency Handling Flow**
   - When a product is created/updated:
     - Native currency price is saved in ACF fields
     - System converts to EUR for WooCommerce
     - Original price information is stored for reference
   - When currency or price changes:
     - New native price and currency are saved
     - Old values are archived for historical reference
     - Updated EUR price is calculated and saved in WooCommerce
     - Price history is updated with change record
   - Automated daily updates:
     - Laravel scheduler runs every 24 hours
     - Fetches latest exchange rates from WPMC
     - Updates all WooCommerce EUR prices based on native prices
     - Records all changes in price history table

5. **Post-Order Processing**
   - Certificate generation
   - Dashboard updates
   - Communication materials preparation

6. **Consulting Service Delivery Flow**
   - When a consulting service order is completed:
     - Consultant receives notification
     - Consultant accesses delivery form
     - Form fields vary based on service type:
       - Calculate: CO2e amount, methodology, etc.
       - Audit: Verification details, findings, etc.
     - System updates associated offset process:
       - Calculate: Sets CO2e amount to be compensated
       - Audit: Marks offset process as audited
     - Customer is notified of completed delivery
     - For Calculate services: Footprint is made available for communication
   - Note: All consulting vendors have pre-established contracts with the platform

7. **Compensation Project Processing Flow**
   - When a compensation project is purchased:
     - System checks if vendor has a contract with platform
     - If contract exists:
       - Standard WooCommerce processing
       - Automatic update of compensated tonnes
     - If no contract exists:
       - Order set to "on-hold" status
       - Admin is notified
       - Admin reviews and completes order manually
       - Admin updates compensated tonnes for customer's offset process
     - Certificate is generated after completion

8. **External Document Processing Flow**
   - Customer uploads external calculation document
   - Customer pays review fee
   - Admin is notified of pending review
   - Admin reviews document
   - If approved:
     - Admin marks as valid calculation
     - Admin sets CO2e amount
     - Document is added to customer's offset process
     - Footprint is made available for communication
   - If rejected:
     - Admin provides rejection reason
     - Customer is notified
     - Customer can resubmit or request refund

9. **Service Fee and Receipt Flow**
   - When an order is placed:
     - System calculates platform service fee
     - Fee is charged based on order type and amount
   - When order status changes to "processing":
     - Sales receipt ("Tosite 1") is sent to vendor and admin for platform service fee
     - Customer receives standard processing notification
   - When order status is "processing" or "on-hold" and vendor is not valid/found:
     - Advance payment receipt is sent to customer and admin
   - When order is completed:
     - Customer receives completion notification
   - For manual payments:
     - Invoice with payment link is sent to customer
   - For new orders:
     - Admin receives new order notification
   - Vendor receives new order notification (via Dokan integration)

## Data Schema

### RFP Table
```
id: int (primary key)
customer_id: int (foreign key to users)
offset_process_id: int (nullable, foreign key to offset processes)
title: string
description: text
requirements: text
budget: decimal (nullable)
budget_currency: string (default: 'EUR')
timeline: datetime (nullable)
status: enum (draft, sent, in_progress, completed, cancelled)
admin_notes: text (for internal admin use)
created_by_admin: boolean (flag if created by admin)
admin_id: int (nullable, foreign key to admin users)
created_at: datetime
updated_at: datetime
```

### RFP_Vendor Table (for mapping RFPs to multiple vendors)
```
id: int (primary key)
rfp_id: int (foreign key to rfps)
vendor_id: int (foreign key to vendors/users)
status: enum (pending, viewed, declined, responded)
admin_notes: text (for internal admin use)
created_at: datetime
updated_at: datetime
```

### RFP_Product Table (for mapping RFPs to specific products)
```
id: int (primary key)
rfp_id: int (foreign key to rfps)
product_id: int (foreign key to products)
created_at: datetime
updated_at: datetime
```

### Offer Table
```
id: int (primary key)
rfp_id: int (foreign key to rfps)
vendor_id: int (foreign key to vendors/users)
product_id: int (nullable, foreign key to products)
price: decimal
price_currency: string (default: 'EUR')
native_price: decimal (nullable)
native_currency: string (nullable)
description: text
terms: text
timeline: datetime (nullable)
status: enum (pending, accepted, declined)
order_id: int (nullable, foreign key to orders - populated when accepted)
admin_notes: text (for internal admin use)
created_by_admin: boolean (flag if created by admin)
admin_id: int (nullable, foreign key to admin users)
original_product_id: int (nullable, tracks product switches)
created_at: datetime
updated_at: datetime
```

### Product Price ACF Fields
```
native_price: decimal (price in vendor's native currency)
native_currency: string (ISO currency code)
exchange_rate_at_save: decimal (exchange rate when price was saved)
price_last_updated: datetime
```

### Price History Table
```
id: int (primary key)
product_id: int (foreign key to products)
woo_price_eur: decimal (WooCommerce price in EUR)
native_price: decimal (price in native currency)
native_currency: string (ISO currency code)
exchange_rate: decimal (exchange rate at time of change)
changed_by_user_id: int (foreign key to users)
change_reason: text (nullable)
created_at: datetime
```

### Audit Log Table
```
id: int (primary key)
entity_type: enum (rfp, offer, order, product)
entity_id: int (foreign key to respective entity)
user_id: int (foreign key to users)
user_type: enum (admin, vendor, customer)
action: string (create, update, delete, etc.)
field_changed: string (nullable)
old_value: text (nullable)
new_value: text (nullable)
created_at: datetime
```

### Consulting Service Delivery Table
```
id: int (primary key)
order_id: int (foreign key to orders)
consultant_id: int (foreign key to users)
service_type: enum (calculate, audit)
delivery_status: enum (pending, delivered, rejected)
co2e_amount: decimal (nullable, for calculate type)
methodology: text (nullable, for calculate type)
offset_process_id: int (foreign key to offset processes)
delivery_notes: text
admin_notes: text (nullable)
admin_verified: boolean (default: false)
admin_id: int (nullable, foreign key to admin users)
delivery_date: datetime (nullable)
created_at: datetime
updated_at: datetime
```

### Vendor Contract Table
```
id: int (primary key)
vendor_id: int (foreign key to users)
contract_status: enum (pending, active, expired, terminated)
terms_accepted: boolean
terms_accepted_date: datetime (nullable)
contract_start_date: datetime (nullable)
contract_end_date: datetime (nullable)
admin_notes: text (nullable)
created_at: datetime
updated_at: datetime
```

### External Document Table
```
id: int (primary key)
customer_id: int (foreign key to users)
document_type: enum (calculation, certificate, report)
file_path: string
status: enum (pending_payment, pending_review, approved, rejected)
review_fee_paid: boolean (default: false)
payment_id: int (nullable, foreign key to payments)
co2e_amount: decimal (nullable)
admin_id: int (nullable, foreign key to admin users)
admin_notes: text (nullable)
rejection_reason: text (nullable)
review_date: datetime (nullable)
offset_process_id: int (nullable, foreign key to offset processes)
created_at: datetime
updated_at: datetime
```

### Service Fee Table
```
id: int (primary key)
order_id: int (foreign key to orders)
fee_type: enum (platform_fee, review_fee, other)
amount: decimal
currency: string (default: 'EUR')
status: enum (pending, charged, refunded)
receipt_sent: boolean (default: false)
receipt_number: string (nullable)
receipt_date: datetime (nullable)
created_at: datetime
updated_at: datetime
```

## Integration Points

1. **WooCommerce Integration**
   - The platform uses WooCommerce for the core e-commerce functionality
   - Custom fields extend WooCommerce to support carbon offset specifics
   - Offers can be converted to WooCommerce orders when accepted
   - WooCommerce stores all prices in EUR for consistency
   - WooCommerce Multi-Currency (WPMC) plugin provides exchange rates

2. **ACF Field Integration**
   - Various ACF fields link orders to carbon footprint calculations
   - Fields store compensation project details and metrics
   - ACF fields store native currency prices and currency codes
   - ACF fields track price history and currency changes

3. **User Dashboard Integration**
   - React components display order information in the user dashboard
   - API endpoints provide order data to the frontend
   - New RFP and Offer management interfaces for both customers and vendors
   - Price display components show prices in both EUR and native currencies

4. **WordPress Admin Integration**
   - Custom admin pages for RFP management
   - Custom admin pages for Offer management
   - Enhanced order management screens with RFP/Offer context
   - Admin tools for price inquiries and project switching
   - Audit logging for admin actions
   - Currency management and price history tools

5. **Laravel Scheduler Integration**
   - Utilizes Laravel's scheduler for automated tasks
   - Configured to run daily price update commands
   - Integrated with WPMC for exchange rate data
   - Logs execution results and any errors

6. **WooCommerce Order Status Integration**
   - Custom order statuses for specific product types
   - Order status hooks for triggering service delivery workflows
   - Integration with vendor contract verification system
   - Custom admin order actions for compensation projects without contracts

7. **Consultant Dashboard Integration**
   - Service delivery interface
   - Delivery form with dynamic fields based on service type
   - Delivery status tracking
   - Notification system for pending and completed deliveries

8. **Document Management Integration**
   - Secure document upload and storage
   - Document review workflow
   - Payment integration for review fees
   - Document status tracking and notifications

9. **Email Notification System**
   - Custom email templates for different order statuses and events
   - Integration with WooCommerce email system
   - Dokan marketplace integration for vendor notifications
   - Receipt generation and distribution

## Email Notification System

The platform uses a combination of standard WooCommerce emails (some overridden) and custom email templates to handle various notifications and receipts throughout the order process.

### Custom Email Templates

1. **Sales Method B Emails** (`sales-method-b-emails.php`)
   - Triggered when order status changes to "processing"
   - Sends "Tosite 1" (myyntitosite) receipt to vendor and admin
   - Contains platform service fee details
   - Implementation:
   ```php
   // Trigger for sales-method-b-emails.php
   add_action('woocommerce_order_status_changed', function($order_id, $from_status, $to_status) {
       if ($to_status === 'processing') {
           do_action('co2market_send_sales_method_b_email', $order_id);
       }
   }, 10, 3);
   ```

2. **Advance Payment Receipt** (`send-advance-payment-receipt.php`)
   - Triggered when order status is "processing" or "on-hold" and vendor is not valid/found
   - Sends advance payment receipt to customer and admin
   - Implementation:
   ```php
   // Trigger for send-advance-payment-receipt.php
   add_action('woocommerce_order_status_changed', function($order_id, $from_status, $to_status) {
       if (in_array($to_status, ['processing', 'on-hold'])) {
           $order = wc_get_order($order_id);
           $vendor_valid = co2market_check_vendor_validity($order);
           
           if (!$vendor_valid) {
               do_action('co2market_send_advance_payment_receipt', $order_id);
           }
       }
   }, 10, 3);
   ```

### Overridden WooCommerce Email Templates

1. **Customer Processing Order** (`customer-processing-order.php`)
   - Standard WooCommerce template, overridden for customization
   - Notifies customer their order is being processed

2. **Customer Completed Order** (`customer-completed-order.php`)
   - Standard WooCommerce template, overridden for customization
   - Notifies customer their order is complete

3. **Customer Invoice** (`customer-invoice.php`)
   - Standard WooCommerce template, overridden for customization
   - Sends invoice/payment link to customer for manual payments

4. **Admin New Order** (`admin-new-order.php`)
   - Standard WooCommerce template, overridden for customization
   - Notifies admin of a new order

### Marketplace/Vendor Email (Dokan Integration)

1. **Vendor New Order** (`../dokan/emails/vendor-new-order.php`)
   - Dokan marketplace template, overridden for customization
   - Notifies vendor of a new order

### Email Configuration

```php
// app/Services/EmailService.php

class EmailService
{
    /**
     * Register custom email templates
     */
    public function registerCustomEmails($emails)
    {
        // Add custom email classes
        $emails['CO2Market_Sales_Method_B_Email'] = new CO2Market_Sales_Method_B_Email();
        $emails['CO2Market_Advance_Payment_Receipt'] = new CO2Market_Advance_Payment_Receipt();
        
        return $emails;
    }
    
    /**
     * Send service fee receipt
     */
    public function sendServiceFeeReceipt($order_id)
    {
        $order = wc_get_order($order_id);
        $vendor_id = co2market_get_order_vendor_id($order);
        $vendor = get_user_by('id', $vendor_id);
        
        if (!$vendor) {
            return false;
        }
        
        $service_fee = co2market_calculate_service_fee($order);
        
        // Record service fee
        $this->recordServiceFee($order_id, $service_fee);
        
        // Generate receipt number
        $receipt_number = 'TOSITE1-' . date('Ymd') . '-' . $order_id;
        
        // Send email
        WC()->mailer()->get_emails()['CO2Market_Sales_Method_B_Email']->trigger(
            $order_id, 
            $vendor->user_email,
            $service_fee,
            $receipt_number
        );
        
        // Also send to admin
        WC()->mailer()->get_emails()['CO2Market_Sales_Method_B_Email']->trigger(
            $order_id, 
            get_option('admin_email'),
            $service_fee,
            $receipt_number
        );
        
        // Update service fee record
        $this->updateServiceFeeReceipt($order_id, $receipt_number);
        
        return true;
    }
    
    /**
     * Record service fee
     */
    private function recordServiceFee($order_id, $amount)
    {
        global $wpdb;
        
        return $wpdb->insert(
            $wpdb->prefix . 'co2market_service_fees',
            [
                'order_id' => $order_id,
                'fee_type' => 'platform_fee',
                'amount' => $amount,
                'currency' => 'EUR',
                'status' => 'charged',
                'created_at' => current_time('mysql'),
                'updated_at' => current_time('mysql'),
            ]
        );
    }
    
    /**
     * Update service fee receipt info
     */
    private function updateServiceFeeReceipt($order_id, $receipt_number)
    {
        global $wpdb;
        
        return $wpdb->update(
            $wpdb->prefix . 'co2market_service_fees',
            [
                'receipt_sent' => true,
                'receipt_number' => $receipt_number,
                'receipt_date' => current_time('mysql'),
                'updated_at' => current_time('mysql'),
            ],
            ['order_id' => $order_id]
        );
    }
    
    /**
     * Make footprint available for communication
     */
    public function makeFootprintAvailableForCommunication($offsetProcessId)
    {
        $offsetProcess = OffsetProcess::find($offsetProcessId);
        
        if (!$offsetProcess || !$offsetProcess->co2e_amount) {
            return false;
        }
        
        // Update offset process status
        $offsetProcess->update([
            'communication_status' => 'available',
            'communication_available_date' => current_time('mysql'),
        ]);
        
        // Generate communication assets
        $communicationService = new CommunicationService();
        $communicationService->generateCommunicationAssets($offsetProcess);
        
        // Notify customer
        $customer = User::find($offsetProcess->customer_id);
        if ($customer) {
            WC()->mailer()->get_emails()['CO2Market_Footprint_Available']->trigger(
                $offsetProcess->id,
                $customer->user_email
            );
        }
        
        return true;
    }
}
```

### Email Templates Location

```
themes/co2market/
├── resources/
│   ├── views/
│   │   ├── emails/
│   │   │   ├── sales-method-b-emails.php
│   │   │   ├── send-advance-payment-receipt.php
│   │   │   ├── customer-processing-order.php
│   │   │   ├── customer-completed-order.php
│   │   │   ├── customer-invoice.php
│   │   │   └── admin-new-order.php
│   │   └── dokan/
│   │       └── emails/
│   │           └── vendor-new-order.php
```

## Email Notification Summary

| Order State/Event | Custom Functionality | File(s) | Recipient(s) |
|-------------------|---------------------|---------|--------------|
| processing | Yes | sales-method-b-emails.php | Vendor, Admin |
| processing, on-hold | Yes | send-advance-payment-receipt.php | Customer, Admin |
| processing | No (Overridden) | customer-processing-order.php | Customer |
| completed | No (Overridden) | customer-completed-order.php | Customer |
| invoice/manual payment | No (Overridden) | customer-invoice.php | Customer |
| new order (admin) | No (Overridden) | admin-new-order.php | Admin |
| new order (vendor) | No (Overridden) | ../dokan/emails/vendor-new-order.php | Vendor |

## Receipt Logic

The platform implements a sophisticated receipt system that handles different scenarios based on:
1. The sales method (Myyntitapa A or B)
2. The vendor's location (within or outside EU/EEA)
3. The WooCommerce order status

### Receipt Types and Triggers

```mermaid
flowchart TD
    %% Order Status Triggers
    OS1[Order Status Changed] --> OS2{Which Status?}
    OS2 -->|Processing| PR1[Check Sales Method]
    OS2 -->|On-Hold| OH1[Check Vendor Validity]
    OS2 -->|Completed| CP1[Send Completion Receipt]
    
    %% Processing Status Logic
    PR1 -->|Myyntitapa A<br>No Contract| PR2[Send Advance Payment Receipt]
    PR1 -->|Myyntitapa B<br>Contract Exists| PR3{Vendor Location?}
    
    %% Myyntitapa B - EU/EEA Logic
    PR3 -->|Finland| PR4[Send Tosite 1<br>With VAT 25.5%]
    PR3 -->|Other EU/EEA| PR5[Send Tosite 1<br>Reverse Charge]
    PR3 -->|Outside EU/EEA| PR6[Send Tosite 1<br>VAT 0% Export]
    
    %% On-Hold Status Logic
    OH1 -->|Valid Vendor| OH2[No Receipt Needed]
    OH1 -->|No Valid Vendor| OH3[Send Advance Payment Receipt]
    
    %% Consulting Service Logic
    CS1[Consulting Service Order] --> CS2[Always Myyntitapa B<br>Contract Exists]
    CS2 --> PR3
    
    %% Compensation Project Logic
    CP2[Compensation Project Order] --> CP3{Vendor Has Contract?}
    CP3 -->|Yes| CP4[Myyntitapa B]
    CP3 -->|No| CP5[Myyntitapa A]
    CP4 --> PR3
    CP5 --> PR2
    
    %% External Document Logic
    ED1[External Document<br>Review Fee] --> ED2[Always Myyntitapa B]
    ED2 --> PR3
    
    %% Styling
    style OS1 fill:#a5e6aa,stroke:#286444
    style PR1 fill:#D2EBF0,stroke:#7DD2DC
    style PR3 fill:#D2EBF0,stroke:#7DD2DC
    style OH1 fill:#D2EBF0,stroke:#7DD2DC
    style CS1 fill:#D8BFD8,stroke:#9370DB
    style CP2 fill:#FFFFE0,stroke:#FFD700
    style ED1 fill:#FFD6E0,stroke:#FF69B4
```

### Sales Method Definitions

1. **Myyntitapa A (Sales Method A / Purchase Order / Ostotoimeksianto)**
   - No contract exists between the vendor and the platform (alustalla ei ole sopimusta myyjän kanssa)
   - Used for compensation projects where the vendor has not accepted terms
   - Used for external document review fees
   - Platform acts as an intermediary (välittäjä)
   - Three receipts are provided:
     1. **Pre-payment Summary Receipt** - For the full amount in the customer's trading currency (e.g., 110 USD)
     2. **Tosite 1 (Service Fee Receipt)** - After transaction completion, showing both trading currency and accounting currency (e.g., 110 USD / 100 EUR)
     3. **Tosite 2A (Product Receipt)** - Between seller and buyer, including seller's VAT ID, in seller's currency with exchange rate information

2. **Myyntitapa B (Sales Method B / Purchase)**
   - Contract exists between the vendor and the platform
   - Used for all consulting services (consultants always have contracts)
   - Used for compensation projects where the vendor has accepted terms
   - Two receipts are provided:
     1. **Tosite 1 (Service Fee Receipt)** - In customer's trading currency, also showing seller's currency and exchange rate (same receipt is sent to vendor)
     2. **Tosite 2B (Product Receipt)** - Sent to vendor only, always in EUR

### Receipt Types by Order Status

#### Processing Status

1. **Myyntitapa A - Pre-payment Summary Receipt**
   - Sent when order status changes to "processing"
   - No valid vendor found (no contract/terms accepted)
   - Recipients: Customer and Admin
   - Content: Pre-payment summary receipt in customer's trading currency (VAT 0%)
   - Implementation: `send-advance-payment-receipt.php`

2. **Myyntitapa B - Tosite 1 Receipt**
   - Sent when order status changes to "processing"
   - Valid vendor with accepted terms
   - Recipients: Vendor and Admin
   - Content varies by vendor location:
     - Finland: Service fee with VAT 25.5%
     - Other EU/EEA: Service fee with Reverse Charge (VAT Directive art 44)
     - Outside EU/EEA: Service fee with VAT 0% (Export of goods)
   - Implementation: `sales-method-b-emails.php`

3. **Myyntitapa B - Tosite 2B Receipt**
   - Sent when order status changes to "processing"
   - Valid vendor with accepted terms
   - Recipients: Vendor only
   - Content: Product receipt in EUR
   - Implementation: `sales-method-b-product-receipt.php`

#### On-Hold Status

1. **Pre-payment Summary Receipt**
   - Sent when order status changes to "on-hold"
   - No valid vendor found
   - Same content as Processing status Pre-payment Summary Receipt
   - Implementation: `send-advance-payment-receipt.php`

#### Completed Status (Myyntitapa A only)

1. **Tosite 1 (Service Fee Receipt)**
   - Sent when order status changes to "completed" for Myyntitapa A orders
   - Shows both trading currency and accounting currency (e.g., 110 USD / 100 EUR)
   - The EUR amount is the official amount for platform's accounting per Finnish law
   - Recipients: Customer and Admin

2. **Tosite 2A (Product Receipt)**
   - Sent when order status changes to "completed" for Myyntitapa A orders
   - Contains seller's VAT ID (e.g., FI12345679)
   - Shows amount in seller's currency (e.g., 100,000 INR)
   - Includes exchange rate information (e.g., INR-USD)
   - May optionally show converted amount in customer's currency
   - Recipients: Customer and Admin

### Product Type Specific Receipt Logic

1. **Consulting Services**
   - Always uses Myyntitapa B (all consultants have contracts)
   - Customer and vendor receive Tosite 1 (Service Fee Receipt) in customer's trading currency
   - Vendor receives additional Tosite 2B (Product Receipt) in EUR
   - VAT handling based on vendor location:
     - Finland: 25.5% VAT
     - Other EU/EEA: Reverse Charge
     - Outside EU/EEA: VAT 0% Export

2. **Compensation Projects**
   - Uses Myyntitapa B if vendor has contract (accepted terms)
     - Customer and vendor receive Tosite 1 (Service Fee Receipt) in customer's trading currency
     - Vendor receives additional Tosite 2B (Product Receipt) in EUR
   - Uses Myyntitapa A if vendor has no contract
     - Initial Pre-payment Summary Receipt to customer in trading currency
     - After completion: Tosite 1 (Service Fee Receipt) and Tosite 2A (Product Receipt)
     - Seller's VAT ID must be verified before final receipts are issued

3. **External Document Review**
   - Always uses Myyntitapa B
   - Customer and vendor receive Tosite 1 (Service Fee Receipt) in customer's trading currency
   - VAT handling based on vendor location:
     - Finland: 25.5% VAT
     - Other EU/EEA: Reverse Charge
     - Outside EU/EEA: VAT 0% Export

### Implementation Details

```php
// Myyntitapa B (Purchase) - Tosite 1 and Tosite 2B Receipts
function sales_method_b_send_receipts($order_id, $order)
{
    // Check if order exists
    if (!$order) return;

    // Define EU/ETA country list
    $eu_eta_countries = [
        'Austria', 'Belgium', 'Bulgaria', 'Croatia', 'Cyprus', 'Czech Republic', 'Denmark', 'Estonia',
        'Finland', 'France', 'Germany', 'Greece', 'Hungary', 'Iceland', 'Ireland', 'Italy',
        'Latvia', 'Liechtenstein', 'Lithuania', 'Luxembourg', 'Malta', 'Netherlands', 'Norway',
        'Poland', 'Portugal', 'Romania', 'Slovakia', 'Slovenia', 'Spain', 'Sweden', 'Switzerland'
    ];

    // Get service fee from order
    $service_fee = get_order_service_fee($order);
    $customer_currency = $order->get_currency();

    foreach ($order->get_items() as $item) {
        $product_id = $item->get_product_id();
        $vendor_id = get_post_field('post_author', $product_id);
        $vendor = dokan()->vendor->get($vendor_id);

        if ($vendor) {
            // Get vendor details
            $vendor_details = get_vendor_details($vendor);
            $vendor_currency = get_vendor_currency($vendor);
            $exchange_rate = get_exchange_rate($customer_currency, $vendor_currency);
            
            // 1. Generate Tosite 1 (Service Fee Receipt) content
            $tosite_1_content = generate_tosite_1_content(
                $vendor_details, 
                $service_fee, 
                $eu_eta_countries,
                $customer_currency,
                $vendor_currency,
                $exchange_rate
            );
            
            // Send Tosite 1 to customer, vendor and admin
            $recipients = [
                $vendor->get_email(), 
                $order->get_billing_email(),
                get_option('admin_email')
            ];
            
            foreach ($recipients as $recipient) {
                wp_mail($recipient, 'CO2Market: Service Fee Receipt (Tosite 1)', $tosite_1_content);
            }
            
            // 2. Generate Tosite 2B (Product Receipt) content
            $tosite_2b_content = generate_tosite_2b_content(
                $vendor_details,
                $order,
                'EUR'
            );
            
            // Send Tosite 2B to vendor only
            wp_mail($vendor->get_email(), 'CO2Market: Product Receipt (Tosite 2B)', $tosite_2b_content);
        }
    }
}

// Myyntitapa A (Purchase Order) - Pre-payment Summary Receipt
function send_prepayment_summary_receipt($order_id, $order)
{
    // Check if order exists
    if (!$order) return;

    // Check if any valid vendor exists for this order
    $has_valid_vendor = check_for_valid_vendor($order);
    
    // If valid vendor exists, don't send pre-payment summary receipt
    if ($has_valid_vendor) return;
    
    // Get order totals and customer currency
    $order_totals = calculate_order_totals($order);
    $customer_currency = $order->get_currency();
    
    // Generate receipt content
    $email_content = generate_prepayment_summary_content($order_totals, $customer_currency);
    
    // Send to customer and admin
    $recipients = [$order->get_billing_email(), get_option('admin_email')];
    foreach ($recipients as $recipient) {
        wp_mail($recipient, 'CO2Market: Pre-payment Summary Receipt', $email_content);
    }
}

// Myyntitapa A (Purchase Order) - Final Receipts on Completion
function send_myyntitapa_a_completion_receipts($order_id, $order)
{
    // Check if order exists and is completed
    if (!$order || $order->get_status() !== 'completed') return;
    
    // Get order data
    $customer_currency = $order->get_currency();
    $order_total = $order->get_total();
    $service_fee = get_order_service_fee($order);
    $service_amount = $order_total - $service_fee;
    
    // Get vendor and seller currency
    $vendor_id = get_order_vendor_id($order);
    $vendor = dokan()->vendor->get($vendor_id);
    $seller_currency = get_vendor_currency($vendor);
    $seller_vat_id = get_vendor_vat_id($vendor);
    
    // Calculate exchange rates
    $customer_to_eur_rate = get_exchange_rate($customer_currency, 'EUR');
    $customer_to_seller_rate = get_exchange_rate($customer_currency, $seller_currency);
    
    // 1. Generate Tosite 1 (Service Fee Receipt) content
    $tosite_1_content = generate_tosite_1_content(
        $service_fee,
        $customer_currency,
        $service_fee * $customer_to_eur_rate,
        'EUR',
        $customer_to_eur_rate
    );
    
    // 2. Generate Tosite 2A (Product Receipt) content
    $tosite_2a_content = generate_tosite_2a_content(
        $service_amount,
        $customer_currency,
        $service_amount * $customer_to_seller_rate,
        $seller_currency,
        $customer_to_seller_rate,
        $seller_vat_id
    );
    
    // Send both receipts to customer and admin
    $recipients = [$order->get_billing_email(), get_option('admin_email')];
    foreach ($recipients as $recipient) {
        wp_mail($recipient, 'CO2Market: Service Fee Receipt (Tosite 1)', $tosite_1_content);
        wp_mail($recipient, 'CO2Market: Product Receipt (Tosite 2A)', $tosite_2a_content);
    }
}
```

### Receipt Logic Decision Tree

1. **Order Received**
   - Check product type:
     - Consulting Service → Myyntitapa B (Purchase)
     - Compensation Project → Check vendor contract status
     - External Document → Myyntitapa B (Purchase)

2. **For Compensation Projects**
   - If vendor has accepted terms → Myyntitapa B (Purchase)
   - If vendor has not accepted terms → Myyntitapa A (Purchase Order)

3. **Order Status Changes to Processing**
   - If Myyntitapa A → Send Pre-payment Summary Receipt in customer's trading currency
   - If Myyntitapa B → Send Tosite 1 (Service Fee Receipt) to customer and vendor; Send Tosite 2B (Product Receipt) to vendor

4. **Order Status Changes to On-Hold**
   - Check if valid vendor exists:
     - No valid vendor → Send Pre-payment Summary Receipt
     - Valid vendor → No receipt needed at this stage

5. **Order Status Changes to Completed (Myyntitapa A only)**
   - Verify seller's VAT ID
   - Send Tosite 1 (Service Fee Receipt) showing both trading and accounting currency
   - Send Tosite 2A (Product Receipt) in seller's currency with exchange rate

## Methodological Verification of Assumptions

### Assumption 1: WooCommerce stores prices in EUR
- **Verification**: WooCommerce by default stores prices in a single base currency, which in this case is configured as EUR.
- **Implementation**: All product prices in the WooCommerce database are stored in EUR, maintaining consistency across the platform.
- **Validation**: This assumption is valid and aligned with WooCommerce's single-currency architecture.

### Assumption 2: Native currency prices should be stored in ACF
- **Verification**: ACF provides flexible custom fields that can store additional pricing information.
- **Implementation**: 
  - Create ACF fields for `native_price` and `native_currency`
  - These fields are saved alongside the standard WooCommerce price
  - The Product model handles both sets of pricing data
- **Validation**: This approach preserves both the native pricing and the standardized EUR pricing.

### Assumption 3: Price history needs to be maintained
- **Verification**: For audit and reference purposes, a history of price changes is necessary.
- **Implementation**: 
  - Create a dedicated `price_history` table
  - Record both native and EUR prices with each change
  - Include exchange rates used for conversion
  - Track who made the change and when
- **Validation**: This approach ensures transparency and maintains a complete audit trail of pricing changes.

### Assumption 4: Currency conversion is needed
- **Verification**: To convert between native currencies and EUR, a conversion mechanism is required.
- **Implementation**:
  - Create a Currency Conversion Service
  - Use WPMC (WooCommerce Multi-Currency) plugin for exchange rates
  - Store the exchange rate used with each price conversion
  - Update rates periodically via Laravel scheduler (every 24 hours)
- **Validation**: This ensures accurate conversion between currencies while maintaining historical context.

### Assumption 5: Standard WooCommerce process needs to preserve original values
- **Verification**: When prices change in the standard WooCommerce process, original values should be preserved.
- **Implementation**:
  - Hook into WooCommerce product update actions
  - Before saving new prices, archive the old values to the price history table
  - Update both WooCommerce price (EUR) and ACF native price fields
  - Automated daily updates maintain EUR prices based on native currency prices
- **Validation**: This approach ensures no historical pricing data is lost during standard WooCommerce operations.

### Assumption 6: RFPs and Offers need to handle multi-currency scenarios
- **Verification**: RFPs and offers may involve different currencies based on vendor preferences.
- **Implementation**:
  - Add currency fields to RFP and Offer tables
  - Display both native and EUR prices in interfaces
  - Convert as needed for consistency in reporting
- **Validation**: This approach provides flexibility for international vendors while maintaining system consistency. 