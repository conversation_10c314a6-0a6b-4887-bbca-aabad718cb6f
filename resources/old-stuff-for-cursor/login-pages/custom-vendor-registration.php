<?php
// Ensure file is not accessed directly
if (!defined('ABSPATH')) {
    exit;
}

// Import the empty signature constant
require_once __DIR__ .  '/signature-pad/empty-signature.php';

// Enqueue scripts and styles for country selector and signature pad
function enqueue_custom_vendor_details_scripts()
{
    wp_enqueue_script('country-select-js', 'https://cdnjs.cloudflare.com/ajax/libs/country-select-js/2.1.0/js/countrySelect.min.js', array('jquery'), null, true);
    wp_enqueue_style('country-select-css', 'https://cdnjs.cloudflare.com/ajax/libs/country-select-js/2.1.0/css/countrySelect.min.css');
}
add_action('wp_enqueue_scripts', 'enqueue_custom_vendor_details_scripts');

// Ensure, the library loads before rendering
function enqueue_signature_pad_library()
{
    wp_enqueue_script(
        'signature-pad-lib',
        'https://cdn.jsdelivr.net/npm/signature_pad@2.3.2/dist/signature_pad.min.js',  // Ensure this is the correct CDN URL for SignaturePad
        array(),  // No dependencies
        null,     // No version
        true      // Load in the footer
    );
}
add_action('wp_enqueue_scripts', 'enqueue_signature_pad_library');

// Append the Dokan 'seller' role to Consultant or Compensation Project Owner upon registration
function add_seller_role_upon_registration($user_id)
{
    $user = new WP_User($user_id);
    $roles_to_add_seller = array('consultant', 'compensation_project_owner');

    foreach ($roles_to_add_seller as $role) {
        if (in_array($role, $user->roles)) {
            // Add the 'seller' role
            $user->add_role('seller');

            // Ensure Dokan-specific metadata is set but disable selling/publishing by default
            update_user_meta($user_id, 'dokan_enable_selling', 'no');  // Selling disabled by default
            update_user_meta($user_id, 'dokan_publishing', 'no');      // Product publishing disabled by default

            // Initialize empty profile settings array if not already set
            if (!get_user_meta($user_id, 'dokan_profile_settings', true)) {
                update_user_meta($user_id, 'dokan_profile_settings', array());
            }

            // Trigger Dokan-specific hook for new vendor
            do_action('dokan_new_seller_created', $user_id, $user);
        }
    }
}
add_action('user_register', 'add_seller_role_upon_registration');

// Restrict access to other pages until additional details are completed
function restrict_access_until_additional_details_completed()
{
    if (is_user_logged_in()) {
        $user = wp_get_current_user();
        $additional_details_completed = get_user_meta($user->ID, 'additional_details_completed', true);
        $user_status = get_user_meta($user->ID, 'ur_user_status', true);
        $additional_details_page_id = 18992; // ID of the additional details page

        if (
            (in_array('consultant', $user->roles) || in_array('compensation_project_owner', $user->roles)) &&
            ($additional_details_completed !== 'true' || $user_status == '0')
        ) {
            if (!is_page($additional_details_page_id)) {
                wp_redirect(get_permalink($additional_details_page_id));
                exit;
            }
        }
    }
}
add_action('template_redirect', 'restrict_access_until_additional_details_completed');

// Display custom vendor details form or message after submission
function display_custom_vendor_form()
{
    if (!is_user_logged_in()) {
        return '<p>Please log in to complete your profile.</p>';
    }

    $user_id = get_current_user_id();
    $additional_details_completed = get_user_meta($user_id, 'additional_details_completed', true);

    if ($additional_details_completed === 'true') {
        // Direct logout without confirmation by manually clearing session
        wp_destroy_current_session();
        wp_clear_auth_cookie();
        ?>
        <script>
            setTimeout(function() {
                window.location.href = "<?php echo home_url(); ?>"; // Redirect to homepage
            }, 5000);
        </script>
        <?php
        return '<p>Thank you, please confirm your email using the link sent.</p>';
    }

    ob_start();
    ?>
    <form id="custom-vendor-details-form" method="post" class="vendor-details-form">
        <?php wp_nonce_field('vendor_details_nonce_action', 'vendor_details_nonce'); ?>
        
        <div class="form-row two-column">
            <div class="form-group">
                <label for="first_name">First Name</label>
                <input type="text" name="first_name" id="first_name" maxlength="64" placeholder="Type your first name" required>
            </div>
            <div class="form-group">
                <label for="last_name">Last Name</label>
                <input type="text" name="last_name" id="last_name" maxlength="64" placeholder="Type your last name" required>
            </div>
        </div>

        <div class="form-row">
            <div class="form-group">
                <label for="organization_name">Organization Name</label>
                <input type="text" name="organization_name" id="organization_name" maxlength="64" placeholder="Type your organization name" required>
            </div>
        </div>

        <div class="form-row">
            <div class="form-group">
                <label for="business_id">Business ID</label>
                <input type="text" name="business_id" id="business_id" maxlength="26" placeholder="Type your business id" required>
            </div>
        </div>

        <div class="form-row">
            <div class="form-group">
                <label for="vendor_country">Vendor Country</label>
                <input type="text" id="vendor_country" name="vendor_country" required>
            </div>
        </div>

        <div class="form-row">
            <div class="form-group">
                <label for="vendor_signature">Vendor Signature</label>
                <canvas id="vendor_signature_pad" style="border:1px solid #000;"></canvas>
                <input type="hidden" name="vendor_signature" id="vendor_signature">
                <button type="button" id="clear_signature" class="clear-signature-button green secondary small">Clear Signature</button>
                <p id="signature-error" style="color: red; display: none;">Signature cannot be empty.</p>
            </div>
        </div>

        <button type="submit" name="submit_vendor_details" class="submit-button green">Submit Additional Information</button>
    </form>

    <script>
        jQuery(document).ready(function($) {
            $('#vendor_country').countrySelect({
                defaultCountry: "fi",
                preferredCountries: ['fi', 'us', 'gb', 'ca', 'de', 'fr', 'au', 'in', 'jp']
            });

            var canvas = document.getElementById('vendor_signature_pad');
            var signaturePad = new SignaturePad(canvas);

            $('#custom-vendor-details-form').on('submit', function(e) {
                var vendorSignature = signaturePad.toDataURL();
                $('#vendor_signature').val(vendorSignature);

                // Validate that the signature is not empty
                if (signaturePad.isEmpty() || vendorSignature === '<?php echo EMPTY_SIGNATURE; ?>') {
                    e.preventDefault(); // Prevent form submission
                    $('#signature-error').show(); // Show error message
                } else {
                    $('#signature-error').hide(); // Hide error message if valid
                }
            });

            $('#clear_signature').on('click', function(e) {
                e.preventDefault();
                signaturePad.clear();
                $('#signature-error').hide(); // Hide error message when cleared
            });
        });

        function resizeCanvas() {
            const canvas = document.getElementById('vendor_signature_pad');
            const parentWidth = canvas.parentElement.clientWidth;

            // Set canvas element size dynamically
            canvas.style.width = parentWidth + 'px';

            // set internal canvas drawing fiield dimensions
            canvas.width = parentWidth;
            canvas.height = parentWidth * 1 / 3;
        }

        window.addEventListener('load', resizeCanvas);
        window.addEventListener('resize', resizeCanvas);
    </script>
    <?php
    return ob_get_clean();
}
add_shortcode('custom_vendor_form', 'display_custom_vendor_form');

// Process form submission with security checks, validation, and token generation
function process_custom_vendor_form_submission()
{
    if (isset($_POST['submit_vendor_details']) && is_user_logged_in()) {
        $user_id = get_current_user_id();

        // Rate-limit submissions (15-second cooldown)
        $last_submission = get_user_meta($user_id, 'last_submission_time', true);
        if ($last_submission && (time() - $last_submission) < 15) { // 15 seconds
            wp_die('You can only submit the form once every 15 seconds. Please wait and try again.');
        }

        // Check if the form has already been submitted
        $additional_details_completed = get_user_meta($user_id, 'additional_details_completed', true);
        if ($additional_details_completed === 'true') {
            wp_die('You have already submitted this form.');
        }

        // Validate nonce for security
        if (!isset($_POST['vendor_details_nonce']) || !wp_verify_nonce($_POST['vendor_details_nonce'], 'vendor_details_nonce_action')) {
            wp_die('Security check failed.');
        }

        // Validate fields
        $first_name = sanitize_text_field($_POST['first_name']);
        $last_name = sanitize_text_field($_POST['last_name']);
        $organization_name = sanitize_text_field($_POST['organization_name']);
        $business_id = sanitize_text_field($_POST['business_id']);
        $vendor_country = sanitize_text_field($_POST['vendor_country']);
        $vendor_signature = $_POST['vendor_signature'];

        if (empty($first_name) || empty($last_name) || empty($organization_name) || empty($vendor_country)) {
            wp_die('Please fill in all required fields.');
        }
        if (strlen($business_id) > 26) {
            wp_die('Business ID cannot exceed 26 characters.');
        }

        // Assume EMPTY_SIGNATURE is defined somewhere as a constant or handle its absence
        if (empty($vendor_signature) || (defined('EMPTY_SIGNATURE') && $vendor_signature === EMPTY_SIGNATURE)) {
            wp_die('Signature is required.');
        }

        // Save user metadata
        update_user_meta($user_id, 'first_name', $first_name);
        update_user_meta($user_id, 'last_name', $last_name);
        update_user_meta($user_id, 'organization_name', $organization_name);
        update_user_meta($user_id, 'business_id', $business_id);
        update_user_meta($user_id, 'vendor_country', $vendor_country);
        update_user_meta($user_id, 'vendor_signature', esc_attr($vendor_signature));

        // Mark additional details as completed
        update_user_meta($user_id, 'additional_details_completed', 'true');

        // Update terms acceptance metadata
        update_user_meta($user_id, 'accepted_terms', 'true');
        update_user_meta($user_id, 'accepted_terms_time', current_time('mysql'));

        // Check if user was created via miniorange
        if (metadata_exists('user', $user_id, 'moopenid_user_profile_url')) {
            // User was created via miniOrange
            update_user_meta($user_id, 'created_via_miniorange', 'true');
        } else {
            // User was not created via miniOrange
            update_user_meta($user_id, 'created_via_miniorange', 'false');
        }
        $created_via_miniorange = get_user_meta($user_id, 'created_via_miniorange', true);

        if ($created_via_miniorange === 'true') {
            // If created via miniorange is true:
            // - Do not set ur_confirm_email_token
            // - Do not set ur_confirm_email
            // - Set ur_login_option = auto_login
            update_user_meta($user_id, 'ur_login_option', 'auto_login');

            // Keep accepted_terms and accepted_terms_time logic as is (already done above)
            // Send the Welcome email
            $user_email = get_userdata($user_id)->user_email;
            wp_mail(
                $user_email,
                'Welcome to CO2Market',
                "Here is some useful information to get you started: Lorem ipsum."
            );
            // Set rate-limiting timestamp
            update_user_meta($user_id, 'last_submission_time', time());

            // After completing details, redirect to homepage ('/') without signing out
            wp_redirect(home_url('/'));
            exit;

        } else {
            // If not created via miniorange, proceed with the old logic
            // Disable login until email confirmation
            update_user_meta($user_id, 'ur_confirm_email', '0');
            update_user_meta($user_id, 'ur_login_option', 'email_confirmation');

            // Generate a random confirmation token
            $confirmation_token = bin2hex(random_bytes(32)); // 64-character token
            update_user_meta($user_id, 'ur_confirm_email_token', $confirmation_token);

            // accepted_terms and accepted_terms_time remain as set above (true and current_time)
            $user_email = get_userdata($user_id)->user_email;
            $confirmation_link = home_url('/email-confirmation-handler.php?confirm_token=' . $confirmation_token);

            // Send the confirmation email
            wp_mail(
                $user_email,
                'Confirm Your Email Address',
                "Please confirm your email by clicking the link below:\n\n" . $confirmation_link
            );

            // Send the Welcome email
            wp_mail(
                $user_email,
                'Welcome to CO2Market',
                "Here is some useful information to get you started: Lorem ipsum."
            );

            // Set rate-limiting timestamp
            update_user_meta($user_id, 'last_submission_time', time());

            // Redirect to the same page with a message (as original logic)
            wp_redirect(get_permalink(18992)); // Replace with the correct page ID
            exit;
        }
    }
}
add_action('init', 'process_custom_vendor_form_submission');

// Add custom columns to the Users table
function add_custom_user_columns($columns)
{
    // Add new columns for custom metadata fields
    $columns['organization_name'] = 'Organization Name';
    $columns['business_id'] = 'Business ID';
    $columns['vendor_country'] = 'Vendor Country';
    $columns['additional_details_completed'] = 'Details Completed';
    return $columns;
}
add_filter('manage_users_columns', 'add_custom_user_columns');

// Populate the custom columns with user metadata
function add_custom_user_column_content($value, $column_name, $user_id)
{
    switch ($column_name) {
        case 'organization_name':
            return get_user_meta($user_id, 'organization_name', true) ?: '—';
        case 'business_id':
            return get_user_meta($user_id, 'business_id', true) ?: '—';
        case 'vendor_country':
            return get_user_meta($user_id, 'vendor_country', true) ?: '—';
        case 'additional_details_completed':
            $completed = get_user_meta($user_id, 'additional_details_completed', true);
            return $completed === 'true' ? 'Yes' : 'No';
        default:
            return $value; // Return the original value for other columns
    }
}
add_filter('manage_users_custom_column', 'add_custom_user_column_content', 10, 3);

// Add fields to the Edit User screen
function add_custom_user_fields_to_edit($user)
{
    ?>
    <h3>Additional Vendor Details</h3>
    <table class="form-table">
        <tr>
            <th><label for="organization_name">Organization Name</label></th>
            <td>
                <input type="text" name="organization_name" id="organization_name"
                       value="<?php echo esc_attr(get_user_meta($user->ID, 'organization_name', true)); ?>" class="regular-text"/>
            </td>
        </tr>
        <tr>
            <th><label for="business_id">Business ID</label></th>
            <td>
                <input type="text" name="business_id" id="business_id"
                       value="<?php echo esc_attr(get_user_meta($user->ID, 'business_id', true)); ?>" class="regular-text"/>
            </td>
        </tr>
        <tr>
            <th><label for="vendor_country">Vendor Country</label></th>
            <td>
                <input type="text" name="vendor_country" id="vendor_country"
                       value="<?php echo esc_attr(get_user_meta($user->ID, 'vendor_country', true)); ?>" class="regular-text"/>
            </td>
        </tr>
        <tr>
            <th><label for="additional_details_completed">Details Completed</label></th>
            <td>
                <select name="additional_details_completed" id="additional_details_completed">
                    <option value="true" <?php selected(get_user_meta($user->ID, 'additional_details_completed', true), 'true'); ?>>Yes</option>
                    <option value="false" <?php selected(get_user_meta($user->ID, 'additional_details_completed', true), 'false'); ?>>No</option>
                </select>
            </td>
        </tr>
    </table>
    <?php
}
add_action('show_user_profile', 'add_custom_user_fields_to_edit');
add_action('edit_user_profile', 'add_custom_user_fields_to_edit');

// Save custom fields when the user profile is updated
function save_custom_user_fields($user_id)
{
    if (!current_user_can('edit_user', $user_id)) {
        return false;
    }

    // Save custom fields
    update_user_meta($user_id, 'organization_name', sanitize_text_field($_POST['organization_name']));
    update_user_meta($user_id, 'business_id', sanitize_text_field($_POST['business_id']));
    update_user_meta($user_id, 'vendor_country', sanitize_text_field($_POST['vendor_country']));
    update_user_meta($user_id, 'additional_details_completed', sanitize_text_field($_POST['additional_details_completed']));
}
add_action('personal_options_update', 'save_custom_user_fields');
add_action('edit_user_profile_update', 'save_custom_user_fields');

// Add the vendor signature to the Edit User screen
function add_vendor_signature_field_to_edit($user)
{
    $vendor_signature = get_user_meta($user->ID, 'vendor_signature', true);
    ?>
    <h3>Vendor Signature</h3>
    <table class="form-table">
        <tr>
            <th><label for="vendor_signature">Signature</label></th>
            <td>
                <?php if (!empty($vendor_signature) && $vendor_signature !== EMPTY_SIGNATURE): ?>
                    <img src="<?php echo esc_attr($vendor_signature); ?>" alt="Vendor Signature" style="border:1px solid #000; width:400px; height:auto;" />
                <?php else: ?>
                    <p>No signature provided.</p>
                <?php endif; ?>
            </td>
        </tr>
    </table>
    <?php
}
add_action('show_user_profile', 'add_vendor_signature_field_to_edit');
add_action('edit_user_profile', 'add_vendor_signature_field_to_edit');
