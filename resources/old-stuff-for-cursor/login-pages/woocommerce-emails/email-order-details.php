<?php
/**
 * Custom Order details table for emails with VAT as percentage.
 */

defined('ABSPATH') || exit;

$text_align = is_rtl() ? 'right' : 'left';

do_action('woocommerce_email_before_order_table', $order, $sent_to_admin, $plain_text, $email); ?>

<h2>
	<?php
    if ($sent_to_admin) {
        $before = '<a class="link" href="' . esc_url($order->get_edit_order_url()) . '">';
        $after  = '</a>';
    } else {
        $before = '';
        $after  = '';
    }
/* translators: %s: Order ID. */
echo wp_kses_post($before . sprintf(__('[Order #%s]', 'woocommerce') . $after . ' (<time datetime="%s">%s</time>)', $order->get_order_number(), $order->get_date_created()->format('c'), wc_format_datetime($order->get_date_created())));
?>
</h2>

<div style="margin-bottom: 40px;">
	<table class="td" cellspacing="0" cellpadding="6" style="width: 100%; font-family: 'Helvetica Neue', Helvetica, Roboto, Arial, sans-serif;" border="1">
		<thead>
			<tr>
				<th class="td" scope="col" style="text-align:<?php echo esc_attr($text_align); ?>;"><?php esc_html_e('Product', 'woocommerce'); ?></th>
				<th class="td" scope="col" style="text-align:<?php echo esc_attr($text_align); ?>;"><?php esc_html_e('Quantity', 'woocommerce'); ?></th>
				<th class="td" scope="col" style="text-align:<?php echo esc_attr($text_align); ?>;"><?php esc_html_e('Price VAT0', 'woocommerce'); ?></th>
				<th class="td" scope="col" style="text-align:<?php echo esc_attr($text_align); ?>;"><?php esc_html_e('VAT (%)', 'woocommerce'); ?></th>
			</tr>
		</thead>
		<tbody>
			<?php
        $subtotal = 0;
$vat_total = 0;

// Loop through order items
foreach ($order->get_items() as $item_id => $item) {
    $product = $item->get_product();
    $item_subtotal = $order->get_line_subtotal($item, false); // Price before tax

    // Fetch VAT percentage from ACF field
    $vat_percentage = get_field('local_vat', $product->get_id()); // Assume VAT is stored as a percentage, e.g., 25 for 25%
    $vat_percentage_display = $vat_percentage ? $vat_percentage . '%' : '0%'; // Display VAT as percentage
    $vat_percentage_decimal = $vat_percentage ? $vat_percentage / 100 : 0; // Convert percentage to decimal for calculation

    $item_tax = $item_subtotal * $vat_percentage_decimal; // VAT amount
    $item_total = $item_subtotal + $item_tax; // Price after tax

    // Accumulate subtotal and VAT
    $subtotal += $item_subtotal;
    $vat_total += $item_tax;

    ?>
				<tr>
					<td class="td" style="text-align:<?php echo esc_attr($text_align); ?>;"><?php echo esc_html($item->get_name()); ?></td>
					<td class="td" style="text-align:<?php echo esc_attr($text_align); ?>;"><?php echo esc_html($item->get_quantity()); ?></td>
					<td class="td" style="text-align:<?php echo esc_attr($text_align); ?>;"><?php echo wc_price($item_subtotal, array( 'currency' => $order->get_currency() )); ?></td>
					<td class="td" style="text-align:<?php echo esc_attr($text_align); ?>;"><?php echo esc_html($vat_percentage_display); ?></td>
				</tr>
				<?php
}
?>
		</tbody>
		<tfoot>
			<tr>
				<th class="td" scope="row" colspan="3" style="text-align:<?php echo esc_attr($text_align); ?>;"><?php esc_html_e('Subtotal:', 'woocommerce'); ?></th>
				<td class="td" style="text-align:<?php echo esc_attr($text_align); ?>;"><?php echo wc_price($subtotal, array( 'currency' => $order->get_currency() )); ?></td>
			</tr>
			<tr>
				<th class="td" scope="row" colspan="3" style="text-align:<?php echo esc_attr($text_align); ?>;"><?php esc_html_e('VAT:', 'woocommerce'); ?></th>
				<td class="td" style="text-align:<?php echo esc_attr($text_align); ?>;"><?php echo wc_price($vat_total, array( 'currency' => $order->get_currency() )); ?></td>
			</tr>
			<tr>
				<th class="td" scope="row" colspan="3" style="text-align:<?php echo esc_attr($text_align); ?>;"><?php esc_html_e('Total:', 'woocommerce'); ?></th>
				<td class="td" style="text-align:<?php echo esc_attr($text_align); ?>;"><?php echo wc_price($subtotal + $vat_total, array( 'currency' => $order->get_currency() )); ?></td>
			</tr>
		</tfoot>
	</table>
</div>

<?php do_action('woocommerce_email_after_order_table', $order, $sent_to_admin, $plain_text, $email); ?>
