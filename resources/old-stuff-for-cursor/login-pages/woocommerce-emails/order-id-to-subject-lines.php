<?php

// Add order ID to the admin-new-order email subject
add_filter('woocommerce_email_subject_new_order', 'add_order_id_to_admin_new_order_subject', 10, 2);
function add_order_id_to_admin_new_order_subject($subject, $order)
{
    $order_id = $order->get_id();
    $subject .= " (Order ID: {$order_id})";
    return $subject;
}

// Add order ID to the customer-on-hold email subject
add_filter('woocommerce_email_subject_customer_on_hold_order', 'add_order_id_to_customer_on_hold_subject', 10, 2);
function add_order_id_to_customer_on_hold_subject($subject, $order)
{
    $order_id = $order->get_id();
    $subject .= " (Order ID: {$order_id})";
    return $subject;
}

// Add order ID to the customer-processing-order email subject
add_filter('woocommerce_email_subject_customer_processing_order', 'add_order_id_to_customer_processing_subject', 10, 2);
function add_order_id_to_customer_processing_subject($subject, $order)
{
    $order_id = $order->get_id();
    $subject .= " (Order ID: {$order_id})";
    return $subject;
}

// Add order ID to the customer-completed-order email subject
add_filter('woocommerce_email_subject_customer_completed_order', 'add_order_id_to_customer_completed_subject', 10, 2);
function add_order_id_to_customer_completed_subject($subject, $order)
{
    $order_id = $order->get_id();
    $subject .= " (Order ID: {$order_id})";
    return $subject;
}
