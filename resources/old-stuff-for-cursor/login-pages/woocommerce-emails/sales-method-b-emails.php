<?php
/**
 * Sales Method B - Custom Emails for WooCommerce
 * Adds Tosite 1 for Method B.
 */

add_action('woocommerce_order_status_processing', 'sales_method_b_send_tosite_1_email', 10, 2);

function sales_method_b_send_tosite_1_email($order_id, $order)
{
    if (! $order) {
        return;
    }

    // Define EU/ETA country list with full country names
    $eu_eta_countries = [
        'Austria', 'Belgium', 'Bulgaria', 'Croatia', 'Cyprus', 'Czech Republic', 'Denmark', 'Estonia',
        'Finland', 'France', 'Germany', 'Greece', 'Hungary', 'Iceland', 'Ireland', 'Italy',
        'Latvia', 'Liechtenstein', 'Lithuania', 'Luxembourg', 'Malta', 'Netherlands', 'Norway',
        'Poland', 'Portugal', 'Romania', 'Slovakia', 'Slovenia', 'Spain', 'Sweden', 'Switzerland'
    ];

    // Fetch service fee from order
    $service_fee = 0;
    foreach ($order->get_items('fee') as $fee) {
        $service_fee += $fee->get_total();
    }

    if ($service_fee <= 0) {
        $service_fee = 100; // Fallback value in EUR
    }

    foreach ($order->get_items() as $item) {
        $product_id = $item->get_product_id();
        $author_id  = get_post_field('post_author', $product_id);

        $vendor = dokan()->vendor->get($author_id);

        if ($vendor) {
            // Fetch vendor details
            $vendor_first_name = $vendor->get_first_name();
            $vendor_last_name = $vendor->get_last_name();
            $organization_name = $vendor->get_meta('organization_name', true) ?: '';
            $business_id = $vendor->get_meta('business_id', true) ?: 'No Business ID Provided';
            $vendor_country = get_user_meta($author_id, 'vendor_country', true) ?: 'Unknown Country';

            $vendor_name = trim($vendor_first_name . ' ' . $vendor_last_name);
            $formatted_vendor_name = $vendor_name . ($organization_name ? ' / ' . $organization_name : '');

            $exchange_rate = 1.1; // Placeholder for exchange rate

            // Generate the content based on vendor country
            ob_start();

            // Green header
            echo '<div style="background-color: #007c44; color: white; padding: 10px 20px; font-size: 20px; font-weight: bold; border-radius: 5px 5px 0 0;">';
            echo 'Receipt for Platform Service Fee';
            echo '</div>';

            // Email content with borders
            echo '<div style="border: 1px solid #007c44; padding: 20px; font-family: Arial, sans-serif; line-height: 1.6;">';

            echo '<p>' . esc_html__('This is an automated email regarding the service fee associated with the recent order.', 'woocommerce') . '</p>';

            if ($vendor_country === 'Finland') {
                echo '<p>' . esc_html__('CO2Market, Y-tunnus / Business ID 2662787-2', 'woocommerce') . '</p>';
                echo '<p><strong>' . esc_html__('Service fee:', 'woocommerce') . '</strong> ' . esc_html($service_fee) . ' EUR</p>';
                echo '<p><strong>' . esc_html__('VAT (25.5%):', 'woocommerce') . '</strong> ' . round($service_fee * 0.255, 2) . ' EUR</p>';
                echo '<p><strong>' . esc_html__('Total Amount:', 'woocommerce') . '</strong> ' . esc_html($service_fee) . ' EUR</p>';
            } elseif (in_array($vendor_country, $eu_eta_countries)) {
                echo '<p>' . esc_html__('Service fee, Reverse charge, VAT Directive art 44', 'woocommerce') . '</p>';
                echo '<p><strong>' . esc_html__('Total Amount:', 'woocommerce') . '</strong> ' . esc_html($service_fee) . ' EUR</p>';
            } else {
                echo '<p>' . esc_html__('Service fee, VAT 0 %, Export of goods', 'woocommerce') . '</p>';
                echo '<p><strong>' . esc_html__('Total Amount:', 'woocommerce') . '</strong> ' . esc_html($service_fee) . ' EUR</p>';
            }

            echo '<p><strong>' . esc_html__('Exchange Rate:', 'woocommerce') . '</strong> ' . esc_html($exchange_rate) . ' USD = 1 EUR</p>';

            echo '<p><strong>' . esc_html__('Vendor Name:', 'woocommerce') . '</strong> ' . esc_html($formatted_vendor_name) . '</p>';
            echo '<p><strong>' . esc_html__('Business ID:', 'woocommerce') . '</strong> ' . esc_html($business_id) . '</p>';
            echo '<p><strong>' . esc_html__('Country:', 'woocommerce') . '</strong> ' . esc_html($vendor_country) . '</p>';

            echo '</div>';

            $email_content = ob_get_clean();

            // Send email
            $recipients = [ $vendor->get_email(), get_option('admin_email') ];
            foreach ($recipients as $recipient) {
                $result = wp_mail($recipient, 'CO2Market: Receipt for Platform Service Fee', $email_content);
                custom_debug_log($result ? "Email sent to {$recipient}" : "Email sending failed to {$recipient}");
            }
        }
    }
}
