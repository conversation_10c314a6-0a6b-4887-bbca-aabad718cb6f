<?php
/**
 * Sales Method B - Receipt for Advance Payment
 * Sends a receipt email to the customer and admin if no valid vendor is found.
 */

add_action('woocommerce_order_status_processing', 'send_advance_payment_receipt', 10, 2);
add_action('woocommerce_order_status_on-hold', 'send_advance_payment_receipt', 10, 2);

function send_advance_payment_receipt($order_id, $order)
{
    if (! $order) {
        return;
    }

    // Assume no valid vendor initially
    $has_valid_vendor = false;

    foreach ($order->get_items() as $item) {
        $product_id = $item->get_product_id();
        $author_id  = get_post_field('post_author', $product_id);

        // Check if the product has a vendor
        $vendor = dokan()->vendor->get($author_id);
        if ($vendor) {
            $accepted_terms = $vendor->get_meta('accepted_terms', true) === 'true';
            $auto_login     = $vendor->get_meta('ur_login_option', true) === 'auto_login';
            $confirm_email  = $vendor->get_meta('ur_confirm_email', true) === '1';

            // If the vendor meets the conditions, set as valid
            if ($accepted_terms && ($auto_login || $confirm_email)) {
                $has_valid_vendor = true;
                break; // Exit loop as at least one valid vendor exists
            }
        }
    }

    // If a valid vendor exists, do not send the email
    if ($has_valid_vendor) {
        return;
    }

    // Fetch service fee from order
    $service_fee = 0;
    foreach ($order->get_items('fee') as $fee) {
        $service_fee += $fee->get_total();
    }

    // Fallback service fee if not set
    if ($service_fee <= 0) {
        $service_fee = 100; // Fallback value in EUR
    }

    // Convert the service fee to the customer's currency using WCML exchange rates
    $customer_currency = $order->get_currency();
    $service_fee_in_customer_currency = convert_service_fee_to_currency($service_fee, 'EUR', $customer_currency);

    // Fetch product prices in order's currency and sum them
    $product_total = 0;
    foreach ($order->get_items() as $item) {
        $product_id = $item->get_product_id();
        $quantity   = $item->get_quantity();

        // Use WCML to fetch the product price in the order's currency
        $product_price_in_currency = apply_filters('wcml_product_price_by_currency', $item->get_total(), $customer_currency);
        $product_total += $product_price_in_currency * $quantity;
    }

    // Total sum: service fee + product total (in customer's currency)
    $total_sum = $service_fee_in_customer_currency + $product_total;

    // Generate the email content
    ob_start();

    // Green header
    echo '<div style="background-color: #007c44; color: white; padding: 10px 20px; font-size: 20px; font-weight: bold; border-radius: 5px 5px 0 0;">';
    echo 'Receipt for Advance Payment';
    echo '</div>';

    // Email content with borders
    echo '<div style="border: 1px solid #007c44; padding: 20px; font-family: Arial, sans-serif; line-height: 1.6;">';

    echo '<p>' . esc_html__('CO2Market, Y-tunnus / Business ID 2662787-2', 'woocommerce') . '</p>';
    echo '<p><strong>' . esc_html__('Ennakkomaksu (ALV 0 %):', 'woocommerce') . '</strong> ' . wc_price($total_sum, [ 'currency' => $customer_currency ]) . '</p>';
    echo '<p><strong>' . esc_html__('Advance Payment (VAT 0 %):', 'woocommerce') . '</strong> ' . wc_price($total_sum, [ 'currency' => $customer_currency ]) . '</p>';

    echo '</div>';

    $email_content = ob_get_clean();

    // Recipients: Customer and Admin
    $recipients = [ $order->get_billing_email(), get_option('admin_email') ];
    foreach ($recipients as $recipient) {
        wp_mail($recipient, 'CO2Market: Receipt for Advance Payment', $email_content);
    }
}

// Convert the service fee to the customer's currency using WCML exchange rates
function convert_service_fee_to_currency($amount, $from_currency, $to_currency)
{
    if ($from_currency === $to_currency) {
        return $amount;
    }

    // Fetch WCML exchange rates
    $exchange_rates = apply_filters('wcml_exchange_rates', []);

    // Get the rate for the target currency
    $rate = $exchange_rates[ $to_currency ] ?? 1;

    return $amount * $rate;
}
