<?php
/**
 * Customer Processing Order Email with Custom Details.
 */

if (! defined('ABSPATH')) {
    exit;
}

do_action('woocommerce_email_header', $email_heading, $email); ?>

<p><?php printf(esc_html__('Hi %s,', 'woocommerce'), esc_html($order->get_billing_first_name())); ?></p>
<p><?php printf(esc_html__('We are processing your purchase #%s. Here are the details:', 'woocommerce'), esc_html($order->get_order_number())); ?></p>

<?php
// Display order details
do_action('woocommerce_email_order_details', $order, $sent_to_admin, $plain_text, $email);
?>

<h3><?php esc_html_e('Vendor Details', 'woocommerce'); ?></h3>
<?php
// Loop through items to display vendor details
foreach ($order->get_items() as $item) {
    $product_id = $item->get_product_id();
    $author_id  = get_post_field('post_author', $product_id);

    $vendor = dokan()->vendor->get($author_id);
    if ($vendor) {
        $vendor_name = trim($vendor->get_first_name() . ' ' . $vendor->get_last_name()) ?: esc_html__('Unknown Vendor', 'woocommerce');
        $organization_name = $vendor->get_meta('organization_name', true) ?: '';
        $business_id = $vendor->get_meta('business_id', true) ?: '';

        $formatted_vendor_name = $vendor_name . ($organization_name ? ' / ' . $organization_name : '');

        echo '<p><strong>' . esc_html__('Vendor:', 'woocommerce') . '</strong> ' . esc_html($formatted_vendor_name) . '</p>';
        echo '<p><strong>' . esc_html__('Business ID:', 'woocommerce') . '</strong> ' . esc_html($business_id) . '</p>';
    } else {
        echo '<p>' . esc_html__('Vendor information is not available.', 'woocommerce') . '</p>';
    }
}
?>

<h3><?php esc_html_e('Customer Details', 'woocommerce'); ?></h3>
<?php
// Custom customer details (explicitly rendered)
if ($billing_address = $order->get_formatted_billing_address()) {
    echo '<p><strong>' . esc_html__('', 'woocommerce') . '</strong> ' . wp_kses_post($billing_address) . '</p>';
    echo '<p><strong>' . esc_html__('', 'woocommerce') . '</strong> ' . esc_html($order->get_billing_phone()) . '</p>';
    echo '<p><strong>' . esc_html__('', 'woocommerce') . '</strong> ' . esc_html($order->get_billing_email()) . '</p>';
} else {
    echo '<p>' . esc_html__('No billing address provided.', 'woocommerce') . '</p>';
}
?>

<?php
// Remove default meta or shipping details if added by WooCommerce
remove_action('woocommerce_email_order_meta', 'woocommerce_email_order_meta');
remove_action('woocommerce_email_order_details', 'woocommerce_email_order_details');

custom_debug_log('Tosite 1B email triggered for order ID: ' . $order->get_id());
sales_method_b_send_tosite_1_email($order->get_id(), $order);

// Footer
do_action('woocommerce_email_footer', $email);
