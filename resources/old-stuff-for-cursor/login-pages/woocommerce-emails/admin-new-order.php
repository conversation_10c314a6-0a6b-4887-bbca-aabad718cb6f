<?php
/**
 * Admin new order email
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/emails/admin-new-order.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see https://woocommerce.com/document/template-structure/
 * @package WooCommerce\Templates\Emails\HTML
 * @version 3.7.0
 */

defined('ABSPATH') || exit;

/*
 * @hooked WC_Emails::email_header() Output the email header
 */
do_action('woocommerce_email_header', $email_heading, $email); ?>

<?php /* translators: %s: Customer billing full name */ ?>
<p><?php printf(esc_html__('You’ve received the following order from %s:', 'woocommerce'), $order->get_formatted_billing_full_name()); // phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped?></p>
<?php

// Display order details
do_action('woocommerce_email_order_details', $order, $sent_to_admin, $plain_text, $email);
?>

<h3><?php esc_html_e('Vendor Details', 'woocommerce'); ?></h3>
<?php
// Loop through items to display vendor details
foreach ($order->get_items() as $item) {
    $product_id = $item->get_product_id();
    $author_id  = get_post_field('post_author', $product_id);

    $vendor = dokan()->vendor->get($author_id);
    if ($vendor) {
        $vendor_name = trim($vendor->get_first_name() . ' ' . $vendor->get_last_name()) ?: esc_html__('Unknown Vendor', 'woocommerce');
        $organization_name = $vendor->get_meta('organization_name', true) ?: '';
        $business_id = $vendor->get_meta('business_id', true) ?: '';

        $formatted_vendor_name = $vendor_name . ($organization_name ? ' / ' . $organization_name : '');

        echo '<p><strong>' . esc_html__('Vendor:', 'woocommerce') . '</strong> ' . esc_html($formatted_vendor_name) . '</p>';
        echo '<p><strong>' . esc_html__('Business ID:', 'woocommerce') . '</strong> ' . esc_html($business_id) . '</p>';
    } else {
        echo '<p>' . esc_html__('Vendor information is not available.', 'woocommerce') . '</p>';
    }
}
?>

<h3><?php esc_html_e('Customer Details', 'woocommerce'); ?></h3>
<?php
// Custom customer details (explicitly rendered)
if ($billing_address = $order->get_formatted_billing_address()) {
    echo '<p><strong>' . esc_html__('', 'woocommerce') . '</strong> ' . wp_kses_post($billing_address) . '</p>';
    echo '<p><strong>' . esc_html__('', 'woocommerce') . '</strong> ' . esc_html($order->get_billing_phone()) . '</p>';
    echo '<p><strong>' . esc_html__('', 'woocommerce') . '</strong> ' . esc_html($order->get_billing_email()) . '</p>';
} else {
    echo '<p>' . esc_html__('No billing address provided.', 'woocommerce') . '</p>';
}
?>

<?php
// Remove default meta or shipping details if added by WooCommerce
remove_action('woocommerce_email_order_meta', 'woocommerce_email_order_meta');
remove_action('woocommerce_email_order_details', 'woocommerce_email_order_details');

// Footer
do_action('woocommerce_email_footer', $email);

/**
 * Show user-defined additional content - this is set in each email's settings.
 */
if ($additional_content) {
    echo wp_kses_post(wpautop(wptexturize($additional_content)));
}

/*
 * @hooked WC_Emails::email_footer() Output the email footer
 */
do_action('woocommerce_email_footer', $email);
