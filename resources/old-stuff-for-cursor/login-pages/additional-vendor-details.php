<?php
// Ensure file is not accessed directly
if (!defined('ABSPATH')) {
    exit;
}

// Enqueue `country-select-js` CSS and JavaScript
function enqueue_additional_vendor_details_scripts()
{
    wp_enqueue_script('country-select-js', 'https://cdnjs.cloudflare.com/ajax/libs/country-select-js/2.1.0/js/countrySelect.min.js', array('jquery'), null, true);
    wp_enqueue_style('country-select-css', 'https://cdnjs.cloudflare.com/ajax/libs/country-select-js/2.1.0/css/countrySelect.min.css');
}
add_action('wp_enqueue_scripts', 'enqueue_additional_vendor_details_scripts');

// Trigger the additional details form for debugging on homepage for specific user
add_action('wp', 'trigger_additional_vendor_details_popup');
function trigger_additional_vendor_details_popup()
{
    if (is_user_logged_in() && is_front_page()) { // Check if on homepage and user is logged in
        $user = wp_get_current_user();
    }
}

// Display the additional vendor details popup with `country-select-js`
function display_additional_vendor_details_popup()
{
    ?>
    <div id="additional-vendor-details-popup" style="display:block; position:fixed; top:0; left:0; width:100%; height:100vh; background:rgba(0,0,0,0.7); z-index:9999;">
        <div class="popup-content register-as-container">
            <h2>Additional Information Required</h2>
            <form id="additional-vendor-details-form" method="post">
                <label for="vendor_business_id">Business ID:</label>
                <input type="text" name="vendor_business_id" id="vendor_business_id" required><br><br>
                
                <label for="vendor_country">Country:</label>
                <input type="text" id="vendor_country" name="vendor_country" required><br><br>

                <button class="button green" type="submit" name="submit_vendor_details_button">Submit</button>
            </form>
        </div>
    </div>
    <script>
        // Prevent scrolling when popup is active
        document.body.classList.add('terms-popup-open');

        jQuery(document).ready(function($) {
            // Initialize `countrySelect` for the country input
            $('#vendor_country').countrySelect({
                defaultCountry: "fi",
                preferredCountries: ['fi', 'us', 'gb', 'ca', 'de', 'fr', 'au', 'in', 'jp']
            });
        });
    </script>
    <style>
        /* Ensures the popup has consistent styling */
        .popup-content {
            background: white;
            width: 80%;
            max-width: 600px;
            margin: auto;
            padding: 2rem;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
            border-radius: 5px;
            overflow-y: auto;
            position: relative;
            top: 10vh;
        }
    </style>
    <?php
}

// Process form submission for additional details securely
add_action('init', 'process_additional_vendor_details_submission');
function process_additional_vendor_details_submission()
{
    if (isset($_POST['submit_vendor_details_button'])) {
        $user_id = get_current_user_id();

        // Validate and sanitize inputs
        $business_id = isset($_POST['vendor_business_id']) ? sanitize_text_field($_POST['vendor_business_id']) : '';
        $country = isset($_POST['vendor_country']) ? sanitize_text_field($_POST['vendor_country']) : '';

        // Check if the fields are populated
        if (!empty($business_id) && !empty($country)) {

            // Securely store Business ID in user meta if it doesn't exist
            if (!metadata_exists('user', $user_id, 'business_id')) {
                update_user_meta($user_id, 'business_id', $business_id);
            }

            // Securely store Country in user meta if it doesn't exist
            if (!metadata_exists('user', $user_id, 'country')) {
                update_user_meta($user_id, 'country', $country);
            }

            // Redirect the user back to the homepage after saving data
            wp_redirect(home_url());
            exit;
        }
    }
}

// Display Business ID and Country in the admin user profile
add_action('show_user_profile', 'display_custom_user_meta_fields');
add_action('edit_user_profile', 'display_custom_user_meta_fields');

function display_custom_user_meta_fields($user)
{
    ?>
    <h3>Additional Information</h3>
    <table class="form-table">
        <tr>
            <th><label for="business_id">Business ID</label></th>
            <td>
                <input type="text" name="business_id" id="business_id" value="<?php echo esc_attr(get_user_meta($user->ID, 'business_id', true)); ?>" class="regular-text" readonly />
            </td>
        </tr>
        <tr>
            <th><label for="country">Country</label></th>
            <td>
                <input type="text" name="country" id="country" value="<?php echo esc_attr(get_user_meta($user->ID, 'country', true)); ?>" class="regular-text" readonly />
            </td>
        </tr>
    </table>
    <?php
}
