<?php
// Ensure file is not accessed directly
if (!defined('ABSPATH')) {
    exit;
}

function display_additional_customer_form()
{
    if (!is_user_logged_in()) {
        return '<p>Please log in to complete your profile.</p>';
    }

    $user_id = get_current_user_id();
    $additional_details_completed = get_user_meta($user_id, 'additional_details_completed', true);
    if (metadata_exists('user', $user_id, 'moopenid_user_profile_url')) {
        // User was created via miniOrange
        update_user_meta($user_id, 'created_via_miniorange', 'true');
    } else {
        // User was not created via miniOrange
        update_user_meta($user_id, 'created_via_miniorange', 'false');
    }
    $created_via_miniorange = get_user_meta($user_id, 'created_via_miniorange', true);

    if ($additional_details_completed === 'true') {
        if ($created_via_miniorange === 'true') {
            // If created via miniorange is true, skip logout and redirect
            // Just display a thank-you message and do not log the user out.
            terms_of_service_popup();

            return '<p>Thank you, your additional details have been completed. The terms will show now.</p>';

        } else {
            // Original logic: Display thank-you message and logout after a delay
            wp_destroy_current_session();
            wp_clear_auth_cookie();
            ?>
            <script>
                setTimeout(function () {
                    window.location.href = "<?php echo esc_url(home_url('/login?message=details_completed')); ?>"; 
                }, 5000);
            </script>
            <?php
            return '<p>Thank you, an email confirmation link has been sent.</p>';
        }
    }

    ob_start();
    ?>
    <form id="additional-customer-details-form" method="post" class="customer-details-form">
        <?php wp_nonce_field('customer_details_nonce_action', 'customer_details_nonce'); ?>
        
        <div class="form-row two-column">
            <div class="form-group">
                <label for="first_name">First Name</label>
                <input type="text" name="first_name" id="first_name" maxlength="64" placeholder="Type your first name" required>
            </div>
            <div class="form-group">
                <label for="last_name">Last Name</label>
                <input type="text" name="last_name" id="last_name" maxlength="64" placeholder="Type your last name" required>
            </div>
        </div>

        <button type="submit" name="submit_customer_details" class="submit-button green small">Submit Details</button>
    </form>
    <?php
    return ob_get_clean();
}
add_shortcode('additional_customer_form', 'display_additional_customer_form');

function process_additional_customer_form_submission()
{
    if (isset($_POST['submit_customer_details']) && is_user_logged_in()) {
        $user_id = get_current_user_id();

        // Rate-limit submissions (15-second cooldown)
        $last_submission = get_user_meta($user_id, 'last_submission_time', true);
        if ($last_submission && (time() - $last_submission) < 15) {
            wp_die('You can only submit the form once every 15 seconds. Please wait and try again.');
        }

        // Check if the form has already been submitted
        $additional_details_completed = get_user_meta($user_id, 'additional_details_completed', true);
        if ($additional_details_completed === 'true') {
            wp_die('You have already submitted this form.');
        }

        // Validate nonce for security
        if (!isset($_POST['customer_details_nonce']) || !wp_verify_nonce($_POST['customer_details_nonce'], 'customer_details_nonce_action')) {
            wp_die('Security check failed.');
        }

        // Validate fields
        $first_name = sanitize_text_field($_POST['first_name']);
        $last_name = sanitize_text_field($_POST['last_name']);

        if (empty($first_name) || empty($last_name)) {
            wp_die('Please fill in all required fields.');
        }

        // Save user meta
        update_user_meta($user_id, 'first_name', $first_name);
        update_user_meta($user_id, 'last_name', $last_name);
        update_user_meta($user_id, 'additional_details_completed', 'true');

        // Check if user was created via miniorange
        $created_via_miniorange = get_user_meta($user_id, 'created_via_miniorange', true);

        if ($created_via_miniorange === 'true') {
            // If created via miniorange is true, and if user is a customer, accept terms = false, no timestamp
            // Also, do not send confirmation email, do not set ur_confirm_email_token
            // and do not set ur_confirm_email. Instead set ur_login_option = auto_login

            // Check if user is a customer
            $user = get_userdata($user_id);
            $roles = $user->roles;
            if (in_array('customer', $roles, true)) {
                // Set accepted_terms to false
                update_user_meta($user_id, 'accepted_terms', 'false');
                // Do not set accepted_terms_time
            } else {
                // If user is vendor, terms might not apply here,
                // but following instructions strictly, only condition given is if user is customer.
                // If vendor, do nothing special for terms here.
            }

            // Do not set ur_confirm_email_token
            // Do not set ur_confirm_email
            // Set ur_login_option = auto_login
            update_user_meta($user_id, 'ur_login_option', 'auto_login');

        } else {
            // If not created via miniorange, proceed with the old logic
            // Email confirmation metadata
            update_user_meta($user_id, 'ur_confirm_email', '0');
            update_user_meta($user_id, 'ur_login_option', 'email_confirmation');

            // Generate email confirmation token
            $confirmation_token = bin2hex(random_bytes(32));
            update_user_meta($user_id, 'ur_confirm_email_token', $confirmation_token);

            $user_email = get_userdata($user_id)->user_email;
            $confirmation_link = home_url('/email-confirmation-handler.php?confirm_token=' . $confirmation_token);

            // Send the confirmation email
            wp_mail(
                $user_email,
                'Confirm Your Email Address',
                "Please confirm your email by clicking the link below:\n\n" . $confirmation_link
            );
        }

        // Set rate-limiting timestamp
        update_user_meta($user_id, 'last_submission_time', time());

        // Send the confirmation email
        wp_mail(
            $user_email,
            'Welcome to CO2Market',
            "Here is some useful information to get you started: Lorem ipsum."
        );
    }
}
add_action('init', 'process_additional_customer_form_submission');

/**
 * Restrict access until additional details completed for both customer and vendor.
 */
function restrict_customer_access_until_additional_details_completed()
{
    if (is_user_logged_in()) {
        $user = wp_get_current_user();
        $additional_details_completed = get_user_meta($user->ID, 'additional_details_completed', true);

        $customer_details_page_id = 18994;
        $vendor_details_page_id = 18992;

        if (in_array('customer', $user->roles, true) && $additional_details_completed !== 'true') {
            if (!is_page($customer_details_page_id)) {
                wp_safe_redirect(get_permalink($customer_details_page_id));
                exit;
            }
        }

        if ((in_array('consultant', $user->roles, true) || in_array('compensation_project_owner', $user->roles, true))
            && $additional_details_completed !== 'true') {
            if (!is_page($vendor_details_page_id)) {
                wp_safe_redirect(get_permalink($vendor_details_page_id));
                exit;
            }
        }
    }
}
add_action('template_redirect', 'restrict_customer_access_until_additional_details_completed', 30);
