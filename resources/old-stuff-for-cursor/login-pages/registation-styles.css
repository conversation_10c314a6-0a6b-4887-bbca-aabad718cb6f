/** Register Page */

.register-page-container {
  display: flex;
  flex-direction: column;
  padding-top: 2rem !important;
  align-items: center;
  gap: 3rem;
  max-width: 420px !important;
  border: none !important;
  border-bottom: 2px solid var(--g-200) !important;
}
.register-heading {
  color: var(--black) !important;
}
.register-page-container .user-type-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  width: 100%;
}

.register-page-container .user-type-container a {
  border-radius: 20px;
  padding: 1.5rem;
  height: 4.5rem;
  background-color: var(--g-400);
  color: var(--g-600);
  font-weight: 700;
  cursor: pointer;
}
.register-page-container .user-type-container .consultant-type-button {
  background-color: var(--o-400);
  color: var(--o-600);
}
.register-page-container .user-type-container .cpo-type-button {
  background-color: var(--b-400);
  color: var(--b-600);
}
.register-page-container .user-type-container a:hover {
  background-color: var(--g-200);
}
.register-page-container .user-type-container a:focus {
  outline: 2px solid var(--g-400);
  outline-offset: 2px;
}
.register-page-container .user-type-container .cpo-type-button:hover {
  background-color: var(--b-200);
}
.register-page-container .user-type-container .cpo-type-button:focus {
  outline: 2px solid var(--b-400);
  outline-offset: 2px;
}
.register-page-container .user-type-container .consultant-type-button:hover {
  background-color: var(--o-200);
}
.register-page-container .user-type-container .consultant-type-button:focus {
  outline: 2px solid var(--o-400);
  outline-offset: 2px;
}

.register-page-container .dropdown-container {
  width: 100%;
  cursor: pointer;
}
.register-page-container .info-dropdown {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem;
  width: 100%;
}
.info-dropdown p {
  margin: 0;
}
.dropdown-container .hidden-info {
  display: none;
  padding: 1rem;
}
.dropdown-container .hidden-info .bolded-heading {
  font-weight: 700;
}
.dropdown-container .hidden-info .bolded-heading:not(:first-child) {
  margin-top: 2rem;
}

.dropdown-container .hidden-info.show-info-active {
  display: block;
}

.info-dropdown .dropdown-text {
  display: flex;
  align-items: flex-end;
  gap: 1rem;
}
.info-dropdown .dropdown-text p {
  font-size: 0.875rem;
}
.info-dropdown .rotate {
  transform: rotate(-90deg);
}

/** Register as customer/cpo/consultant Forms */
.register-as-form {
  padding: 0 !important;
  gap: 2rem;
}
.register-as-form form {
  width: 100%;
}
.register-as-form label {
  margin: 0 0 0.2rem !important;
  font-weight: 400 !important;
  font-size: 0.875rem !important;
}
.register-as-form .form-row,
.register-as-form .ur-field-item {
  margin-bottom: 0rem !important;
}
.register-tag {
  text-align: center;
  width: fit-content;
  padding: 0.2rem 1rem;
  background-color: var(--g-200);
  color: var(--g-600);
  border-radius: 5px;
}
.register-tag.consultant-register {
  color: var(--o-600);
  background-color: var(--o-200);
}
.register-tag.cpo-register {
  color: var(--b-600);
  background-color: var(--b-200);
}

.register-as-form input {
  border: 2px solid var(--g-400) !important;
  border-radius: 5px !important;
  padding: 0.7rem 1rem !important;
}
.register-as-form .user-registration-password-strength {
  width: fit-content;
  position: absolute;
  right: 7%;
  margin-top: -41px;
  font-size: 0.875rem !important;
  font-weight: 400;
  border-radius: 5px;
}
.register-as-form .checkbox {
  width: auto;
  border: none;
}
.register-as-form .checkbox input {
  color-scheme: none;
  accent-color: var(--g-400);
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 20px !important;
}

.register-as-form .ur-submit-button {
  text-transform: capitalize;
  font-weight: 400;
  letter-spacing: normal;
  padding: 0.8em 1.5em !important;
  font-size: 1rem !important;
}
.register-as-form .ur-submit-button:hover {
  color: var(--g-600);
  background-color: var(--g-200);
}

/** Additional registration info pages */
.user-info-header {
  color: var(--black) !important;
  margin-bottom: 2rem !important;
  text-align: center;
}

#custom-vendor-details-form .two-column,
#additional-customer-details-form .two-column {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0;
}
#additional-customer-details-form .two-column {
  margin-bottom: 2rem;
}

#custom-vendor-details-form input,
#additional-customer-details-form .two-column input {
  width: 100%;
  border: 2px solid var(--g-200) !important;
  border-radius: 5px;
}

#custom-vendor-details-form input:focus,
#additional-customer-details-form .two-column input:focus {
  border: 2px solid var(--g-400) !important;
}

#custom-vendor-details-form,
#additional-customer-details-form {
  max-width: 550px !important;
  display: flex;
  flex-direction: column;
}

#custom-vendor-details-form button,
#additional-customer-details-form button {
  width: auto;
  align-self: flex-end;
}

#custom-vendor-details-form label,
#additional-customer-details-form .two-column label {
  font-size: 0.875rem;
  font-weight: 400;
}

#custom-vendor-details-form label:after,
#additional-customer-details-form .two-column label:after {
  content: " *";
  color: var(--text-warning);
}

#custom-vendor-details-form .submit-button {
  letter-spacing: normal;
  padding: 0.8em 1.5em !important;
  font-size: 1rem !important;
}
#custom-vendor-details-form .clear-signature-button {
  background-color: transparent;
  margin-top: 1rem;
}

#custom-vendor-details-form button:hover,
#additional-customer-details-form button:hover {
  color: var(--g-600);
  background-color: var(--g-200);
}

#custom-vendor-details-form .country-select.inside {
  width: 100%;
}

.miniorange-container {
  max-width: 420px !important;
}
