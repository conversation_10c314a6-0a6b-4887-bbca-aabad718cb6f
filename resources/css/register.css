/** Register Page */

.register-page-container {
  display: flex;
  flex-direction: column;
  padding-top: 2rem !important;
  align-items: center;
  gap: 3rem;
  max-width: 420px !important;
  border: none !important;
  border-bottom: 2px solid var(--g-200) !important;
}
.register-heading {
  color: var(--black) !important;
}

/** Register as customer/cpo/consultant Forms */
.register-as-form {
  padding: 0 !important;
  gap: 2rem;
}
.register-as-form form {
  width: 100%;
}
.register-as-form label {
  margin: 0 0 0.2rem !important;
  font-weight: 400 !important;
  font-size: 0.875rem !important;
}
.register-as-form .form-row,
.register-as-form .ur-field-item {
  margin-bottom: 0rem !important;
}

.register-as-form input {
  border: 2px solid var(--g-400) !important;
  border-radius: 5px !important;
  padding: 0.7rem 1rem !important;
}
.register-as-form .user-registration-password-strength {
  width: fit-content;
  position: absolute;
  right: 7%;
  margin-top: -41px;
  font-size: 0.875rem !important;
  font-weight: 400;
  border-radius: 5px;
}
.register-as-form .checkbox {
  width: auto;
  border: none;
}
.register-as-form .checkbox input {
  color-scheme: none;
  accent-color: var(--g-400);
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 20px !important;
}

.register-as-form .ur-submit-button {
  text-transform: capitalize;
  font-weight: 400;
  letter-spacing: normal;
  padding: 0.8em 1.5em !important;
  font-size: 1rem !important;
}
.register-as-form .ur-submit-button:hover {
  color: var(--g-600);
  background-color: var(--g-200);
}

/** Additional registration info pages */
.user-info-header {
  color: var(--black) !important;
  margin-bottom: 2rem !important;
  text-align: center;
}

#custom-vendor-details-form .two-column,
#additional-customer-details-form .two-column {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0;
}
#additional-customer-details-form .two-column {
  margin-bottom: 2rem;
}

#custom-vendor-details-form input,
#additional-customer-details-form .two-column input {
  width: 100%;
  border: 2px solid var(--g-200) !important;
  border-radius: 5px;
}

#custom-vendor-details-form input:focus,
#additional-customer-details-form .two-column input:focus {
  border: 2px solid var(--g-400) !important;
}

#custom-vendor-details-form,
#additional-customer-details-form {
  max-width: 550px !important;
  display: flex;
  flex-direction: column;
}

#custom-vendor-details-form button,
#additional-customer-details-form button {
  width: auto;
  align-self: flex-end;
}

#custom-vendor-details-form label,
#additional-customer-details-form .two-column label {
  font-size: 0.875rem;
  font-weight: 400;
}

#custom-vendor-details-form label:after,
#additional-customer-details-form .two-column label:after {
  content: " *";
  color: var(--text-warning);
}

#custom-vendor-details-form .submit-button {
  letter-spacing: normal;
  padding: 0.8em 1.5em !important;
  font-size: 1rem !important;
}
#custom-vendor-details-form .clear-signature-button {
  background-color: transparent;
  margin-top: 1rem;
}

#custom-vendor-details-form button:hover,
#additional-customer-details-form button:hover {
  color: var(--g-600);
  background-color: var(--g-200);
}

#custom-vendor-details-form .country-select.inside {
  width: 100%;
}

/** Additional Customer Details Form Styles */
#additional-customer-details-form input:focus {
  border: 2px solid #A5E6AA !important; /* ox-green-400 */
  outline: none !important;
  box-shadow: 0 0 0 2px rgba(165, 230, 170, 0.2) !important; /* ox-green-400 with opacity */
}

/* Ensure proper spacing between first and last name fields */
@media (max-width: 640px) {
  #additional-customer-details-form .grid-cols-1 > div:not(:last-child) {
    margin-bottom: 1rem;
  }
}

/* Override register-page-container styles for this page */
.register-page-container {
  max-width: 550px !important;
  border: none !important;
}

/* Add extra padding to checkbox label */
#additional-customer-details-form .flex.items-center label {
  padding-left: 10px !important;
}

/* Clean checkbox styles that match registration template */
input[type="checkbox"] {
  height: 1.5rem !important;
  width: 1.5rem !important;
  color: #A5E6AA !important;
  border: 2px solid #A5E6AA !important;
  border-radius: 5px !important;
  appearance: none !important;
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  background-color: white !important;
  padding: 0 0 0 5px !important;
  margin: 0 !important;
}

input[type="checkbox"]:checked {
  background-color: #A5E6AA !important;
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3e%3c/svg%3e") !important;
  background-size: 100% 100% !important;
  background-position: center !important;
  background-repeat: no-repeat !important;
}

input[type="checkbox"]:focus {
  outline: none !important;
  box-shadow: 0 0 0 2px rgba(165, 230, 170, 0.2) !important;
}

/* Ensure checkbox and label are properly aligned */
.flex.items-center {
  display: flex !important;
  align-items: center !important;
}

/** Additional Vendor Details Form Styles */
/* Override country-select styles to match the theme */
.country-select.inside .flag-dropdown:hover, 
.country-select.inside .flag-dropdown:focus {
  border-color: #A5E6AA !important; /* ox-green-400 */
}
.country-select.inside:focus, 
.country-select.inside:active, 
.country-select.inside.open {
  border-color: #A5E6AA !important; /* ox-green-400 */
}

/* Override register.css input styles */
#additional-vendor-details-form input:focus {
  border: 2px solid #A5E6AA !important; /* ox-green-400 */
  outline: none !important;
  box-shadow: 0 0 0 2px rgba(165, 230, 170, 0.2) !important; /* ox-green-400 with opacity */
}

/* Ensure full width for organization, business ID and country fields */
#organization_name, #business_id, #vendor_country {
  width: 100% !important;
}

/* Make country-select container full width */
.country-select.inside {
  width: 100% !important;
}

/* Fix country selector flag overlapping with text */
.country-select.inside .selected-flag {
  width: 40px !important;
}

.country-select.inside input {
  padding-left: 46px !important; /* Increased left padding to accommodate flag */
}

.country-select.inside input::placeholder {
  text-indent: 0 !important;
}

/* Ensure proper spacing between first and last name fields */
@media (max-width: 640px) {
  #additional-vendor-details-form .grid-cols-1 > div:not(:last-child) {
    margin-bottom: 1rem;
  }
}

/* Style the signature canvas to match input elements */
#vendor_signature_pad {
  background-color: white !important;
  border: 2px solid var(--g-400) !important;
  border-radius: 5px !important;
  padding: 0.5rem !important;
  min-height: 100px !important;
}

/* Style the clear signature button */
#clear_signature {
  color: #286444 !important; /* ox-green-600 */
  background-color: white !important;
  border: 2px solid #A5E6AA !important; /* ox-green-400 */
}

#clear_signature:hover {
  background-color: rgba(165, 230, 170, 0.1) !important; /* ox-green-400 with low opacity */
}

/** Template Register Styles */
.register-form input {
  appearance: none !important;
  border-radius: 0.5rem !important;
  width: 100% !important;
  padding: 0.5rem 0.75rem !important;
  border: 1px solid #D1D5DB !important; /* border-gray-300 */
  color: var(--ox-black) !important;
}

.register-form input:focus {
  outline: none !important;
  ring: 2px !important;
  ring-color: #A5E6AA !important; /* ox-green-400 */
  border-color: #A5E6AA !important; /* ox-green-400 */
  z-index: 10 !important;
}

.register-form .checkbox-container {
  display: flex !important;
  align-items: center !important;
}

.register-form button {
  font-weight: 400 !important;
}
