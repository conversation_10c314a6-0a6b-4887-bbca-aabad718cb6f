.footprint-example-page {
    /* Complex components */
    .progress-bar-triple {
        @apply relative w-[100px] h-[100px] rounded-full;
        background: radial-gradient(closest-side, white 79%, transparent 80% 100%),
                    conic-gradient(
                        theme('colors.ox-green.600') 0 var(--temp1),
                        theme('colors.ox-green.400') var(--temp1) calc(var(--temp1) + var(--temp2)),
                        theme('colors.ox-green.100') calc(var(--temp1) + var(--temp2)) calc(var(--temp1) + var(--temp2) + var(--temp3))
                    );
    }

    .half-square {
        @apply w-[25px] min-w-[25px] h-[25px] mt-2 self-end;
        border-bottom-left-radius: theme('borderRadius.md');
        border-left: 2px solid theme('colors.ox-green.200');
        border-bottom: 2px solid theme('colors.ox-green.200');
    }

    /* Tooltip styles */
    .tooltip-container {
        @apply relative inline-block;
    }

    .disc-tooltip {
        @apply hidden w-[300px] bg-white rounded-2xl border border-gray-200 p-4 text-left absolute z-10 bottom-[85%] shadow-lg opacity-0;
        transition: opacity 0.5s, display 0s 0.5s;
    }

    .disclaimer:hover .disc-tooltip {
        @apply block opacity-100;
        transition: opacity 0.5s;
    }

    /* Dropdown styles */
    .dropdown {
        @apply relative w-64 inline-block my-4;
    }

    .dropbtn {
        @apply flex justify-between items-center w-full text-ox-green-600 p-1 px-4 text-sm border-2 border-ox-green-400 cursor-pointer rounded-md transition-all;
    }

    .sidebar-dropdown-content {
        @apply hidden w-full absolute bg-white rounded-md shadow-lg z-10;
    }

    .dropdown:hover .sidebar-dropdown-content {
        @apply block;
    }

    /* Scope styles */
    .scope-container {
        @apply flex items-start gap-3;
    }

    .scope-list {
        @apply ml-6;
    }

    .scope {
        @apply my-2 flex items-start gap-3;
    }

    .scope .square {
        @apply w-6 h-6 rounded bg-ox-green-400;
    }

    .scope .darkSquare {
        @apply bg-ox-green-600;
    }

    .scope .normalSquare {
        @apply bg-ox-green-400;
    }

    .scope .lightSquare {
        @apply bg-ox-green-100;
    }

    .scope .scope-text {
        @apply font-semibold mr-1;
    }

    /* Bottom container and sidebar */
    .bottom-container {
        @apply flex flex-col mt-20 sm:mt-0;
    }

    .footprint-sidebar {
        @apply flex flex-wrap justify-center items-baseline mb-20 gap-6;
    }

    .example-page-sidebar {
        @apply my-6 w-[380px] sm:w-[300px] sm:mt-12;
    }

    .highlighted-item {
        @apply bg-[#FAFAF5] rounded-2xl p-6 flex flex-col items-center h-[450px];
    }

    .sidebar-image {
        @apply bg-ox-green-400 w-[50px] h-[50px] -mt-[3.7rem] p-2.5 rounded-md;
    }

    .inner-footprint-items {
        @apply flex flex-col items-center text-center mt-6;
    }

    .small-example-sidebar-heading {
        @apply mt-4 text-ox-green-600 flex w-32;
    }

    .scope-button-container {
        @apply flex gap-1 my-4;
    }

    .side-scope-item {
        @apply cursor-pointer text-ox-green-600 rounded-md border-2 border-ox-green-400 px-4 py-1 text-sm;
    }

    .side-scope-item.active {
        @apply bg-ox-green-400;
    }

    .total-carbon, .total-amount {
        @apply flex;
    }

    .big-text {
        @apply whitespace-nowrap text-5xl text-ox-green-600 my-6 mb-10;
    }

    .big-text.bolded-num {
        @apply font-semibold mr-2;
    }

    .next-highlight-item {
        @apply relative top-4 mx-[-3.5rem];
    }

    .next-highlight-item img {
        @apply w-20;
    }

    .footprint-desc {
        @apply my-4 mb-16 sm:mx-8;
    }

    .footprint-desc p {
        @apply mb-8 leading-8;
    }

    /* Hide scope values by default */
    .scope-val12, .scope-valTotal {
        @apply hidden;
    }

    /* Media queries */
    @media (max-width: 768px) {
        .scope-container,
        .scope-list .scope {
            @apply flex-col;
        }
        .scope-list {
            @apply m-0 flex gap-4;
        }
    }

    @media (max-width: 544px) {
        .scope-container {
            @apply items-center;
        }
    }

    @media screen and (max-width: 814px) {
        .next-highlight-item {
            @apply static rotate-90 basis-full self-center flex justify-center;
        }
    }

    @media screen and (max-width: 1210px) {
        #next-item-arrow-2 {
            @apply static rotate-90 basis-full self-center flex justify-center;
        }
    }
}

/* Swiper styles */
.communicate-stamp-swiper {
    width: 100%;
    padding: 20px 0;
}

.example-page-swiper {
    width: 100%;
    overflow: visible; /* Allow slides to overlap container */
}

/* Remove the wrapper constraints */
.example-page-swiper .swiper-wrapper {
    /* Remove fixed width/height */
    display: flex;
    align-items: center;
}

.example-page-swiper .swiper-slide {
    width: 140px !important;
    height: 140px !important;
    position: relative;
    transition: all 0.3s ease;
    transform-origin: center;
}

.example-page-swiper .swiper-slide svg {
    width: 140px !important;
    height: 140px !important;
    border-radius: 0.5rem;
}

.example-page-swiper .swiper-slide:not(.swiper-slide-active) {
    opacity: 0.4;
    transform: scale(0.85);
}

/* Remove shadows from coverflow effect */
.swiper-slide-shadow-left,
.swiper-slide-shadow-right {
    display: none;
} 