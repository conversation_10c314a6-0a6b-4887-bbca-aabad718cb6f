
/* Company Product Cards */
.company-featured-product,
.company-product {
    @apply bg-white rounded-lg shadow-sm overflow-hidden;
}

.company-featured-product .product-image,
.company-product .product-image {
    @apply w-full;
}

.company-featured-product .product-image img,
.company-product .product-image img {
    @apply w-full h-48 object-cover;
}

.company-featured-product .product-content,
.company-product .product-content {
    @apply p-6;
}

.company-featured-product .product-header,
.company-product .product-header {
    @apply flex justify-between items-start mb-4;
}

.company-featured-product .product-header h3,
.company-product .product-header h3 {
    @apply text-xl font-bold text-ox-black;
}

.company-featured-product .rating,
.company-product .rating {
    @apply flex items-center gap-2;
}

.company-featured-product .product-categories,
.company-product .product-categories {
    @apply flex flex-wrap gap-2 mb-4;
}

.company-featured-product .product-categories li,
.company-product .product-categories li {
    @apply bg-ox-green-400 text-ox-green-600 px-3 py-1 rounded-md text-sm;
}

.company-featured-product p,
.company-product p {
    @apply mb-6 text-ox-black;
}

/* Featured specific styles */
.company-featured-product {
    @apply border-2 border-ox-green-400;
}

/* Company Page Styles */
.company-info {
    @apply max-w-7xl mx-auto px-6 py-8;
}

/* Banner */
.company-info .banner {
    @apply w-full mb-8 rounded-lg overflow-hidden bg-ox-green-300;
    height: 202px; /* Previously 192px (h-48) + 10px */
}

.company-info .banner img {
    @apply w-full h-full object-cover;
}

/* Main Layout */
.company-info .main-info-container {
    @apply flex gap-8;
}

/* Sidebar (Left Column) */
.company-info .sidebar {
    @apply w-80 flex-shrink-0;
}

/* Main Content (Right Column) */
.company-info .main-content-container {
    @apply flex-grow;
}

.company-info .common-content {
    @apply bg-white rounded-lg p-6 mb-8;
}

/* Title Container */
.company-info .title-container {
    @apply flex items-center gap-6 mb-4;
}

.company-info .company-header {
    @apply text-ox-black relative pr-6;
}

/* Add vertical divider after company name */
.company-info .company-header::after {
    content: '';
    @apply absolute right-0 top-1/2 -translate-y-1/2 w-[1px] h-8 bg-ox-black;
}

.company-info .inner-rating {
    @apply flex items-center gap-2;
}

.company-info .company-rating {
    @apply text-lg font-semibold text-ox-black;
}

.company-info .no-rating {
    @apply text-sm font-normal;
}

/* Product Types */
.company-info .product-types {
    display: flex;
    flex-wrap: wrap;
    margin: calc(1rem + 5px) 0 2rem;
    gap: 5px;
}

.company-info .type {
    @apply bg-ox-green-200 text-ox-green-600 rounded-md text-sm;
    padding: 0.8em 1em;
}

/* Content Navigation */
.company-info .button-container {
    @apply flex gap-8 mb-8;
}

.project-card-content .card-footer .cart {
    margin-bottom: 0;
}