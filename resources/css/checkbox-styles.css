/* Clean checkbox styles for forms */
input[type="checkbox"] {
    height: 1.5rem !important;
    width: 1.5rem !important;
    color: #A5E6AA !important;
    border: 2px solid #A5E6AA !important;
    border-radius: 5px !important;
    appearance: none !important;
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    background-color: white !important;
    padding: 0 0 0 5px !important;
    margin: 0 !important;
    vertical-align: middle !important;
}

input[type="checkbox"]:checked {
    background-color: #A5E6AA !important;
    background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3e%3c/svg%3e") !important;
    background-size: 100% 100% !important;
    background-position: center !important;
    background-repeat: no-repeat !important;
}

input[type="checkbox"]:focus {
    outline: none !important;
    box-shadow: 0 0 0 2px rgba(165, 230, 170, 0.2) !important;
}

/* Ensure checkbox and label are properly aligned */
.flex.items-center {
    display: flex !important;
    align-items: center !important;
}

/* Improve label vertical alignment */
.flex.items-center label {
    display: flex !important;
    align-items: center !important;
    line-height: 1.5rem !important;
    padding-top: 1px !important;
}

/* Specific style for customer details checkbox label */
.checkbox-label {
    padding-left: 10px !important;
} 