.category-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
  max-width: 100%;
  width: 100%;
}

.loading-placeholder {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 1rem;
}

.loading-square {
  background-color: #e1f0dc;
  height: 40px;
  width: 40px;
  border-radius: 5px;
}

.loading-rectangle {
  background-color: #e1f0dc;
  height: 20px;
  width: 150px;
  border-radius: 5px;
}

@media (max-width: 1024px) {
  .category-grid,
  .loading-placeholder {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    max-width: auto;
  }
  .main-container {
    max-width: auto;
  }
  .subcategory-container {
    flex-direction: column !important;
  }
  .step-container {
    max-width: 70vw !important;
  }
  .breadcrumbs > ul {
    white-space: normal !important;
    flex-wrap: wrap !important;
  }
  .breadcrumbs > ul > li {
    margin-top: 1rem !important;
  }
  .category-heading {
    text-align: center !important;
  }
}

@media (max-width: 768px) {
  .main-container {
    padding-left: 0 !important;
  }

  .calculator-breadcrumbs > ul {
    flex-wrap: wrap;
  }

  .calculator-breadcrumbs > ul > li {
    margin-top: 1rem;
  }
}

@media (max-width: 544px) {
  .step-container {
    max-width: 100% !important;
  }
  .category-grid,
  .loading-placeholder {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
  }
  .calculated-value-container {
    flex-direction: row;
  }
  .loading-rectangle {
    max-width: 5rem;
  }
  .category-heading {
    text-align: left !important;
    margin-bottom: 20px !important;
  }
  .calculated-value-container {
    flex-direction: column !important;
    gap: 1.5rem !important;
  }
  .new-footprint-button {
    text-align: left;
    max-width: 7em;
  }
  .calculated-value-container {
    align-items: flex-start;
  }
}

.category-item {
  display: flex;
  align-items: center;
  gap: 10px;
  text-align: left;
  color: #0a2222 !important;
  font-size: 16px !important;
  font-family: ’Rubik’, sans-serif;
  font-weight: 400 !important;
  line-height: 1.8em !important;
}

.category-icon-placeholder {
  background-color: #e1f0dc;
  height: 40px;
  width: 40px;
  border-radius: 5px;
}

.category-text-placeholder {
  background-color: #e1f0dc;
  height: 20px;
  width: 150px;
  border-radius: 5px;
}

.category-button {
  min-width: 50px;
  background-color: #a5e6aa;
  border: none;
  border-radius: 5px;
  background-color: #a5e6aa;
}

.category-button:hover {
  background-color: #e1f0dc;
}

.category-button svg {
  border-radius: 5px;
}

.subcategory-container {
  display: flex;
  flex-direction: row;
  gap: 1.5rem; /* Ensure the gap matches your design */
  box-sizing: border-box;
  justify-content: space-between;
}

.subcategory-options {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.subcategory-select {
  padding: 0.8em 1.5em !important;
  font-size: 1.125rem !important;
  border-radius: 5px !important;
  border: 2px solid #a5e6aa !important;
  background-color: #fff;
  color: #286444 !important;
  width: 100%;
}

.subcategory-select:focus {
  border-color: theme('colors.ox-green.500') !important;
  outline: none !important;
}

.subcategory-label {
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  color: #0a2222;
  text-align: left !important;
  height: 100%;
}

.calculated-value-container {
  display: flex;
  flex-direction: row;
  gap: 1rem;
}

.calculated-value {
  display: flex;
  align-items: flex-start;
}

.calculated-value-number {
  margin-bottom: 0.5rem;
  text-decoration: underline;
  text-underline-offset: 8px;
  text-decoration-color: #6ec376;
  color: #0a2222;
}

.calculated-value-text {
  font-size: 0.875rem;
  color: black;
}

.main-container {
  position: relative;
  border-radius: 2rem;
  width: 100%;
  min-height: 380px;
  padding: 2.5rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.inner-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  height: 100%;
}

.calculator-breadcrumbs > ul {
  display: flex;
  align-items: center;
  font-size: 0.875rem;
  margin-bottom: 1.25rem;
  margin-left: 0.5rem;
}

.calculator-breadcrumbs > ul > li:not(:last-child):not(:first-child) {
  opacity: 50%;
}

.calculator-breadcrumbs > ul > li:not(:last-child) .breadcrumb-label {
  opacity: 50%;
}

.calculator-breadcrumbs > ul > li:last-child .breadcrumb-item {
  color: #0a2222;
}

.calculator-breadcrumbs > ul > li {
  display: flex;
  align-items: center;
  color: #286444;
}

.calculator-breadcrumbs > ul > li:not(:first-child)::before {
  content: "\2022";
  padding: 0 1rem;
  opacity: 50%;
}

.breadcrumb-item {
  display: flex;
  gap: 1.25rem;
  cursor: pointer;
  color: #286444;
}

.breadcrumb-button {
  min-width: 50px;
  background-color: #a5e6aa;
  border-radius: 5px;
}

.breadcrumb-button:hover {
  background-color: #e1f0dc !important;
}

.breadcrumb-button svg {
  border-radius: 5px !important;
}

.breadcrumb-label {
  align-self: center;
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.8em;
  padding-top: 2px;
}

.category-heading {
  color: #0a2222 !important;
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1.4em;
  letter-spacing: 0.05em;
  text-align: left !important;
  margin-bottom: 0 !important;
  margin-top: 2rem !important;
}

.step-container {
  margin-top: 1.5rem;
  flex: 1;
  height: 100%;
  width: 100%;
}

.input-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.input-wrapper {
  width: fit-content;
}

.input-field {
  padding: 0.8em 1.5em !important;
  font-size: 1.125rem !important;
  border-radius: 5px !important;
  background-color: #fff !important;
  border: 2px solid #e1f0dc !important;
  color: #c8dcc8 !important;
}

.input-field:focus {
  border: 2px solid #a5e6aa !important;
  color: #286444 !important;
}

.footprint-input {
  padding: 0.8em 1.5em;
  font-size: 1.125rem;
  border-radius: 5px;
  background-color: #fff;
  border: 2px solid #e1f0dc;
  color: #c8dcc8;
}

.footprint-input:focus {
  border: 2px solid #a5e6aa;
  color: #286444;
}

.footprint-input:focus {
  border: 2px solid #a5e6aa !important;
}

.navigation-buttons {
  display: flex;
  justify-content: space-between;
  width: 100%;
  align-items: center;
  margin-top: 4.5rem;
  font-size: 16px !important;
}

.previous-button {
  color: #0a2222;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 400 !important;
  line-height: 1.4em !important;
  letter-spacing: 0.02em !important;
}

.previous-button svg {
  min-width: 16px !important;
  max-width: 16px !important;
}

.next-button,
.certify-button {
  padding: 0.6em 1.5em;
  font-size: 1rem;
  border-radius: 5px;
  background-color: #a5e6aa !important;
  transition: background-color 100ms ease;
  color: #286444 !important;
  margin-right: 1rem;
  margin-bottom: 1rem;
}

.certify-button {
  padding: 0.5em 1.5em !important;
}

.next-button:hover,
.certify-button:hover {
  background-color: #e1f0dc !important;
}

.offset-button,
.certify-button {
  text-align: left !important;
}

.offset-button {
  color: #286444 !important;
  padding: 0.4em 1.5em;
  font-size: 1rem;
  border-radius: 5px;
  background-color: #fff;
  transition: background-color 100ms ease;
  border: 2px solid #a5e6aa !important;
}

.offset-button:hover {
  background-color: #e1f0dc;
  border: 2px solid #a5e6aa !important;
  outline: none !important;
}

.calculations {
  display: inline-flex;
  flex-wrap: wrap;
  width: 100%;
}

.calculated-value-container {
  gap: 2rem !important;
  align-items: flex-end;
}

.calculated-value {
  background-color: #e1f0dc !important;
  border-radius: 5px !important;
  padding: 1em;
  align-items: center !important;
}

.calculated-value-number {
  margin-bottom: 0 !important;
  text-decoration: none !important;
  align-items: center !important;
  color: #286444 !important;
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1.4em;
  letter-spacing: 0.02em;
}

.calculated-value-text {
  color: #286444 !important;
  font-size: 1.5rem;
  font-weight: 400;
  line-height: 1.4em;
  letter-spacing: 0.02em;
  margin-left: 5px;
}

.new-footprint-button {
  cursor: pointer;
  color: #286444 !important;
  font-weight: 400 !important;
  line-height: 1.4em !important;
  letter-spacing: 0.02em !important;
}

.new-footprint-button::before {
  content: "+";
  font-size: 1.5rem;
  display: inline-flex;
  width: 1rem !important;
  height: 1rem !important;
  color: #286444 !important;
  margin-right: 0.5rem;
}

.additional-info {
  display: inline-flex;
  flex-wrap: wrap;
}

.certify-offset-buttons {
  width: max-content;
  display: flex;
  justify-content: left;
}

.datasource-container {
  text-align: left;
  padding-bottom: 2rem;
  text-align: left;
  color: #0a2222 !important;
  font-size: 16px !important;
  font-family: ’Rubik’, sans-serif;
  font-weight: 400 !important;
  line-height: 1.8em !important;
}

.datasource-container > * {
  width: auto !important;
}

.info-and-value-container {
  display: inline-flex;
  align-items: baseline;
  width: 100%;
  padding-bottom: 2rem;
  flex-wrap: wrap;
}

.info-value {
  width: max-content;
  margin-right: 1rem;
  margin-bottom: 1rem;
  overflow-wrap: break-word;
  word-wrap: break-word;
  hyphens: auto;
}
