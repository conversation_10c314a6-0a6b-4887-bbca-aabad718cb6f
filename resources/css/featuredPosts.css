.frontpage-featured-posts .uagb-post__title {
	color: theme('colors.ox-black') !important;
	font-weight: 700 !important;
	font-size: 1.25rem !important;
	padding-top: 1rem;
	text-transform: uppercase !important;
	line-height: 1.5em;
}

.frontpage-featured-posts {
	column-gap: 25px !important;
	row-gap: 25px !important;
}

.frontpage-featured-posts .uagb-post__inner-wrap .uagb-post__taxonomy {
	text-transform: none !important;
}
.frontpage-featured-posts .uagb-post__inner-wrap .uagb-post__title {
	display: inline-flex;
	margin-top: -0.5rem;
}

@media (max-width: 976px) {
.frontpage-featured-posts .uagb-post__inner-wrap .uagb-post__title {
	margin-top: 0 !important;
}
}

.frontpage-featured-posts .uagb-post__inner-wrap .uagb-post__taxonomy a {
	font-size: 16px !important;
}

.frontpage-featured-posts .uagb-post__inner-wrap .uagb-post__taxonomy span {
	display: none !important;
}

.frontpage-featured-posts .uagb-post__inner-wrap .uagb-post__text .uagb-post__excerpt {
	padding-left: 1.5rem;
    padding-right: 1.5rem;
    margin-bottom: 1.5rem;
	overflow-wrap: break-word;
}

.frontpage-featured-posts .uagb-post__inner-wrap {
	border-radius: 20px !important;
	background-color: theme('colors.ox-green.100') !important;
}

.frontpage-featured-posts .uagb-post__image a img {
	border-radius: 20px 20px 5px 5px;
	aspect-ratio: 16 / 9 !important;
	object-fit: cover !important;
}

.frontpage-featured-posts .uagb-post__inner-wrap .uagb-post__cta .wp-block-button__link {
    text-transform: none !important;
    border-radius: 5px !important;
    background: theme('colors.ox-green.400') !important;
    color: theme('colors.ox-green.600') !important;
    font-weight: 700 !important;
    padding: 0.8em 1em !important; /* Adjusted padding */
    display: inline-flex !important;
    align-items: center !important; /* Added for vertical alignment */
    font-size: 16px !important;
    letter-spacing: 0.02em !important;
    height: 2.5em;
    line-height: 1 !important; /* Added to prevent line-height issues */
    gap: 5px !important; /* Added for consistent spacing between text and icon */
}

.frontpage-featured-posts .uagb-post__inner-wrap .uagb-post__cta .wp-block-button__link:hover {
	background-color: theme('colors.ox-green.200') !important;
	border-color: theme('colors.ox-green.200') !important;
}

/* Add Font Awesome arrow icon to Read more button */
.frontpage-featured-posts .uagb-post__inner-wrap .uagb-post__cta .wp-block-button__link::after {
	content: '\f061'; /* Font Awesome arrow-right icon */
	font-family: 'Font Awesome 5 Free', 'Font Awesome 5 Pro';
	font-weight: 900;
	display: inline-block;
	margin-left: 5px !important;
	color: theme('colors.ox-green.600') !important;
	transition: transform 0.2s ease;
	position: relative;
	font-size: 14px;
}

/* Add hover effect for smooth transition */
.frontpage-featured-posts .uagb-post__inner-wrap .uagb-post__cta .wp-block-button__link:hover::after {
	transform: translateX(3px); /* Add slight movement on hover */
}

/* Base tag styles */
.frontpage-featured-posts .uagb-post__text .uagb-post__taxonomy a {
    text-decoration: none;
    height: 2.5em;
    padding-left: 0.6em;
    padding-right: 0.6em;
    border-radius: 5px;
    background: theme('colors.ox-green.400');
    align-items: center;
    color: theme('colors.ox-green.600');
    font-weight: normal; /* Reset default font weight */
    display: inline-flex;
    justify-content: center;
    margin-bottom: 5px !important;
}

.frontpage-featured-posts .uagb-post__text .uagb-post__taxonomy a:hover {
	background-color: theme('colors.ox-green.200');
	border-color: theme('colors.ox-green.200');
}

.frontpage-featured-posts .uagb-post-grid .uagb-post__inner-wrap .uagb-post__taxonomy.default {
	margin: 0 !important;
	position: absolute !important;
  	top: 1.5em !important;
	display: inline-flex !important;
	flex-wrap: wrap !important;
	width: 30% !important;
}

.frontpage-featured-posts .uagb-post__inner-wrap .uagb-post__taxonomy.default {
	position: absolute !important;
	top: 1.5em !important;
	width: 30% !important;
	margin-top: 1em;
	margin-bottom: 1em;
}

@media (max-width: 976px) {
.frontpage-featured-posts .uagb-post__inner-wrap .uagb-post__taxonomy.default {
		position: relative !important;
		top: 0 !important;
		width: 100% !important;
	}
}

@media (max-width: 976px) {
.frontpage-featured-posts .uagb-post__inner-wrap {
	padding: 0 !important;
	margin: 20px !important;
}

/* Ensure consistent left/right spacing for all elements */
.frontpage-featured-posts .uagb-post__inner-wrap .uagb-post__taxonomy.default,
.frontpage-featured-posts .uagb-post__title,
.frontpage-featured-posts .uagb-post__excerpt,
.frontpage-featured-posts .uagb-post__cta {
	padding-left: 1.5rem !important;
	padding-right: 1.5rem !important;
	margin-left: 0 !important;
	margin-right: 0 !important;
}

/* Specific button container adjustments */
.frontpage-featured-posts .uagb-post__inner-wrap .uagb-post__cta {
	margin-bottom: 1.5rem !important;
	margin-top: 1.5rem !important;
}

/* Ensure the button link itself doesn't have additional margins */
.frontpage-featured-posts .uagb-post__inner-wrap .uagb-post__cta .wp-block-button__link {
	margin-left: 0 !important;
	margin-right: 0 !important;
}
}

/* Primary Category Styles */
.frontpage-featured-posts .uagb-post__text .uagb-post__taxonomy a:first-of-type {
	background-color: theme('colors.ox-green.400');
	color: theme('colors.ox-green.600') !important;
	font-weight: 700 !important; /* Bold for primary category */
	text-decoration: none;
	border-radius: 5px;
	margin-right: 5px;
	white-space: nowrap;
	width: min-content;
	padding: 0.3em 0.3em;
	border: 2px solid theme('colors.ox-green.400');
	margin-bottom: 0.1em;
}

.frontpage-featured-posts .uagb-post__text .uagb-post__taxonomy a:first-of-type:hover {
	background-color: theme('colors.ox-green.200');
	border-color: theme('colors.ox-green.200');
}

/* Secondary Category Styles */
.frontpage-featured-posts .uagb-post__text .uagb-post__taxonomy a:nth-child(2) {
	height: 2.5em;
	background-color: theme('colors.ox-green.200');
	border: 2px solid theme('colors.ox-green.200');
	color: theme('colors.ox-green.600') !important;
	font-weight: normal !important; /* Normal weight for secondary category */
	border-radius: 5px;
	text-decoration: none;
	white-space: nowrap;
	width: fit-content;
	margin-right: 5px;
}

.frontpage-featured-posts .uagb-post__text .uagb-post__taxonomy a:nth-child(2):hover {
	border: 2px solid theme('colors.ox-green.400');
	background-color: theme('colors.ox-green.200');
}

.documentation-row {
	padding-top: 3em !important;
	padding-bottom: 1em !important;
	border-top: 2px solid theme('colors.ox-green.200') !important;
	display: flex !important;
	justify-content: space-between !important;
	align-items: center !important;
}

@media (max-width: 976px) {
	.documentation-row {
		padding-left: 1.5em !important;
		padding-right: 1.5em !important;
		/* Remove flex-direction: column to keep side-by-side layout */
	}

	.documentation-container .documentation-title {
		margin-bottom: 1em !important; /* Add space between title and button on mobile */
	}

	.documentation-container .documentation-row .documentation-button-container {
		margin-left: 1em !important; /* Add some space between title and button */
	}

	.documentation-container .documentation-row .documentation-button-container .documentation-button .wp-block-button__link {
		width: 100% !important; /* Full width button on mobile */
		text-align: center !important;
	}
}

.documentation-container .documentation-title {
	margin: 0 !important; /* Remove default margins */
}

.documentation-container .documentation-row .documentation-button-container .documentation-button .wp-block-button__link,
.featured-projects-button .wp-block-button__link,
.featured-projects-button.wp-block-button__link {
	padding: 0.5em 1.2em !important;
	font-size: 14px !important;
	color: theme('colors.ox-green.600') !important;
	border-radius: 5px !important;
	background-color: #FFF !important;
	transition: background-color 100ms ease !important;
	border: 2px solid theme('colors.ox-green.400') !important;
	text-transform: none !important;
	letter-spacing: 0.02em !important;
	font-weight: 400 !important;
	white-space: nowrap !important; /* Prevent button text from wrapping */
	display: inline-block !important; /* Changed from inline-flex */
}

/* Special styling for featured-consultants-button */
.featured-consultants-button .wp-block-button__link,
.featured-consultants-button.wp-block-button__link {
	padding: 0.5em 1.2em !important;
	font-size: 14px !important;
	color: theme('colors.ox-green.600') !important;
	border-radius: 5px !important;
	background-color: transparent !important; /* Remove background color */
	transition: all 100ms ease !important;
	border: 2px solid theme('colors.ox-green.400') !important;
	text-transform: none !important;
	letter-spacing: 0.02em !important;
	font-weight: 400 !important;
	white-space: nowrap !important; /* Prevent button text from wrapping */
}

.documentation-container .documentation-row .documentation-button-container .documentation-button .wp-block-button__link:hover,
.featured-projects-button .wp-block-button__link:hover,
.featured-projects-button.wp-block-button__link:hover {
	background-color: theme('colors.ox-green.200') !important;
}

/* Arrow styles removed */

/* Special hover styling for featured-consultants-button */
.featured-consultants-button .wp-block-button__link:hover,
.featured-consultants-button.wp-block-button__link:hover {
	background-color: theme('colors.ox-green.200') !important; /* Change to green-200 on hover */
	color: theme('colors.ox-green.600') !important; /* Keep same text color on hover */
	border-color: theme('colors.ox-green.400') !important; /* Keep same border color */
}

.documentation-container .documentation-row .documentation-button-container .documentation-button .wp-block-button__link:focus,
.featured-projects-button .wp-block-button__link:focus,
.featured-projects-button.wp-block-button__link:focus,
.documentation-container .documentation-row .documentation-button-container .documentation-button .wp-block-button__link:focus-visible,
.featured-projects-button .wp-block-button__link:focus-visible,
.featured-projects-button.wp-block-button__link:focus-visible {
	outline: 2px solid theme('colors.ox-green.400') !important;
	outline-offset: 2px !important;
}

/* Special focus styling for featured-consultants-button */
.featured-consultants-button .wp-block-button__link:focus,
.featured-consultants-button.wp-block-button__link:focus,
.featured-consultants-button .wp-block-button__link:focus-visible,
.featured-consultants-button.wp-block-button__link:focus-visible {
	outline: 2px solid theme('colors.ox-green.400') !important;
	outline-offset: 2px !important;
	background-color: transparent !important; /* Keep transparent background on focus */
}
