/* Scope all styles to product page to avoid affecting other pages */
.product-page {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

/* Two Column Layout */
.product-page .product-header-grid,
.product-page .product-secondary-grid {
    @apply grid grid-cols-1 lg:grid-cols-2 gap-16 mb-4;
}

/* Secondary Grid Specific Layout */
.product-page .secondary-left-column {
    @apply w-full;
}

/* Product Images */
.product-page .product-images {
    @apply w-full;
}

.product-page .main-image {
    @apply w-full rounded-lg overflow-hidden flex items-center justify-start;
    min-height: 400px;
    border-radius: 10px;
}

.product-page .main-image img {
    @apply max-w-full max-h-[400px] object-contain;
    border-radius: 10px;
}

/* Product Thumbnails Carousel */
.product-page .product-thumbnails-carousel {
    @apply w-full;
    height: 100px;
}

.thumbnailSwiper {
    @apply w-full h-full;
}

.thumbnailSwiper .swiper-wrapper {
    @apply gap-4 h-full;
}

.thumbnailSwiper .swiper-slide {
    @apply flex items-center justify-center bg-ox-green-100 rounded-lg overflow-hidden;
    width: calc(25% - 12px) !important;
    height: 100% !important;
    cursor: pointer;
    border-radius: 5px;
}

.product-page .thumbnail-slide {
    @apply w-full h-full relative transition-all duration-200;
}

.product-page .thumbnail-slide.active {
    @apply ring-2 ring-ox-green-400;
}

.product-page .thumbnail-image {
    @apply w-full h-full;
}

/* Swiper Navigation Buttons */
.thumbnailSwiper .swiper-button-next,
.thumbnailSwiper .swiper-button-prev {
    color: theme('colors.ox-green.600');
    width: 24px;
    height: 24px;
    margin-top: -12px;
}

.thumbnailSwiper .swiper-button-next:after,
.thumbnailSwiper .swiper-button-prev:after {
    font-size: 16px;
    font-weight: bold;
}

/* Swiper button hover states */
.thumbnailSwiper .swiper-button-next:hover,
.thumbnailSwiper .swiper-button-prev:hover {
    color: theme('colors.ox-green.400');
}

/* Disable buttons when at end */
.thumbnailSwiper .swiper-button-disabled {
    opacity: 0.35;
    cursor: auto;
    pointer-events: none;
}

/* Right Column - Product Info */
.product-page .info-column {
    @apply w-full;
}

/* Status Badges */
.product-page .status-badges {
    @apply flex flex-wrap gap-4 mb-12;
}

.product-page .badge {
    @apply inline-flex items-center gap-1 px-3 py-1.5 text-sm font-medium;
}

.product-page .badge img {
    @apply w-5 h-5;
}

.product-page .badge.active {
    @apply bg-ox-green-200 text-ox-green-600 rounded-md;
    padding-top: 0.2rem;
    padding-bottom: 0.2rem;
    padding-left: 0.4rem;
    padding-right: 0.8rem;
}

.product-page .badge.not-active {
    @apply bg-ox-orange-200 text-ox-green-600 rounded-md;
    padding-top: 0.2rem;
    padding-bottom: 0.2rem;
    padding-left: 0.4rem;
    padding-right: 0.8rem;
}

.product-page .badge.certified {
    @apply bg-transparent text-ox-black rounded-full;
    gap: 1rem;
}

.product-page .badge.certified img {
    width: 1.75rem;
    height: 1.75rem;
}

/* Product Title and Description */
.product-page .product-title {
    @apply text-3xl font-bold text-ox-black mb-8;
    text-transform: none;
}

.product-page .product-description {
    @apply text-base text-ox-black mb-12 max-w-3xl;
    font-family: "Inter", sans-serif;
    font-weight: 400;
    line-height: 2;
}

.product-page .product-description p {
    @apply text-ox-black;
    font-family: "Inter", sans-serif;
    font-weight: 400;
    line-height: 2;
}

/* Project Meta */
.product-page .project-meta {
    @apply flex items-start gap-8;
    margin-top: -2rem;
}

/* Score Circle */
.product-page .score-container {
    @apply flex-shrink-0 relative flex flex-col items-center;
    width: 138px;
}

.product-page .score-circle {
    @apply w-full h-[138px] rounded-full flex items-center justify-center;
    transform: scalex(-1);
    background: 
        radial-gradient(closest-side, white 82%, transparent 83% 100%),
        conic-gradient(theme('colors.ox-green.400') var(--score), theme('colors.ox-green.600') 0);
}

.product-page .score {
    @apply text-3xl font-bold text-ox-green-600;
    transform: scalex(-1);
}

/* Ask for Offer button in score container */
.product-page .score-container .ask-for-offer-btn {
    @apply flex items-center px-4 py-2 rounded-md text-sm font-normal w-full mt-5;
    border: 2px solid theme('colors.ox-green.400');
    color: theme('colors.ox-green.600');
    background-color: transparent;
    transition: all 100ms ease;
    text-align: center;
    justify-content: center;
}

.product-page .score-container .ask-for-offer-btn:hover {
    background-color: theme('colors.ox-green.200');
}

.product-page .score-container .ask-for-offer-btn:focus {
    outline: 2px solid theme('colors.ox-green.400');
    outline-offset: 2px;
}

/* Meta Info */
.product-page .info {
    @apply flex-grow;
}

.product-page .info-item {
    @apply mb-0 last:mb-0;
}

.product-page .info-item {
    @apply flex items-baseline gap-0;
}

.product-page .info-item strong {
    @apply text-sm font-bold text-ox-black shrink-0;
    width: 120px;
}

.product-page .info-item .greentext {
    @apply text-ox-green-500 text-base flex-grow ml-4;
}

.product-page .info-item.lower-item {
    @apply mt-2;
}

/* SDGs */
.product-page .sdgs-container {
    @apply flex flex-wrap gap-2 mt-6;
}

.product-page .sdg-image {
    @apply flex-shrink-0;
    width: 32px;
    height: 32px;
}

.product-page .sdg-image img {
    @apply w-full h-full rounded;
}

/* Info Icons */
.product-page .info-icons {
    @apply mt-6;
}

/* Price Bar */
.product-page .price-bar {
    @apply flex items-center justify-between mb-12 mt-12 bg-ox-green-100 p-4 rounded-lg relative;
}

/* Price Bar - No Price Variant */
.product-page .price-bar--no-price {
    @apply flex-col;
}

.product-page .price-bar--no-price .left-price-bar {
    @apply w-full;
}


.product-page .price-bar--no-price .amount-box {
    @apply justify-start;
}

/* Left side with price and available tonnes */
.product-page .left-price-bar {
    @apply flex items-center gap-12;
}


/* Price section */
.product-page .tonne-price-box {
    @apply flex items-center gap-2;
}

.product-page .tonne-price {
    @apply text-sm text-ox-black whitespace-normal block;
    max-width: 100px;
    margin-right: 1rem;
}

.product-page .pricebar-value-p {
    @apply px-3 py-3 bg-ox-green-200 rounded-md text-base font-bold text-ox-green-600 whitespace-nowrap;
}

/* Available tonnes section */
.product-page .amount-box {
    @apply flex items-center gap-2;
}

.product-page .available-amount {
    @apply text-sm text-ox-black whitespace-normal block;
    max-width: 100px;
    margin-right: 1rem;
}

.product-page .stock-quantity {
    @apply px-3 py-3 bg-ox-green-200 rounded-md text-base font-bold text-ox-green-600 whitespace-nowrap;
}

.product-page .out-of-stock {
    @apply px-3 py-3 bg-red-100 rounded-md text-base font-bold text-red-600 whitespace-nowrap;
}

.product-page .low-stock {
    @apply px-3 py-3 bg-yellow-100 rounded-md text-base font-bold text-yellow-600 whitespace-nowrap;
}

/* Right side with input and cart button */
.product-page .cart {
    @apply flex flex-col gap-4 pl-8;
    margin-bottom: 0;
}

/* Price calculator table styles */
.product-page .wc-measurement-price-calculator-price-table {
    @apply w-full;
}

.product-page .price-table-content {
    @apply flex justify-between;
    gap: 0.5rem;
}

/* Quantity input container */
.product-page .quantity-input-container {
    @apply relative flex-grow;
}

.product-page .amount_needed {
    @apply w-full px-4 py-2 border rounded-md text-ox-green-600 font-bold bg-white 
           border-ox-green-400 ring-1 ring-ox-green-400 outline-none;
    /* Remove Webkit spinners */
    &::-webkit-outer-spin-button,
    &::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
    }
    /* Remove Firefox spinners */
    -moz-appearance: textfield;
}

/* Units styling */
.product-page .units {
    @apply absolute right-4 top-1/2 -translate-y-1/2 flex items-center gap-2;
}

.product-page .units-value {
    @apply text-ox-green-600 font-bold;
    font-size: 1rem;
}

/* Add to cart button */
.product-page .single_add_to_cart_button {
    @apply flex items-center gap-2 bg-ox-green-400 text-ox-green-600 rounded-md 
           hover:bg-ox-green-200 transition-colors duration-200 font-medium justify-center;
    padding-left: 1rem !important;
    padding-right: 1rem !important;
}

.product-page .single_add_to_cart_button > p{
    @apply text-ox-green-600;
}
/* Purchase type info and subscription */
.product-page .disclaimer {
    @apply mt-2 text-sm text-ox-black flex items-center justify-between;
}

.product-page .disclaimer .purchase-type {
    @apply flex items-center gap-1;
    position: relative;
}

.product-page .subscription-checkbox {
    @apply flex items-center gap-2;
}

.product-page .subscription-checkbox label {
    @apply flex items-center gap-2 text-sm text-ox-black;
}

.product-page .subscription-checkbox input[type="checkbox"] {
    @apply w-4 h-4 border-2 border-ox-green-400 rounded text-ox-green-600 
           focus:ring-ox-green-400 focus:ring-offset-0;
    accent-color: theme('colors.ox-green.400');
}

.product-page .question-circle {
    @apply flex items-center justify-center w-4 h-4 rounded-full bg-ox-green-400 
           text-ox-green-600 text-xs font-bold cursor-help;
}

.product-page .disc-tooltip {
    @apply hidden absolute p-2 bg-white rounded-md shadow-lg text-xs w-48;
    bottom: -120%;
    right: 0;
}

.product-page .disclaimer:hover .disc-tooltip {
    @apply block;
}

/* Content Grid */
.product-page .product-content-grid {
    @apply grid grid-cols-1 lg:grid-cols-[1fr_2fr] gap-8;
}

/* Left Column */
.product-page .left-column > section {
    @apply bg-white rounded-lg p-6 mb-6;
}

.product-page .left-column h2 {
    @apply text-ox-black text-lg font-bold mb-6 ml-4;
    margin-top: 0.5rem;
    letter-spacing: 0.05em;
}

/* Project Type Card */
.product-page .type-card {
    @apply space-y-4;
}

.product-page .type-flex {
    @apply flex items-center gap-4 relative;
    padding-left: 1rem;
}

.product-page .type-flex::before {
    content: '';
    @apply absolute bg-ox-green-100 rounded-lg;
    left: 1rem;
    top: 0;
    bottom: 0;
    right: 0;
    z-index: 0;
}

.product-page .type-flex img {
    @apply relative z-10;
    width: 33px;
    height: 33px;
    margin-left: -1rem;
}

.product-page .type-content {
    @apply relative z-10 py-4 pr-4;
}

.product-page .type-content h3 {
    @apply text-sm font-bold text-ox-black mb-1;
    letter-spacing: 0.02em;
    text-transform: none;
}

.product-page .type-content p {
    @apply text-lg text-ox-black font-normal;
}

/* Co-benefits */
.product-page .benefit-card {
    @apply flex items-start gap-4 mb-4 last:mb-0;
}

.product-page .benefit-card svg {
    @apply w-8 h-8 flex-shrink-0;
    fill: theme('colors.ox-green.600');
}

.product-page .benefit-content h3 {
    @apply text-base font-medium text-ox-black mb-1;
    text-transform: none;
}

.product-page .benefit-content p {
    @apply text-sm text-ox-black;
}

/* Project Details */
.product-page .detail-row {
    @apply flex items-start gap-4 mb-6 last:mb-0;
}

.product-page .detail-row .icon-column {
    @apply flex-shrink-0;
}

.product-page .detail-row .icon-column svg {
    @apply w-6 h-6;
    fill: theme('colors.ox-black');
}

/* Special styling for specific icons */
.product-page .detail-row .icon-column svg[data-filename="general_certification.svg"],
.product-page .detail-row .icon-column svg[data-filename="general_location.svg"],
.product-page .detail-row .icon-column svg[data-filename="general_contact.svg"] {
    fill: theme('colors.ox-black');
}

.product-page .detail-row .content-column {
    @apply flex-grow;
}

.product-page .detail-row .content-column .small-header {
    @apply text-sm font-bold text-ox-black mb-2 ml-0 mt-0;
    letter-spacing: 0.02em;
    text-transform: none;
}

.product-page .detail-row .content-column p {
    @apply text-sm text-ox-black;
}

.product-page .detail-row .content-column .link {
    @apply text-ox-green-600 hover:text-ox-green-400 transition-colors duration-200 
           hover:underline underline-offset-2;
}

/* Right Column */
.product-page .right-column > section {
    @apply bg-white rounded-lg p-6 mb-6 shadow-sm;
}

.product-page .right-column h3 {
    @apply text-ox-black font-bold;
    font-size: 1.5rem;
    line-height: 1.4em;
    letter-spacing: .05em;
    text-transform: uppercase;
    margin-bottom: 1.5rem;
}

/* Content Sections */
.product-page .content {
    @apply text-base leading-relaxed text-ox-black;
}

.product-page .content > * {
    @apply mb-4 last:mb-0;
}

.product-page .content h1 {
    @apply text-3xl font-bold mb-4;
}

.product-page .content h2 {
    @apply text-2xl font-bold mb-3;
}

.product-page .content h3 {
    @apply text-lg font-medium text-ox-black mb-2;
    text-transform: none;
}

.product-page .content h4 {
    @apply text-lg font-bold mb-2;
}

.product-page .content ul {
    @apply list-disc list-inside;
}

.product-page .content ol {
    @apply list-decimal list-inside;
}

.product-page .content a {
    @apply text-ox-green-600 hover:text-ox-green-400 transition-colors duration-200 
           hover:underline underline-offset-2;
}

.product-page .content blockquote {
    @apply pl-4 border-l-4 border-ox-green-200 italic my-4;
}

.product-page .content img {
    @apply rounded-lg my-4;
}

/* Benefits List */
.product-page .benefits-list {
    @apply mt-4 space-y-2 list-none;
}

.product-page .benefits-list li {
    @apply flex items-start gap-2 text-sm text-ox-black;
}

.product-page .benefits-list li::before {
    content: '•';
    @apply text-ox-green-600 font-bold;
}

/* Map Section */
.product-page .map-section iframe {
    @apply w-full h-[300px] rounded-lg;
}

/* Consultation Service Specific Styles */
.product-page.consultation-service .left-column > section:first-of-type {
   margin-bottom: 0;
}

/* Consulting Service Price Bar */
.product-page.consultation-service .price-bar {
    @apply px-8 py-4 flex items-center justify-between;
}

.product-page.consultation-service .left-price-bar {
    @apply flex items-center gap-12;
}

.product-page.consultation-service .cart {
    @apply flex items-center gap-4;
}

.product-page.consultation-service .single_add_to_cart_button {
    @apply w-auto;
    width: fit-content;
}

/* Consulting Service Ask for Offer Button */
.product-page.consultation-service .ask-for-offer-btn {
    @apply flex items-center px-4 py-2 rounded-md text-sm font-medium text-ox-green-600;
    border: 2px solid theme('colors.ox-green.400');
    background-color: transparent;
    transition: all 100ms ease;
    width: fit-content;
    margin-top: 3rem;
}

.product-page.consultation-service .ask-for-offer-btn:hover {
    background-color: theme('colors.ox-green.200');
}

.product-page.consultation-service .ask-for-offer-btn:focus {
    outline: 2px solid theme('colors.ox-green.400');
    outline-offset: 2px;
}

.product-page.consultation-service .info {
    @apply flex flex-col;
}

/* Move the button from info to under service category */
.product-page.consultation-service .project-type {
    @apply relative;
}

.product-page.consultation-service .project-type .ask-for-offer-btn {
    margin-top: 3rem;
    margin-left: 1rem;
}

@media (max-width: 1024px) {
    .product-page.consultation-service .price-bar {
        @apply flex-col gap-6;
    }

    .product-page.consultation-service .left-price-bar {
        @apply w-full grid grid-cols-2 gap-4;
    }

    .product-page.consultation-service .amount-box {
        @apply order-2;
    }

    .product-page.consultation-service .tonne-price-box {
        @apply order-1;
    }

    .product-page.consultation-service .cart {
        @apply w-full border-t border-ox-green-200 pt-6 flex items-start;
    }

    .product-page.consultation-service .cart > div {
        @apply flex items-center gap-4 w-auto;
        justify-content: flex-start;
        margin-left: 0;
        padding-left: 0 !important;
        gap: 0.25rem;
    }

    .product-page.consultation-service .cart .pricebar-value-p {
        @apply mr-4;
    }

    .product-page.consultation-service .cart .single_add_to_cart_button {
        @apply ml-0;
        width: fit-content;
    }
}

@media (max-width: 768px) {
    .product-page.consultation-service .price-bar {
        @apply px-4;
    }

    .product-page.consultation-service .left-price-bar {
        @apply flex flex-col gap-4;
    }

    .product-page.consultation-service .amount-box,
    .product-page.consultation-service .tonne-price-box {
        @apply w-full justify-between order-none;
    }

    .product-page.consultation-service .cart {
        @apply justify-start;
    }

    .product-page.consultation-service .cart > div {
        @apply flex items-center justify-start gap-4;
        width: fit-content;
        margin-left: 0;
    }

    .product-page.consultation-service .tonne-price,
    .product-page.consultation-service .consultation-time-estimate {
        @apply text-left;
        text-align: left;
    }

    .product-page.consultation-service .consultation-time-estimate-time,
    .product-page.consultation-service .consultation-time-estimate-estimate {
        text-align: left;
    }
}

/* Consulting Service Price Bar Labels */
.product-page.consultation-service .tonne-price {
    @apply text-sm text-ox-black whitespace-normal block text-right;
    max-width: 100px;
    margin-right: 1rem;
}

.product-page.consultation-service .available-amount {
    @apply text-sm text-ox-black whitespace-normal block text-right;
    max-width: 100px;
    margin-right: 1rem;
    line-height: 1.2;
    position: relative;
}

.product-page.consultation-service .available-amount::before {
    content: "Time";
    display: block;
    text-align: right;
    position: absolute;
    right: 0;
    top: 0;
}

.product-page.consultation-service .available-amount::after {
    content: "estimate:";
    display: block;
    text-align: right;
    position: absolute;
    right: 0;
    top: 1.2em;
}

.product-page.consultation-service .available-amount {
    visibility: hidden;
}

/* Remove the pseudo-element content for consultation service */
.product-page.consultation-service .available-amount::after,
.product-page.consultation-service .available-amount::before {
    content: none;
}

/* Show the original text in consultation service */
.product-page.consultation-service .available-amount {
    display: block;
}

/* Hide the original text only in consultation service */
.product-page.consultation-service .available-amount span {
    display: none;
}

/* Consulting Service Time Estimate Label */
.consultation-time-estimate {
    @apply text-sm text-ox-black whitespace-normal block text-right;
    max-width: 100px;
    margin-right: 1rem;
    line-height: 1.2;
}

.consultation-time-estimate-time {
    display: block;
    text-align: right;
}

.consultation-time-estimate-estimate {
    display: block;
    text-align: right;
}

/* Responsive Adjustments */
@media (max-width: 1024px) {
    /* Two Column Layout */
    .product-page .product-header-grid,
    .product-page .product-secondary-grid,
    .product-page .product-content-grid {
        @apply grid-cols-1 gap-8;
    }
    
    /* Column order for tablet and mobile */
    .product-page .product-content-grid .left-column {
        order: 2;
    }
    
    .product-page .product-content-grid .right-column {
        order: 1;
    }
    
    /* Project sections order */
    .product-page .about-project {
        order: 1;
    }
    
    .product-page .impact-description {
        order: 2;
    }
    
    .product-page .project-type {
        order: 3;
    }
    
    .product-page .co-benefits {
        order: 4;
    }
    
    .product-page .project-details {
        order: 5;
    }
    
    /* Price Bar */
    .product-page .price-bar {
        @apply flex-col gap-6;
    }

    .product-page .left-price-bar {
        @apply w-full flex-row flex-wrap gap-4;
    }

    .product-page .tonne-price-box,
    .product-page .amount-box {
        @apply flex-1 min-w-[200px];
    }

    .product-page .amount-box {
        @apply justify-end;
    }

    .product-page .cart {
        @apply w-full pl-0;
    }

    .product-page .price-table-grid {
        @apply grid-cols-1 gap-4;
    }

    .product-page .price-table-content,
    .product-page .single_add_to_cart_button {
        @apply col-span-1;
    }

    .product-page .disclaimer,
    .product-page .subscription-checkbox {
        @apply col-span-1 justify-start;
    }

    /* Project Meta */
    .product-page .project-meta {
        @apply flex-row items-center gap-6 flex-wrap;
        margin-top: 2rem;
    }

    .product-page .info {
        @apply w-full;
    }

    /* Score Circle */
    .product-page .score-container {
        @apply flex-row items-center gap-8;
        width: auto;
        height: auto;
    }

    .product-page .score-circle {
        width: 120px;
        height: 120px;
        flex-shrink: 0;
    }

    .product-page .score-container .ask-for-offer-btn {
        @apply w-auto whitespace-nowrap mt-0;
    }
}

@media (max-width: 768px) {
    /* General Spacing */
    .product-page {
        @apply px-4;
    }

    /* Headers */
    .product-page .product-title {
        @apply text-2xl mb-6;
    }

    /* Status Badges */
    .product-page .status-badges {
        @apply flex-wrap gap-2 mb-8;
    }

    /* Project Details */
    .product-page .detail-row {
        @apply flex-col gap-2;
    }

    .product-page .detail-row .icon-column {
        @apply mb-2;
    }

    /* Price Bar */
    .product-page .tonne-price,
    .product-page .available-amount {
        @apply text-sm max-w-full;
    }

    .product-page .pricebar-value-p,
    .product-page .stock-quantity {
        @apply text-sm;
    }

    /* Cart Form */
    .product-page .quantity-input-container {
        @apply w-full;
    }

    .product-page .single_add_to_cart_button {
        @apply w-full justify-center py-3;
    }

    /* SDGs */
    .product-page .sdgs-container {
        @apply gap-1;
    }

    .product-page .sdg-image {
        width: 28px;
        height: 28px;
    }

    /* Left Column Sections */
    .product-page .left-column > section {
        @apply p-4;
    }

    /* Right Column Sections */
    .product-page .right-column > section {
        @apply p-4;
    }

    .product-page .right-column h3 {
        @apply text-xl mb-4;
    }

    /* Benefits */
    .product-page .benefit-card {
        @apply gap-3;
    }

    .product-page .benefit-card svg {
        @apply w-6 h-6;
    }

    /* Type Cards */
    .product-page .type-flex,
    .product-page .sdg-flex {
        @apply pl-0;
    }

    .product-page .type-flex::before,
    .product-page .sdg-flex::before {
        @apply left-0;
    }

    .product-page .type-flex img,
    .product-page .sdg-flex img {
        @apply ml-0;
        width: 28px;
        height: 28px;
    }
}

@media (max-width: 480px) {
    /* Further adjustments for mobile */
    .product-page .product-header-grid,
    .product-page .product-secondary-grid,
    .product-page .product-content-grid {
        @apply gap-6;
    }

    /* Main Image */
    .product-page .main-image {
        aspect-ratio: 16/9;
        border-radius: 0.5rem !important;
    }

    /* Thumbnails */
    .product-page .product-thumbnails-carousel {
        height: 70px;
    }

    .thumbnailSwiper .swiper-slide {
        width: calc(33.333% - 8px) !important;
    }

    /* Score */
    .product-page .score-container {
        @apply flex-row items-center gap-6;
    }

    .product-page .score-circle {
        width: 100px;
        height: 100px;
    }

    .product-page .score {
        @apply text-2xl;
    }

    /* Price Bar */
    .product-page .price-bar {
        @apply p-3;
    }

    .product-page .left-price-bar {
        @apply flex-col;
    }

    .product-page .tonne-price-box,
    .product-page .amount-box {
        @apply w-full min-w-0 flex-none;
        justify-content: space-between;
    }

    .product-page .pricebar-value-p,
    .product-page .stock-quantity,
    .product-page .out-of-stock,
    .product-page .low-stock {
        @apply ml-auto;
    }

    /* Cart Form */
    .product-page .disclaimer {
        @apply flex-col gap-2;
    }

    .product-page .disc-tooltip {
        @apply left-0 right-auto;
        width: 200px;
    }
}

/* Add custom scrollbar styling for Webkit browsers */
.product-page .image-gallery::-webkit-scrollbar {
    @apply h-1.5;
}

.product-page .image-gallery::-webkit-scrollbar-track {
    @apply bg-ox-green-100 rounded-full;
}

.product-page .image-gallery::-webkit-scrollbar-thumb {
    @apply bg-ox-green-400 rounded-full;
}

/* Price calculator grid positioning */
.product-page .price-table-grid {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 0.8rem;
    align-items: center;
}

.product-page .price-table-content {
    grid-row: 1;
    grid-column: 1;
    @apply flex items-center;
}

.product-page .single_add_to_cart_button {
    grid-row: 1;
    grid-column: 2;
    @apply py-2 px-0;
}

.product-page .disclaimer {
    grid-row: 2;
    grid-column: 1;
    @apply flex items-center m-0 justify-end;
}

.product-page .subscription-checkbox {
    grid-row: 2;
    grid-column: 2;
    @apply flex items-center justify-start m-0;
}

.product-page .content p {
    margin-bottom: 1.5rem;
}

.product-page .content p:last-child {
    margin-bottom: 0;
}

/* SDG List Cards */
.product-page .sdg-list {
    @apply space-y-4 border-b border-ox-green-300 pb-12;
}

.product-page .sdg-flex {
    @apply flex items-center gap-4 relative;
    padding-left: 1rem;
}

.product-page .sdg-flex::before {
    content: '';
    @apply absolute bg-ox-green-100 rounded-lg;
    left: 1rem;
    top: 0;
    bottom: 0;
    right: 0;
    z-index: 0;
}

.product-page .sdg-flex img {
    @apply relative z-10;
    width: 33px;
    height: 33px;
    margin-left: -1rem;
}

.product-page .sdg-content {
    @apply relative z-10 py-4 pr-4;
}

.product-page .sdg-content h3 {
    @apply text-sm font-bold text-ox-black mb-1;
    letter-spacing: 0.02em;
    text-transform: none;
}

@media (min-width: 769px) {
    .product-page .tonne-price,
    .product-page .available-amount {
        @apply text-right;
    }
}

.product-page.consultation-service .project-type h2 {
    @apply mb-0; /* This adds 3rem (48px) margin bottom */
} 
