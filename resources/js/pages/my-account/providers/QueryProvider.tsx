import React from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

// Create a single shared QueryClient instance
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutes
      retry: 1
    }
  }
});

interface QueryProviderProps {
  children: React.ReactNode;
}

/**
 * Provides a React Query context to its children
 * Use this to wrap components that need to use React Query hooks
 */
const QueryProvider: React.FC<QueryProviderProps> = ({ children }) => {
  return (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );
};

export default QueryProvider;

/**
 * Higher-order component that wraps a component with QueryProvider
 */
export function withQueryProvider<T extends object>(
  Component: React.ComponentType<T>
): React.FC<T> {
  return (props: T) => (
    <QueryProvider>
      <Component {...props} />
    </QueryProvider>
  );
}
