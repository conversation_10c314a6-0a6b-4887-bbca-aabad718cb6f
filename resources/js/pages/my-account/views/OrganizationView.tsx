import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle
} from '../../../components/ui/card';
import { But<PERSON> } from '../../../components/ui/button';
import { Input } from '../../../components/ui/input';
import { Label } from '../../../components/ui/label';
import { Badge } from '../../../components/ui/badge';
import {
  AlertCircle,
  Building,
  Plus,
  X,
  Users,
  Target,
  Edit2,
  Check
} from 'lucide-react';
import { Organization, OffsetProcess, OrganizationUser } from '../types';
import { useConfirm } from '../../../components/ui/confirm-dialog';

interface OrganizationResponse {
  success: boolean;
  data: Organization | null;
  message?: string;
}

interface OrganizationApiResponse {
  success: boolean;
  data: Organization;
  message: string;
}

const fetchOrganization = async (): Promise<Organization | null> => {
  const response = await fetch('/api/v1/organization', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json'
    }
  });

  if (!response.ok) {
    throw new Error('Failed to fetch organization');
  }

  const result: OrganizationResponse = await response.json();
  return result.data;
};

const createOrganization = async (name: string): Promise<Organization> => {
  const response = await fetch('/api/v1/organization', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ name })
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to create organization');
  }

  const result: OrganizationApiResponse = await response.json();
  return result.data;
};

const updateOrganization = async (name: string): Promise<Organization> => {
  const response = await fetch('/api/v1/organization', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ name })
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to update organization');
  }

  const result: OrganizationApiResponse = await response.json();
  return result.data;
};

const addUserToOrganization = async (email: string): Promise<Organization> => {
  const response = await fetch('/api/v1/organization/users', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ email })
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to add user to organization');
  }

  const result: OrganizationApiResponse = await response.json();
  return result.data;
};

const removeUserFromOrganization = async (
  userId: number
): Promise<Organization> => {
  const response = await fetch('/api/v1/organization/users', {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ user_id: userId })
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to remove user from organization');
  }

  const result: OrganizationApiResponse = await response.json();
  return result.data;
};

const OrganizationView: React.FC = () => {
  const [isCreating, setIsCreating] = useState(false);
  const [newOrgName, setNewOrgName] = useState('');
  const [isAddingUser, setIsAddingUser] = useState(false);
  const [newUserEmail, setNewUserEmail] = useState('');
  const [isEditingName, setIsEditingName] = useState(false);
  const [editedOrgName, setEditedOrgName] = useState('');
  const queryClient = useQueryClient();
  const confirm = useConfirm();

  const { data: organization, isLoading, error } = useQuery({
    queryKey: ['organization'],
    queryFn: fetchOrganization
  });

  const createOrgMutation = useMutation({
    mutationFn: createOrganization,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['organization'] });
      setIsCreating(false);
      setNewOrgName('');
    },
    onError: (error: Error) => {
      alert(error.message);
    }
  });

  const updateOrgMutation = useMutation({
    mutationFn: updateOrganization,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['organization'] });
      setIsEditingName(false);
      setEditedOrgName('');
    },
    onError: (error: Error) => {
      alert(error.message);
    }
  });

  const addUserMutation = useMutation({
    mutationFn: addUserToOrganization,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['organization'] });
      setIsAddingUser(false);
      setNewUserEmail('');
    },
    onError: (error: Error) => {
      alert(error.message);
    }
  });

  const removeUserMutation = useMutation({
    mutationFn: removeUserFromOrganization,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['organization'] });
    },
    onError: (error: Error) => {
      alert(error.message);
    }
  });

  const handleCreateOrganization = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (newOrgName.trim()) {
      createOrgMutation.mutate(newOrgName.trim());
    }
  };

  const handleUpdateOrganization = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (editedOrgName.trim() && editedOrgName.trim() !== organization?.name) {
      updateOrgMutation.mutate(editedOrgName.trim());
    } else {
      setIsEditingName(false);
      setEditedOrgName('');
    }
  };

  const handleEditName = () => {
    setEditedOrgName(organization?.name || '');
    setIsEditingName(true);
  };

  const handleCancelEdit = () => {
    setIsEditingName(false);
    setEditedOrgName('');
  };

  const handleAddUser = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (newUserEmail.trim()) {
      addUserMutation.mutate(newUserEmail.trim());
    }
  };

  const handleRemoveUser = async (user: OrganizationUser) => {
    const confirmed = await confirm({
      title: 'Remove User',
      body: `Are you sure you want to remove ${user.name} from the organization?`
    });

    if (confirmed) {
      removeUserMutation.mutate(user.id);
    }
  };

  // Check if current user is the creator
  const isCreator = organization && organization.creator_id;

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <p className="text-gray-600">Failed to load organization data</p>
        </div>
      </div>
    );
  }

  if (!organization) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold">Organization</h1>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building className="h-5 w-5" />
              Create Organization
            </CardTitle>
          </CardHeader>
          <CardContent>
            {!isCreating ? (
              <div className="text-center py-8">
                <Building className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  No Organization Found
                </h3>
                <p className="text-gray-600 mb-6">
                  You don't belong to any organization yet. Create one to start
                  collaborating with your team.
                </p>
                <Button onClick={() => setIsCreating(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Organization
                </Button>
              </div>
            ) : (
              <form onSubmit={handleCreateOrganization} className="space-y-4">
                <div>
                  <Label htmlFor="org-name">Organization Name</Label>
                  <Input
                    id="org-name"
                    type="text"
                    value={newOrgName}
                    onChange={e => setNewOrgName(e.target.value)}
                    placeholder="Enter organization name"
                    required
                  />
                </div>
                <div className="flex gap-2">
                  <Button
                    type="submit"
                    disabled={createOrgMutation.isPending || !newOrgName.trim()}
                  >
                    {createOrgMutation.isPending ? 'Creating...' : 'Create'}
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      setIsCreating(false);
                      setNewOrgName('');
                    }}
                  >
                    Cancel
                  </Button>
                </div>
              </form>
            )}
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Organization</h1>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Building className="h-5 w-5" />
              {!isEditingName ? (
                <span>{organization.name}</span>
              ) : (
                <form
                  onSubmit={handleUpdateOrganization}
                  className="flex items-center gap-2"
                >
                  <Input
                    type="text"
                    value={editedOrgName}
                    onChange={e => setEditedOrgName(e.target.value)}
                    className="text-2xl font-semibold h-8"
                    required
                    autoFocus
                  />
                  <Button
                    type="submit"
                    size="sm"
                    variant="ghost"
                    disabled={
                      updateOrgMutation.isPending || !editedOrgName.trim()
                    }
                  >
                    <Check className="h-4 w-4" />
                  </Button>
                  <Button
                    type="button"
                    size="sm"
                    variant="ghost"
                    onClick={handleCancelEdit}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </form>
              )}
            </div>
            {!isEditingName && isCreator && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleEditName}
                className="ml-2"
              >
                <Edit2 className="h-4 w-4" />
              </Button>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-8">
            <div>
              <h3 className="text-lg font-medium mb-4 flex items-center gap-2">
                <Users className="h-5 w-5" />
                Team Members ({organization.users.length})
              </h3>

              <div className="space-y-3">
                {organization.users.map(user => (
                  <div
                    key={user.id}
                    className="flex items-center justify-between p-3 border rounded-lg"
                  >
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center">
                        {user.avatar ? (
                          <img
                            src={user.avatar}
                            alt={user.name}
                            className="w-full h-full rounded-full object-cover"
                          />
                        ) : (
                          <span className="text-green-600 text-sm">
                            {user.name.charAt(0)}
                          </span>
                        )}
                      </div>
                      <div>
                        <p className="font-medium">{user.name}</p>
                        <p className="text-sm text-gray-500">{user.email}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {user.id.toString() === organization.creator_id && (
                        <Badge variant="outline">Creator</Badge>
                      )}
                      {user.id.toString() !== organization.creator_id &&
                        isCreator && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleRemoveUser(user)}
                            disabled={removeUserMutation.isPending}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        )}
                    </div>
                  </div>
                ))}
              </div>

              {isCreator && !isAddingUser ? (
                <Button
                  variant="outline"
                  onClick={() => setIsAddingUser(true)}
                  className="mt-4 w-full"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Team Member
                </Button>
              ) : isCreator && isAddingUser ? (
                <form onSubmit={handleAddUser} className="mt-4 space-y-3">
                  <div>
                    <Label htmlFor="user-email">User Email</Label>
                    <Input
                      id="user-email"
                      type="email"
                      value={newUserEmail}
                      onChange={e => setNewUserEmail(e.target.value)}
                      placeholder="Enter user email"
                      required
                    />
                  </div>
                  <div className="flex gap-2">
                    <Button
                      type="submit"
                      size="sm"
                      disabled={
                        addUserMutation.isPending || !newUserEmail.trim()
                      }
                    >
                      {addUserMutation.isPending ? 'Adding...' : 'Add'}
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setIsAddingUser(false);
                        setNewUserEmail('');
                      }}
                    >
                      Cancel
                    </Button>
                  </div>
                </form>
              ) : null}
            </div>

            <div>
              <h3 className="text-lg font-medium mb-4 flex items-center gap-2">
                <Target className="h-5 w-5" />
                Offset Processes ({organization.offset_processes?.length || 0})
              </h3>

              {organization.offset_processes &&
              organization.offset_processes.length > 0 ? (
                <div className="space-y-3">
                  {organization.offset_processes.map(process => (
                    <div key={process.id} className="p-3 border rounded-lg">
                      <div className="flex items-center justify-between">
                        <h4 className="font-medium">{process.name}</h4>
                        <Badge variant="outline">
                          {process.step.charAt(0).toUpperCase() +
                            process.step.slice(1)}
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-500 mt-1">
                        Created by user ID: {process.user_id}
                      </p>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 border-2 border-dashed border-gray-200 rounded-lg">
                  <Target className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-gray-500">No offset processes yet</p>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default OrganizationView;
