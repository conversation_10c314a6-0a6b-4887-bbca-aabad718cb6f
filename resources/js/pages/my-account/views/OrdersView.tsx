import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { createColumnHelper, ColumnDef } from '@tanstack/react-table';
import DataTable from '../components/ui/DataTable';
import { Order } from '../types/orders';
import { useOrders } from '../hooks/useOrders';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

// Create a new queryClient
const queryClient = new QueryClient();

// Wrap component with QueryClientProvider
const OrdersViewWithQueryProvider: React.FC = () => {
  return (
    <QueryClientProvider client={queryClient}>
      <OrdersView />
    </QueryClientProvider>
  );
};

const OrdersView: React.FC = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(25);

  const { data, isLoading, error } = useOrders(currentPage, pageSize);
  const orders = data?.data?.orders || [];
  const pagination = data?.data?.pagination || {
    total: 0,
    max_pages: 1,
    current_page: 1,
    per_page: 25
  };

  // Create column definitions
  const columnHelper = createColumnHelper<Order>();

  const columns: ColumnDef<Order, any>[] = [
    columnHelper.accessor('order_number', {
      header: 'Order ID',
      cell: info => <span className="font-medium">#{info.getValue()}</span>
    }),
    columnHelper.accessor('formatted_date', {
      header: 'Date',
      cell: info => info.getValue()
    }),
    columnHelper.accessor('formatted_total', {
      header: 'Total',
      cell: info => (
        <span
          className="font-medium"
          dangerouslySetInnerHTML={{ __html: info.getValue() }}
        ></span>
      )
    }),
    columnHelper.accessor('items_count', {
      header: 'Items',
      cell: info => info.getValue()
    }),
    columnHelper.accessor('status_name', {
      header: 'Status',
      cell: info => {
        const status = info.row.original.status;
        let statusClass = '';

        // Apply styling based on status
        switch (status) {
          case 'completed':
            statusClass = 'bg-green-100 text-green-800';
            break;
          case 'processing':
            statusClass = 'bg-blue-100 text-blue-800';
            break;
          case 'on-hold':
            statusClass = 'bg-yellow-100 text-yellow-800';
            break;
          case 'cancelled':
            statusClass = 'bg-red-100 text-red-800';
            break;
          case 'refunded':
            statusClass = 'bg-purple-100 text-purple-800';
            break;
          case 'failed':
            statusClass = 'bg-red-100 text-red-800';
            break;
          case 'pending':
            statusClass = 'bg-gray-100 text-gray-800';
            break;
          default:
            statusClass = 'bg-gray-100 text-gray-800';
        }

        return (
          <span
            className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${statusClass}`}
          >
            {info.getValue()}
          </span>
        );
      }
    }),
    columnHelper.accessor('payment_method', {
      header: 'Payment'
    }),
    columnHelper.display({
      id: 'actions',
      header: 'Actions',
      cell: info => (
        <div className="flex items-center space-x-2">
          <Link
            to={`/my-account/orders/${info.row.original.id}`}
            className="text-blue-600 hover:text-blue-800 hover:underline"
          >
            View
          </Link>
        </div>
      )
    })
  ];

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handlePageSizeChange = (size: number) => {
    setPageSize(size);
    setCurrentPage(1); // Reset to first page when changing page size
  };

  if (error) {
    return (
      <div className="p-8 bg-white rounded-lg shadow">
        <div className="text-red-500">
          <p>Error loading orders: {error.message}</p>
          <button
            onClick={() =>
              queryClient.invalidateQueries({ queryKey: ['orders'] })
            }
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold">My Orders</h1>
      </div>

      <DataTable
        columns={columns}
        data={orders}
        loading={isLoading}
        currentPage={pagination.current_page}
        maxPages={pagination.max_pages}
        pageSize={pageSize}
        onPageChange={handlePageChange}
        onPageSizeChange={handlePageSizeChange}
        pageSizeOptions={[25, 50, 100]}
        className="bg-white shadow-sm"
      />
    </div>
  );
};

export default OrdersViewWithQueryProvider;
