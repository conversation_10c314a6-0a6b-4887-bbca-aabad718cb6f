import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import ProductForm from '../components/ProductForm';
import { AllowedProductTypes } from '../types/products';
import { ChevronLeft } from 'lucide-react';

const fetchAllowedProductTypes = async (): Promise<AllowedProductTypes> => {
  const response = await fetch('/api/v1/products/allowed-types', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json'
    },
    credentials: 'include'
  });

  const data = await response.json();
  if (!data.success) {
    throw new Error(data.message || 'Failed to fetch allowed product types');
  }

  return data.data;
};

const ProductCreateView: React.FC = () => {
  const navigate = useNavigate();

  const { data: allowedTypesData } = useQuery({
    queryKey: ['allowedProductTypes'],
    queryFn: fetchAllowedProductTypes
  });

  const handleSuccess = () => {
    navigate('/products');
  };

  const handleCancel = () => {
    navigate('/products');
  };

  return (
    <div>
      <div className="flex items-center gap-3 mb-4">
        <button
          onClick={handleCancel}
          className="p-2 border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
        >
          <ChevronLeft className="w-4 h-4" />
        </button>
        <h1 className="text-xl font-medium">Create New Product</h1>
      </div>

      <ProductForm
        allowedTypes={allowedTypesData?.allowed_types || []}
        onSuccess={handleSuccess}
        onCancel={handleCancel}
      />
    </div>
  );
};

export default ProductCreateView;
