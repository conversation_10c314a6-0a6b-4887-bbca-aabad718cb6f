import React from 'react';
import CarbonGoalCard from '../components/CarbonGoalCard';
import RevenueCard from '../components/RevenueCard';
import CustomersCard from '../components/CustomersCard';
import MessagesCard from '../components/MessagesCard';
import OrdersTable from '../components/OrdersTable';
import { CarbonGoal, Revenue, Customers, Message, Order } from '../types';

interface DashboardViewProps {
  carbonGoal: CarbonGoal;
  revenue: Revenue;
  customers: Customers;
  messages: Message[];
  orders: Order[];
}

const DashboardView: React.FC<DashboardViewProps> = ({
  carbonGoal,
  revenue,
  customers,
  messages,
  orders
}) => {
  return (
    <div>
      <h1 className="text-2xl font-bold mb-6">DASHBOARD</h1>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <CarbonGoalCard carbonGoal={carbonGoal} />
        <RevenueCard revenue={revenue} />
        <CustomersCard customers={customers} />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <MessagesCard messages={messages} className="md:col-span-1" />
        <OrdersTable orders={orders} className="md:col-span-2" />
      </div>
    </div>
  );
};

export default DashboardView;
