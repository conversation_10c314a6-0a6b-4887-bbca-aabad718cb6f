import { cn } from '@/lib/utils';
import React from 'react';

const ChatView = () => {
  const getCookie = (name: string) => {
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2)
      return parts
        .pop()
        ?.split(';')
        .shift();
    return null;
  };
  const token = getCookie('chat_token');
  console.log('🚀 ~ ChatView ~ token:', token);
  const url = `${
    import.meta.env.VITE_ROCKET_CHAT_URL
  }/home?resumeToken=${token}`;
  console.log('🚀 ~ ChatView ~ url:', url);
  return (
    <iframe
      id="rocketchat-iframe"
      className={cn('w-full h-full')}
      src={url}
      sandbox="allow-same-origin allow-scripts allow-popups allow-forms"
    />
  );
};

export default ChatView;
