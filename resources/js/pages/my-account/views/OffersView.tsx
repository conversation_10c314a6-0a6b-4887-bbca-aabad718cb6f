import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Offer } from '../types/offers';

// Mock API function - in a real app, this would call an actual API
const fetchOffers = async (): Promise<Offer[]> => {
  // Simulating API call delay
  await new Promise(resolve => setTimeout(resolve, 1000));

  // Mock data
  return [
    {
      id: 'off1',
      title: 'Eco-friendly Product Bundle',
      description: 'Bundle of sustainable products at a discounted price',
      type: 'bundle',
      discountType: 'percentage',
      discountValue: 15,
      startDate: '2023-06-01',
      endDate: '2023-08-31',
      status: 'active',
      carbonSavings: 12.5,
      products: [
        { id: 'prod1', name: 'Sustainable T-shirt', quantity: 2 },
        { id: 'prod3', name: 'Recycled Paper Notebook', quantity: 1 }
      ]
    },
    {
      id: 'off2',
      title: 'Summer Carbon Offset Sale',
      description: 'Special discount for carbon-neutral products',
      type: 'discount',
      discountType: 'fixed',
      discountValue: 10,
      startDate: '2023-07-01',
      endDate: '2023-07-31',
      status: 'upcoming',
      carbonSavings: 8.3,
      products: [
        { id: 'prod2', name: 'Eco-friendly Jeans', quantity: 1 },
        { id: 'prod4', name: 'Bamboo Water Bottle', quantity: 1 }
      ]
    },
    {
      id: 'off3',
      title: 'Zero Waste Promotion',
      description: 'Buy one, get one free on selected zero waste products',
      type: 'bogo',
      discountType: 'percentage',
      discountValue: 100,
      startDate: '2023-05-15',
      endDate: '2023-06-15',
      status: 'expired',
      carbonSavings: 15.2,
      products: [{ id: 'prod5', name: 'Organic Cotton Hoodie', quantity: 1 }]
    }
  ];
};

const OffersView: React.FC = () => {
  const [filter, setFilter] = useState('all');

  const { data: offers = [], isLoading, error } = useQuery({
    queryKey: ['offers'],
    queryFn: fetchOffers
  });

  // Filter offers based on status
  const filteredOffers =
    filter === 'all' ? offers : offers.filter(offer => offer.status === filter);

  // Status badge component
  const StatusBadge = ({ status }: { status: string }) => {
    let bgColor = 'bg-green-100 text-green-800';
    let statusText = 'Active';

    if (status === 'upcoming') {
      bgColor = 'bg-blue-100 text-blue-800';
      statusText = 'Upcoming';
    } else if (status === 'expired') {
      bgColor = 'bg-gray-100 text-gray-800';
      statusText = 'Expired';
    }

    return (
      <span
        className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${bgColor}`}
      >
        {statusText}
      </span>
    );
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-green-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div
        className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4"
        role="alert"
      >
        <p>Error loading offers. Please try again.</p>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold">Offers & Promotions</h1>
        <button className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2">
          Create New Offer
        </button>
      </div>

      {/* Filter tabs */}
      <div className="flex space-x-1 mb-6 border-b">
        <button
          className={`px-4 py-2 font-medium ${
            filter === 'all'
              ? 'text-green-600 border-b-2 border-green-600'
              : 'text-gray-500'
          }`}
          onClick={() => setFilter('all')}
        >
          All
        </button>
        <button
          className={`px-4 py-2 font-medium ${
            filter === 'active'
              ? 'text-green-600 border-b-2 border-green-600'
              : 'text-gray-500'
          }`}
          onClick={() => setFilter('active')}
        >
          Active
        </button>
        <button
          className={`px-4 py-2 font-medium ${
            filter === 'upcoming'
              ? 'text-green-600 border-b-2 border-green-600'
              : 'text-gray-500'
          }`}
          onClick={() => setFilter('upcoming')}
        >
          Upcoming
        </button>
        <button
          className={`px-4 py-2 font-medium ${
            filter === 'expired'
              ? 'text-green-600 border-b-2 border-green-600'
              : 'text-gray-500'
          }`}
          onClick={() => setFilter('expired')}
        >
          Expired
        </button>
      </div>

      {/* Offers list */}
      <div className="space-y-6">
        {filteredOffers.length > 0 ? (
          filteredOffers.map(offer => (
            <div
              key={offer.id}
              className="bg-white rounded-lg shadow-sm overflow-hidden"
            >
              <div className="p-6">
                <div className="flex items-start justify-between">
                  <h3 className="text-xl font-medium">{offer.title}</h3>
                  <StatusBadge status={offer.status} />
                </div>

                <p className="text-gray-600 mt-2">{offer.description}</p>

                <div className="mt-4 flex flex-wrap gap-4">
                  <div>
                    <p className="text-sm text-gray-500">Discount</p>
                    <p className="font-medium">
                      {offer.discountType === 'percentage'
                        ? `${offer.discountValue}% off`
                        : `$${offer.discountValue} off`}
                    </p>
                  </div>

                  <div>
                    <p className="text-sm text-gray-500">Type</p>
                    <p className="font-medium capitalize">{offer.type}</p>
                  </div>

                  <div>
                    <p className="text-sm text-gray-500">Duration</p>
                    <p className="font-medium">
                      {new Date(offer.startDate).toLocaleDateString()} -{' '}
                      {new Date(offer.endDate).toLocaleDateString()}
                    </p>
                  </div>

                  <div>
                    <p className="text-sm text-gray-500">Carbon Savings</p>
                    <p className="font-medium text-green-600">
                      {offer.carbonSavings} kg CO₂e
                    </p>
                  </div>
                </div>

                {/* Products included */}
                <div className="mt-4">
                  <p className="text-sm text-gray-500 mb-2">
                    Products included:
                  </p>
                  <div className="flex flex-wrap gap-2">
                    {offer.products.map(product => (
                      <span
                        key={product.id}
                        className="px-3 py-1 bg-gray-100 text-gray-800 rounded-full text-sm"
                      >
                        {product.name}{' '}
                        {product.quantity > 1 ? `(x${product.quantity})` : ''}
                      </span>
                    ))}
                  </div>
                </div>

                <div className="mt-6 flex justify-end space-x-3">
                  <button className="px-3 py-1 border border-gray-300 rounded-md text-sm hover:bg-gray-50">
                    Edit
                  </button>
                  <button className="px-3 py-1 bg-gray-200 text-gray-800 rounded-md text-sm hover:bg-gray-300">
                    Duplicate
                  </button>
                  <button className="px-3 py-1 bg-green-600 text-white rounded-md text-sm hover:bg-green-700">
                    View Analytics
                  </button>
                </div>
              </div>
            </div>
          ))
        ) : (
          <div className="text-center py-12 bg-gray-50 rounded-lg">
            <svg
              className="mx-auto h-12 w-12 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900">
              No offers found
            </h3>
            <p className="mt-1 text-sm text-gray-500">
              {filter !== 'all'
                ? `You don't have any ${filter} offers.`
                : 'Create an offer to get started.'}
            </p>
            <div className="mt-4">
              <button
                type="button"
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
              >
                Create New Offer
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default OffersView;
