import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import { Product, AllowedProductTypes } from '../types/products';
import ProductsListSkeleton from '../components/ProductsListSkeleton';

const fetchProducts = async (): Promise<{
  products: Product[];
  total: number;
  total_pages: number;
}> => {
  const response = await fetch('/api/v1/products', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json'
    },
    credentials: 'include'
  });

  const data = await response.json();
  if (!data.success) {
    throw new Error(data.message || 'Failed to fetch products');
  }

  return data.data;
};

const fetchAllowedProductTypes = async (): Promise<AllowedProductTypes> => {
  const response = await fetch('/api/v1/products/allowed-types', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json'
    },
    credentials: 'include'
  });

  const data = await response.json();
  if (!data.success) {
    throw new Error(data.message || 'Failed to fetch allowed product types');
  }

  return data.data;
};

const ProductsListView: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const navigate = useNavigate();

  const { data: productsData, isLoading, error, refetch } = useQuery({
    queryKey: ['products'],
    queryFn: fetchProducts
  });

  const { data: allowedTypesData } = useQuery({
    queryKey: ['allowedProductTypes'],
    queryFn: fetchAllowedProductTypes
  });

  const products = productsData?.products || [];

  const filteredProducts = products.filter(product =>
    product.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleEditProduct = (productId: number) => {
    navigate(`${productId}`);
  };

  const handleCreateProduct = () => {
    navigate('create');
  };

  const handleViewProduct = (productId: number) => {
    const baseUrl = window.location.origin;
    const productUrl = `${baseUrl}/?p=${productId}`;
    window.open(productUrl, '_blank');
  };

  const StatusBadge = ({ status }: { status: string }) => {
    let bgColor = 'bg-green-100 text-green-800';
    let statusText = 'Published';

    if (status === 'draft') {
      bgColor = 'bg-yellow-100 text-yellow-800';
      statusText = 'Draft';
    } else if (status === 'private') {
      bgColor = 'bg-red-100 text-red-800';
      statusText = 'Private';
    }

    return (
      <span
        className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${bgColor}`}
      >
        {statusText}
      </span>
    );
  };

  const getProductTypeLabel = (product: Product) => {
    if (product.acf_fields?.company_name) {
      return 'Consulting Service';
    }
    if (product.acf_fields?.project_provider) {
      return 'Compensation Project';
    }
    return 'Product';
  };

  const canCreateProducts =
    allowedTypesData?.allowed_types &&
    allowedTypesData.allowed_types.length > 0;

  if (isLoading) {
    return (
      <div>
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-semibold">My Products</h1>
          <div className="h-10 bg-gray-200 rounded w-32 animate-pulse"></div>
        </div>

        <div className="mb-6">
          <div className="h-10 bg-gray-200 rounded w-full animate-pulse"></div>
        </div>

        <ProductsListSkeleton />
      </div>
    );
  }

  if (error) {
    return (
      <div
        className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4"
        role="alert"
      >
        <p>Error loading products. Please try again.</p>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold">Your Products</h1>
        {canCreateProducts && (
          <button
            onClick={handleCreateProduct}
            className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
          >
            Add New Product
          </button>
        )}
      </div>

      <div className="mb-6">
        <div className="relative">
          <input
            type="text"
            placeholder="Search products by name..."
            value={searchTerm}
            onChange={e => setSearchTerm(e.target.value)}
            className="w-full px-4 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
          />
          <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
            <svg
              className="w-5 h-5 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
              />
            </svg>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredProducts.length > 0 ? (
          filteredProducts.map(product => (
            <div
              key={product.id}
              className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden flex flex-col"
            >
              <div className="relative h-48">
                {product.images && product.images.length > 0 ? (
                  <img
                    src={product.images[0]}
                    alt={product.name}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-48 bg-gray-200 flex items-center justify-center">
                    <span className="text-gray-400 text-4xl">📦</span>
                  </div>
                )}
                <div className="absolute top-2 right-2">
                  <StatusBadge status={product.status} />
                </div>
                <div className="absolute bottom-2 left-2 bg-blue-600 text-white px-2 py-1 rounded-md text-xs font-medium">
                  {getProductTypeLabel(product)}
                </div>
              </div>

              <div className="p-4 flex flex-col h-full">
                <div className="flex-1">
                  <h3 className="text-lg font-medium mb-2 truncate">
                    {product.name}
                  </h3>

                  {product.short_description && (
                    <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                      <div
                        dangerouslySetInnerHTML={{
                          __html: product.short_description
                        }}
                      />
                    </p>
                  )}

                  <div className="flex justify-between items-center mb-3">
                    <p className="text-lg font-semibold text-green-600">
                      €{product.price.toFixed(2)}
                    </p>
                    {product.acf_fields?.location && (
                      <p className="text-sm text-gray-500">
                        📍 {product.acf_fields.location}
                      </p>
                    )}
                  </div>

                  {product.acf_fields?.available_tonnes && (
                    <p className="text-sm text-gray-600 mb-3">
                      Available: {product.acf_fields.available_tonnes} tonnes
                      CO₂
                    </p>
                  )}

                  {product.acf_fields?.company_name && (
                    <p className="text-sm text-gray-600 mb-3">
                      Company: {product.acf_fields.company_name}
                    </p>
                  )}
                </div>

                <div className="flex justify-end space-x-2 mt-4 pt-4 border-t border-gray-100">
                  <button
                    onClick={() => handleEditProduct(product.id)}
                    className="px-3 py-1 border border-gray-300 rounded-md text-sm hover:bg-gray-50"
                  >
                    Edit
                  </button>
                  <button
                    onClick={() => handleViewProduct(product.id)}
                    className="px-3 py-1 bg-green-600 text-white rounded-md text-sm hover:bg-green-700"
                  >
                    View
                  </button>
                </div>
              </div>
            </div>
          ))
        ) : (
          <div className="col-span-3 text-center py-12 bg-gray-50 rounded-lg">
            <svg
              className="mx-auto h-12 w-12 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
              />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900">
              No products found
            </h3>
            <p className="mt-1 text-sm text-gray-500">
              {canCreateProducts
                ? 'Get started by creating your first product.'
                : "You don't have permission to create products or no products match your search."}
            </p>
            {canCreateProducts && (
              <button
                onClick={handleCreateProduct}
                className="mt-3 px-4 py-2 bg-green-600 text-white rounded-md text-sm hover:bg-green-700"
              >
                Create Product
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default ProductsListView;
