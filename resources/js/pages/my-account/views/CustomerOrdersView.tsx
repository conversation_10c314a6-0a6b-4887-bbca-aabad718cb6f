import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Link } from 'react-router-dom';
import { CustomerOrder } from '../types/customerOrders';

// Mock API function - in a real app, this would call an actual API
const fetchCustomerOrders = async (): Promise<CustomerOrder[]> => {
  // Simulating API call delay
  await new Promise(resolve => setTimeout(resolve, 1000));

  // Mock data
  return [
    {
      id: 'co-001',
      customer: {
        id: 'cust-1',
        name: 'Acme Corporation',
        email: '<EMAIL>',
        type: 'business'
      },
      orders: [
        {
          id: 'ord-101',
          date: '2025-05-10T14:23:45Z',
          total: 2499.99,
          status: 'completed',
          items: 5
        },
        {
          id: 'ord-89',
          date: '2025-04-28T10:15:22Z',
          total: 1299.5,
          status: 'completed',
          items: 3
        }
      ],
      totalSpend: 3799.49,
      carbonOffset: 450.2,
      lastPurchase: '2025-05-10T14:23:45Z'
    },
    {
      id: 'co-002',
      customer: {
        id: 'cust-2',
        name: 'EcoFriendly Solutions',
        email: '<EMAIL>',
        type: 'business'
      },
      orders: [
        {
          id: 'ord-105',
          date: '2025-05-15T09:42:18Z',
          total: 5680.75,
          status: 'processing',
          items: 12
        }
      ],
      totalSpend: 5680.75,
      carbonOffset: 682.5,
      lastPurchase: '2025-05-15T09:42:18Z'
    },
    {
      id: 'co-003',
      customer: {
        id: 'cust-3',
        name: 'Green Planet Retail',
        email: '<EMAIL>',
        type: 'business'
      },
      orders: [
        {
          id: 'ord-98',
          date: '2025-05-05T16:37:29Z',
          total: 3450.25,
          status: 'completed',
          items: 8
        },
        {
          id: 'ord-67',
          date: '2025-03-17T11:08:53Z',
          total: 2100.0,
          status: 'completed',
          items: 5
        },
        {
          id: 'ord-52',
          date: '2025-02-28T14:22:10Z',
          total: 1875.5,
          status: 'completed',
          items: 4
        }
      ],
      totalSpend: 7425.75,
      carbonOffset: 890.3,
      lastPurchase: '2025-05-05T16:37:29Z'
    }
  ];
};

const CustomerOrdersView: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'name' | 'totalSpend' | 'lastPurchase'>(
    'lastPurchase'
  );
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  const { data: customerOrders = [], isLoading, error } = useQuery({
    queryKey: ['customerOrders'],
    queryFn: fetchCustomerOrders
  });

  // Filter customer orders based on search term
  const filteredCustomerOrders = customerOrders.filter(
    co =>
      co.customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      co.customer.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Sort customer orders
  const sortedCustomerOrders = [...filteredCustomerOrders].sort((a, b) => {
    if (sortBy === 'name') {
      return sortDirection === 'asc'
        ? a.customer.name.localeCompare(b.customer.name)
        : b.customer.name.localeCompare(a.customer.name);
    } else if (sortBy === 'totalSpend') {
      return sortDirection === 'asc'
        ? a.totalSpend - b.totalSpend
        : b.totalSpend - a.totalSpend;
    } else {
      // lastPurchase
      return sortDirection === 'asc'
        ? new Date(a.lastPurchase).getTime() -
            new Date(b.lastPurchase).getTime()
        : new Date(b.lastPurchase).getTime() -
            new Date(a.lastPurchase).getTime();
    }
  });

  // Handle sort change
  const handleSortChange = (column: typeof sortBy) => {
    if (sortBy === column) {
      // Toggle direction if column is the same
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      // Set new column and default to descending
      setSortBy(column);
      setSortDirection('desc');
    }
  };

  // Status badge component
  const OrderStatusBadge = ({ status }: { status: string }) => {
    let bgColor = 'bg-green-100 text-green-800';

    if (status === 'processing') {
      bgColor = 'bg-blue-100 text-blue-800';
    } else if (status === 'cancelled') {
      bgColor = 'bg-red-100 text-red-800';
    } else if (status === 'on-hold') {
      bgColor = 'bg-yellow-100 text-yellow-800';
    }

    return (
      <span
        className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${bgColor}`}
      >
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-green-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div
        className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4"
        role="alert"
      >
        <p>Error loading customer orders. Please try again.</p>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold">Customer Orders</h1>
      </div>

      {/* Search */}
      <div className="mb-6">
        <div className="relative">
          <input
            type="text"
            placeholder="Search customers by name or email..."
            value={searchTerm}
            onChange={e => setSearchTerm(e.target.value)}
            className="w-full px-4 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
          />
          <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
            <svg
              className="w-5 h-5 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
              />
            </svg>
          </div>
        </div>
      </div>

      {/* Customer orders list */}
      {sortedCustomerOrders.length > 0 ? (
        <div className="space-y-8">
          {sortedCustomerOrders.map(customerOrder => (
            <div
              key={customerOrder.id}
              className="bg-white shadow-sm rounded-lg overflow-hidden"
            >
              {/* Customer header */}
              <div className="p-6 border-b border-gray-200">
                <div className="flex justify-between items-start mb-2">
                  <div>
                    <h3 className="text-xl font-semibold">
                      {customerOrder.customer.name}
                    </h3>
                    <p className="text-gray-600">
                      {customerOrder.customer.email}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-gray-500">Total Spend</p>
                    <p className="font-semibold text-lg">
                      $
                      {customerOrder.totalSpend.toLocaleString('en-US', {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2
                      })}
                    </p>
                  </div>
                </div>

                <div className="flex flex-wrap gap-8 mt-4">
                  <div>
                    <p className="text-sm text-gray-500">Orders</p>
                    <p className="font-medium">{customerOrder.orders.length}</p>
                  </div>

                  <div>
                    <p className="text-sm text-gray-500">Last Purchase</p>
                    <p className="font-medium">
                      {formatDate(customerOrder.lastPurchase)}
                    </p>
                  </div>

                  <div>
                    <p className="text-sm text-gray-500">Carbon Offset</p>
                    <p className="font-medium text-green-600">
                      {customerOrder.carbonOffset.toFixed(1)} kg CO₂e
                    </p>
                  </div>
                </div>

                <div className="mt-4">
                  <Link
                    to={`/my-account/customers/${customerOrder.customer.id}`}
                    className="text-green-600 hover:underline text-sm font-medium"
                  >
                    View Customer Details
                  </Link>
                </div>
              </div>

              {/* Orders table */}
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        Order ID
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        Date
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        Items
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        Total
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        Status
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {customerOrder.orders.map(order => (
                      <tr key={order.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">
                            {order.id}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            {formatDate(order.date)}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            {order.items}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">
                            $
                            {order.total.toLocaleString('en-US', {
                              minimumFractionDigits: 2,
                              maximumFractionDigits: 2
                            })}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <OrderStatusBadge status={order.status} />
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <Link
                            to={`/my-account/orders/${order.id}`}
                            className="text-green-600 hover:text-green-800"
                          >
                            View
                          </Link>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12 bg-gray-50 rounded-lg">
          <svg
            className="mx-auto h-12 w-12 text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
            />
          </svg>
          <h3 className="mt-2 text-sm font-medium text-gray-900">
            No customers found
          </h3>
          <p className="mt-1 text-sm text-gray-500">
            {searchTerm
              ? 'Try adjusting your search criteria.'
              : 'No customer orders exist yet.'}
          </p>
        </div>
      )}
    </div>
  );
};

export default CustomerOrdersView;
