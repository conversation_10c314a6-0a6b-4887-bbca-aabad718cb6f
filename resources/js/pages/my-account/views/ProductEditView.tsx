import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { useParams, useNavigate } from 'react-router-dom';
import ProductForm from '../components/ProductForm';
import ProductFormSkeleton from '../components/ProductFormSkeleton';
import { Product, AllowedProductTypes } from '../types/products';
import { ChevronLeft } from 'lucide-react';

const fetchProduct = async (id: number): Promise<Product> => {
  const response = await fetch(`/api/v1/products/${id}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json'
    },
    credentials: 'include'
  });

  const data = await response.json();
  if (!data.success) {
    throw new Error(data.message || 'Failed to fetch product');
  }

  return data.data;
};

const fetchAllowedProductTypes = async (): Promise<AllowedProductTypes> => {
  const response = await fetch('/api/v1/products/allowed-types', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json'
    },
    credentials: 'include'
  });

  const data = await response.json();
  if (!data.success) {
    throw new Error(data.message || 'Failed to fetch allowed product types');
  }

  return data.data;
};

const ProductEditView: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  const productId = id ? parseInt(id, 10) : null;

  const { data: allowedTypesData } = useQuery({
    queryKey: ['allowedProductTypes'],
    queryFn: fetchAllowedProductTypes
  });

  const {
    data: product,
    isLoading: isLoadingProduct,
    error: productError
  } = useQuery({
    queryKey: ['product', productId],
    queryFn: () => fetchProduct(productId!),
    enabled: !!productId
  });

  const handleSuccess = () => {
    navigate('/products');
  };

  const handleCancel = () => {
    navigate('/products');
  };

  if (!productId) {
    return (
      <div
        className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4"
        role="alert"
      >
        <p>Invalid product ID.</p>
      </div>
    );
  }

  return (
    <div>
      <div className="flex items-center gap-3 mb-4">
        <button
          onClick={handleCancel}
          className="p-2 border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
        >
          <ChevronLeft className="w-4 h-4" />
        </button>
        <h1 className="text-xl font-medium">
          {isLoadingProduct ? 'Loading Product...' : `Edit Product`}
        </h1>
      </div>

      {productError ? (
        <div
          className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4"
          role="alert"
        >
          <p>Error loading product. Please try again.</p>
        </div>
      ) : isLoadingProduct ? (
        <ProductFormSkeleton />
      ) : product ? (
        <ProductForm
          allowedTypes={allowedTypesData?.allowed_types || []}
          product={product}
          onSuccess={handleSuccess}
          onCancel={handleCancel}
        />
      ) : null}
    </div>
  );
};

export default ProductEditView;
