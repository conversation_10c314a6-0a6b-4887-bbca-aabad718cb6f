import React from 'react';
import { use<PERSON>ara<PERSON>, <PERSON> } from 'react-router-dom';
import { useSingleOrder } from '../hooks/useSingleOrder';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

// Import order components
import OrderSummary from '../components/order/OrderSummary';
import OrderItems from '../components/order/OrderItems';
import OrderAddresses from '../components/order/OrderAddresses';
import CarbonFootprintDetails from '../components/order/CarbonFootprintDetails';
import OrderNotes from '../components/order/OrderNotes';

// Create a new queryClient
const queryClient = new QueryClient();

// Wrap component with QueryClientProvider
const SingleOrderViewWithQueryProvider: React.FC = () => {
  return (
    <QueryClientProvider client={queryClient}>
      <SingleOrderView />
    </QueryClientProvider>
  );
};

const SingleOrderView: React.FC = () => {
  const { orderId } = useParams<{ orderId: string }>();
  const { data, isLoading, error, isError } = useSingleOrder(orderId);

  const order = data?.data?.order;

  // Handle loading state
  if (isLoading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl font-bold">Order Details</h1>
          <Link
            to="/my-account/orders"
            className="text-blue-600 hover:text-blue-800 flex items-center"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4 mr-1"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 19l-7-7 7-7"
              />
            </svg>
            Back to orders
          </Link>
        </div>
        <div className="animate-pulse space-y-6">
          <div className="h-40 bg-gray-200 rounded-md"></div>
          <div className="h-64 bg-gray-200 rounded-md"></div>
          <div className="h-40 bg-gray-200 rounded-md"></div>
        </div>
      </div>
    );
  }

  // Handle error state
  if (isError || !order) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl font-bold">Order Details</h1>
          <Link
            to="/my-account/orders"
            className="text-blue-600 hover:text-blue-800 flex items-center"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4 mr-1"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 19l-7-7 7-7"
              />
            </svg>
            Back to orders
          </Link>
        </div>
        <div className="bg-red-50 border border-red-200 rounded-md p-4 text-red-700">
          <p className="font-medium">Error loading order</p>
          <p className="mt-1">
            {error?.message ||
              'Order not found or you do not have permission to view it.'}
          </p>
          <button
            onClick={() =>
              queryClient.invalidateQueries({ queryKey: ['order', orderId] })
            }
            className="mt-3 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold">Order #{order.order_number}</h1>
        <Link
          to="/my-account/orders"
          className="text-blue-600 hover:text-blue-800 flex items-center"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-4 w-4 mr-1"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M15 19l-7-7 7-7"
            />
          </svg>
          Back to orders
        </Link>
      </div>

      <div className="space-y-6">
        {/* Order Summary */}
        <OrderSummary
          status={order.status}
          status_name={order.status_name}
          order_number={order.order_number}
          date={order.formatted_date}
          payment_method={order.payment_method_title}
          totals={order.totals}
        />

        {/* Order Items */}
        <OrderItems items={order.items} />

        {/* Order Addresses */}
        <OrderAddresses billing={order.billing} shipping={order.shipping} />

        {/* Carbon Footprint */}
        <CarbonFootprintDetails carbonFootprint={order.carbon_footprint} />

        {/* Order Notes */}
        <OrderNotes notes={order.notes} customerNote={order.customer_note} />

        {/* Downloads (if any) */}
        {order.downloads && order.downloads.length > 0 && (
          <div className="rounded-md border">
            <h3 className="text-lg font-medium p-4 bg-gray-50 border-b">
              Downloads
            </h3>
            <div className="p-4">
              <ul className="divide-y divide-gray-200">
                {order.downloads.map((download, index) => (
                  <li key={index} className="py-3 flex justify-between">
                    <span>{download.product_name}</span>
                    <a
                      href={download.download_url}
                      className="text-blue-600 hover:text-blue-800"
                      download
                    >
                      Download
                    </a>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="flex justify-end space-x-3 pt-4">
          {order.status === 'processing' && (
            <button className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
              Track Order
            </button>
          )}
          <Link
            to={`/my-account/orders`}
            className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
          >
            Back to Orders
          </Link>
        </div>
      </div>
    </div>
  );
};

export default SingleOrderViewWithQueryProvider;
