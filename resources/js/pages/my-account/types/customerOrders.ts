
export interface Customer {
  id: string;
  name: string;
  email: string;
  type: 'business' | 'individual';
  phone?: string;
  company?: string;
  address?: {
    line1: string;
    line2?: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  };
  createdAt?: string;
  lastActive?: string;
}

export interface CustomerOrderItem {
  id: string;
  date: string;
  total: number;
  status: 'completed' | 'processing' | 'on-hold' | 'cancelled';
  items: number;
}

export interface CustomerOrder {
  id: string;
  customer: Customer;
  orders: CustomerOrderItem[];
  totalSpend: number;
  carbonOffset: number;
  lastPurchase: string;
}

export interface CustomerAnalytics {
  customerId: string;
  lifetimeValue: number;
  averageOrderValue: number;
  totalOrders: number;
  firstPurchaseDate: string;
  lastPurchaseDate: string;
  purchaseFrequency: number; // average days between orders
  topProducts: {
    productId: string;
    productName: string;
    quantity: number;
    revenue: number;
  }[];
  carbonOffsetContribution: number;
}
