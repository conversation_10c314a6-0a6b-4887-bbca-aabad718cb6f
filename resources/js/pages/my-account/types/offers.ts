
export interface OfferProduct {
  id: string;
  name: string;
  quantity: number;
}

export interface Offer {
  id: string;
  title: string;
  description: string;
  type: 'discount' | 'bundle' | 'bogo' | 'flash_sale';
  discountType: 'percentage' | 'fixed';
  discountValue: number;
  startDate: string;
  endDate: string;
  status: 'active' | 'upcoming' | 'expired';
  carbonSavings: number;
  products: OfferProduct[];
  minimumPurchase?: number;
  usageLimit?: number;
  usageCount?: number;
  couponCode?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface OfferFilter {
  status?: 'active' | 'upcoming' | 'expired' | 'all';
  search?: string;
  type?: string;
  startDateFrom?: string;
  startDateTo?: string;
  endDateFrom?: string;
  endDateTo?: string;
  sortBy?: 'title' | 'startDate' | 'endDate' | 'createdAt' | 'discountValue';
  sortDirection?: 'asc' | 'desc';
}

export interface OfferAnalytics {
  offerId: string;
  redemptions: number;
  revenue: number;
  carbonSaved: number;
  conversionRate: number;
  averageOrderValue: number;
  topProducts: {
    productId: string;
    productName: string;
    quantity: number;
    revenue: number;
  }[];
}
