// Types for the My Account module

export interface Role {
  label: string;
  color: string;
  role: string;
}

export interface OrganizationUser {
  id: number;
  name: string;
  email: string;
  avatar: string | false;
}

export interface OffsetProcess {
  id: number;
  name: string;
  step: string;
  user_id: number;
}

export interface Organization {
  id: number;
  name: string;
  users: OrganizationUser[];
  creator_id: string;
  offset_processes?: OffsetProcess[];
}

export interface User {
  id?: number;
  name: string;
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  company: string;
  avatar?: string;
  roles?: Role[];
  organization?: Organization | null;
}

export interface Order {
  id: string;
  customer: string;
  date: string;
  total: number;
  status: 'delivered' | 'pending' | 'action_needed';
}

export interface Message {
  id: string;
  sender: string;
  message: string;
  time: string;
  read: boolean;
}

export interface CarbonGoal {
  percentage: number;
  message: string;
}

export interface Revenue {
  amount: number;
  percentageChange: number;
}

export interface Customers {
  count: number;
  percentageChange: number;
}

export interface AccountPageProps {
  user: User;
  carbonGoal: CarbonGoal;
  revenue: Revenue;
  customers: Customers;
  orders: Order[];
  messages: Message[];
}
