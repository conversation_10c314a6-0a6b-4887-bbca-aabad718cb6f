export interface Product {
  id: number;
  name: string;
  description: string;
  short_description: string;
  price: number;
  categories: string[];
  images: string[];
  status: string;
  acf_fields: AcfFields;
}

export interface AcfFields {
  location?: string;
  country?: string;
  local_vat?: number;
  can_ask_for_offer?: boolean;
  contact_persons?: any[];
  form?: any;
  for?: string[];
  users?: any[];

  // Compensation project specific fields
  project_provider?: string;
  project_id?: string;
  available_tonnes?: number;
  active?: boolean;
  impact_description?: string;
  webpage?: string;
  address?: string;
  contact?: string;
  certified?: string;
  certificate?: string;

  // Consulting service specific fields
  service_id?: number;
  consultant_id?: number;
  company_name?: string;
  time_estimate?: string;
  standard?: string[];
}

export interface ProductFormData {
  product_type: string;
  name: string;
  price: number;
  short_description?: string;
  description?: string;
  location?: string;
  country?: string;
  local_vat?: number;
  can_ask_for_offer?: boolean;

  // Image handling
  image?: File;
  remove_image?: boolean;

  // Compensation project specific
  project_provider?: string;
  project_id?: string;
  available_tonnes?: number;
  active?: boolean;
  impact_description?: string;
  webpage?: string;
  address?: string;
  contact?: string;
  certified?: string;
  certificate?: string;

  // Consulting service specific
  service_id?: number;
  consultant_id?: number;
  company_name?: string;
  time_estimate?: string;
  standard?: string[];
  consulting_subcategories?: number[];
}

export interface FormOptions {
  countries: string[];
  certifications: Array<{ slug: string; name: string }>;
  standards: string[];
  consulting_subcategories: Array<{ id: number; slug: string; name: string }>;
}

export interface AllowedProductTypes {
  allowed_types: string[];
  user_roles: string[];
}

export interface ProductFilter {
  search?: string;
  status?: string;
  category?: string;
  minPrice?: number;
  maxPrice?: number;
  sortBy?: 'name' | 'price' | 'stock' | 'createdAt';
  sortDirection?: 'asc' | 'desc';
}

export interface ProductsResponse {
  products: Product[];
  total: number;
  page: number;
  perPage: number;
  totalPages: number;
}
