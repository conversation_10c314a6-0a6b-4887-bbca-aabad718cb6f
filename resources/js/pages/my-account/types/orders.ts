import { Column } from '@tanstack/react-table';

export interface Order {
  id: number;
  order_number: string;
  status: string;
  status_name: string;
  date_created: string;
  formatted_date: string;
  total: string;
  formatted_total: string;
  payment_method: string;
  items_count: number;
}

export interface Pagination {
  total: number;
  per_page: number;
  current_page: number;
  max_pages: number;
}

export interface OrdersResponse {
  success: boolean;
  data: {
    orders: Order[];
    pagination: Pagination;
  };
}

export interface OrderItem {
  id: number;
  variation_id: number | null;
  name: string;
  quantity: number;
  subtotal: number;
  total: number;
  tax: number;
  formatted_subtotal: string;
  formatted_total: string;
  formatted_tax: string;
  image?: string;
  permalink?: string;
}

export interface OrderNote {
  id: number;
  content: string;
  date: string;
  formatted_date: string;
}

export interface OrderTotal {
  key: string;
  label: string;
  value: string;
}

export interface Address {
  first_name: string;
  last_name: string;
  company: string;
  address_1: string;
  address_2: string;
  city: string;
  state: string;
  postcode: string;
  country: string;
  email: string;
  phone: string;
}

export interface CarbonFootprint {
  amount: number;
  units: string;
  offset: boolean;
  offset_amount: number;
}

export interface DetailedOrder extends Order {
  date_modified: string | null;
  date_completed: string | null;
  date_paid: string | null;
  customer_note: string;
  payment_method_title: string;
  transaction_id: string;
  subtotal: number;
  tax_total: number;
  shipping_total: number;
  discount_total: number;
  formatted_subtotal: string;
  formatted_tax_total: string;
  formatted_shipping_total: string;
  formatted_discount_total: string;
  billing: Address;
  shipping: Address;
  items: OrderItem[];
  notes: OrderNote[];
  totals: OrderTotal[];
  downloads: any[];
  carbon_footprint: CarbonFootprint;
}

export interface SingleOrderResponse {
  success: boolean;
  data: {
    order: DetailedOrder;
  };
}
