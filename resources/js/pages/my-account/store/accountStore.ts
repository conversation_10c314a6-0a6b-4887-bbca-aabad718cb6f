// Store for My Account data using Zustand
import { create } from 'zustand';
import {
  User,
  Order,
  Message,
  CarbonGoal,
  Revenue,
  Customers
} from '../types/index';

interface AccountStore {
  user: User;
  carbonGoal: CarbonGoal;
  revenue: Revenue;
  customers: Customers;
  orders: Order[];
  messages: Message[];
  unreadMessagesCount: number;

  // Actions
  updateUser: (user: Partial<User>) => void;
  markMessageAsRead: (id: string) => void;
  updateCarbonGoal: (goal: Partial<CarbonGoal>) => void;
  setInitialData: (data: { user?: User }) => void;
}

export const useAccountStore = create<AccountStore>(set => ({
  // Default data
  user: {
    name: '',
    first_name: '',
    last_name: '',
    email: '',
    phone: '',
    company: '',
    roles: []
  },

  carbonGoal: {
    percentage: 100,
    message: 'Your business is 100% carbon neutral'
  },

  revenue: {
    amount: 45231.89,
    percentageChange: 20.1
  },

  customers: {
    count: 2350,
    percentageChange: 180.1
  },

  orders: [
    {
      id: 'ORD001',
      customer: '<PERSON>',
      date: '7.5.2024',
      total: 4000,
      status: 'delivered'
    },
    {
      id: 'ORD002',
      customer: '<PERSON> <PERSON>e',
      date: '7.5.2024',
      total: 4000,
      status: 'pending'
    },
    {
      id: 'ORD003',
      customer: 'Bob Smith',
      date: '7.5.2024',
      total: 4000,
      status: 'action_needed'
    }
  ],

  messages: [
    {
      id: '1',
      sender: 'Lorem Hudson',
      message: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
      time: '2:00 PM',
      read: false
    },
    {
      id: '2',
      sender: 'Juliette Kent',
      message: 'consectetur adipiscing elit, sed do eiusmod tempor incididunt.',
      time: '2:00 PM',
      read: false
    },
    {
      id: '3',
      sender: 'Peter Wade',
      message: 'ut labore et dolore magna aliqua. Ut enim ad minim veniam.',
      time: '2:00 PM',
      read: false
    }
  ],

  // Computed property
  get unreadMessagesCount() {
    return this.messages.filter(msg => !msg.read).length;
  },

  // Actions
  updateUser: userData =>
    set(state => ({ user: { ...state.user, ...userData } })),

  markMessageAsRead: id =>
    set(state => ({
      messages: state.messages.map(msg =>
        msg.id === id ? { ...msg, read: true } : msg
      )
    })),

  updateCarbonGoal: goalData =>
    set(state => ({ carbonGoal: { ...state.carbonGoal, ...goalData } })),

  setInitialData: data =>
    set(state => ({
      ...state,
      ...(data.user && { user: { ...state.user, ...data.user } })
    }))
}));
