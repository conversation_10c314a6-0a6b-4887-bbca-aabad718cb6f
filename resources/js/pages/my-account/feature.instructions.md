


# My Account Page - Feature Completion Checklist

## { } Dashboard View
- { } New Messages (Fetch 5 latest messages from Rocket.Chat API)
- { } Order Statuses (Fetch 5 latest WooCommerce orders via PHP API)

## { } Orders Page
- {x} Orders Table (List all user orders)
- {x} Order Detail View (Order summary, items, address)
- { } Embedded Rocket.Chat for each order

## { } Customer Orders (Visible only to merchants)
- { } Orders Table (List customer orders)
- { } Order Detail View (Order summary, items, address)
- { } Embedded Rocket.Chat for customer communication

## { } Offers
- { } Offer View for Merchant (Receive offers from customers)
- { } Rocket.Chat Integration (Chat with customer)
- { } Counter Offer Functionality
- { } Offer View for Customer (Accept or decline offers)

## { } Account View
- { } Edit Account Information
- { } Change Password

## { } Organization View
- { } Edit Company Information
- { } Add Users via Email

## { } Products View (Merchants only)
- { } List Products
- { } Create New Products