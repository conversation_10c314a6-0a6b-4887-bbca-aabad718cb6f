import { useQuery } from '@tanstack/react-query';
import { SingleOrderResponse } from '../types/orders';

async function fetchOrder(orderId: number | string): Promise<SingleOrderResponse> {
  const response = await fetch(`/api/v1/orders/${orderId}`);
  
  if (!response.ok) {
    throw new Error('Failed to fetch order');
  }
  
  return response.json();
}

export function useSingleOrder(orderId: number | string | undefined) {
  return useQuery<SingleOrderResponse, Error>({
    queryKey: ['order', orderId],
    queryFn: () => fetchOrder(orderId as string),
    enabled: !!orderId,
    staleTime: 1000 * 60 * 5, // 5 minutes
    retry: 1,
  });
}
