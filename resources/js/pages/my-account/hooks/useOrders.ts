import { keepPreviousData, useQuery } from '@tanstack/react-query';
import { OrdersResponse } from '../types/orders';

async function fetchOrders(page = 1, perPage = 25): Promise<OrdersResponse> {
  const response = await fetch(`/api/v1/orders?page=${page}&per_page=${perPage}`);
  
  if (!response.ok) {
    throw new Error('Failed to fetch orders');
  }
  
  return response.json();
}

export function useOrders(page = 1, perPage = 25) {
  return useQuery<OrdersResponse, Error>({
    queryKey: ['orders', page, perPage],
    queryFn: () => fetchOrders(page, perPage),
    placeholderData: keepPreviousData,
    staleTime: 1000 * 60 * 5, // 5 minutes
    retry: 1,
  });
}
