import React from 'react';
import LeftNavigation from '../components/LeftNavigation';
import { User } from '../types';

interface AccountLayoutProps {
  user: User;
  children: React.ReactNode;
}

const AccountLayout: React.FC<AccountLayoutProps> = ({ user, children }) => {
  return (
    <div className="flex min-h-screen px-6">
      {/* Left sidebar */}
      <div className="w-64 border-r border-gray-200">
        <LeftNavigation user={user} />
      </div>

      {/* Main content */}
      <div className="flex-1 p-6">{children}</div>
    </div>
  );
};

export default AccountLayout;
