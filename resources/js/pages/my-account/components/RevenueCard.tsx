import React from 'react';
import { cn } from '../../../lib/utils';
import { Revenue } from '../types';

interface RevenueCardProps {
  revenue: Revenue;
  className?: string;
}

const RevenueCard: React.FC<RevenueCardProps> = ({ revenue, className }) => {
  const isPositive = revenue.percentageChange >= 0;

  return (
    <div
      className={cn(
        'bg-white p-6 rounded-md border border-gray-200',
        className
      )}
    >
      <div className="flex items-center mb-3">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-5 w-5 mr-2 text-gray-700"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
          />
        </svg>
        <h3 className="uppercase text-gray-800 font-bold">Total Revenue</h3>
      </div>

      <div className="flex flex-col">
        <span className="text-3xl font-semibold mb-1">
          ${' '}
          {revenue.amount.toLocaleString('en-US', { minimumFractionDigits: 2 })}
        </span>
        <span
          className={cn(
            'text-sm',
            isPositive ? 'text-green-600' : 'text-red-600'
          )}
        >
          {isPositive ? '+' : ''}
          {revenue.percentageChange}% from last month
        </span>
      </div>
    </div>
  );
};

export default RevenueCard;
