import React from 'react';
import { cn } from '../../../lib/utils';
import { CarbonGoal } from '../types';

interface CarbonGoalCardProps {
  carbonGoal: CarbonGoal;
  className?: string;
}

const CarbonGoalCard: React.FC<CarbonGoalCardProps> = ({
  carbonGoal,
  className
}) => {
  return (
    <div
      className={cn(
        'bg-white p-6 rounded-md border border-gray-200',
        className
      )}
    >
      <h3 className="uppercase text-gray-800 font-bold mb-4">Carbon Goal</h3>
      <div className="flex items-center">
        <div className="relative w-24 h-24 mr-6">
          <svg className="w-full h-full" viewBox="0 0 36 36">
            <path
              d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
              fill="none"
              stroke="#E9ECEF"
              strokeWidth="3"
            />
            <path
              d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
              fill="none"
              stroke="#8AD48A"
              strokeWidth="3"
              strokeDasharray={`${carbonGoal.percentage}, 100`}
            />
            <text
              x="18"
              y="20.35"
              className="fill-current text-gray-800 text-lg font-semibold"
              textAnchor="middle"
            >
              {carbonGoal.percentage}%
            </text>
          </svg>
        </div>
        <div>
          <p className="text-gray-600">{carbonGoal.message}</p>
        </div>
      </div>
    </div>
  );
};

export default CarbonGoalCard;
