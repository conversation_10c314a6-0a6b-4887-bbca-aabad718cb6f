import React from 'react';
import { cn } from '../../../../lib/utils';
import { CarbonFootprint } from '../../types/orders';

interface CarbonFootprintDetailsProps {
  carbonFootprint: CarbonFootprint;
  className?: string;
}

const CarbonFootprintDetails: React.FC<CarbonFootprintDetailsProps> = ({
  carbonFootprint,
  className
}) => {
  const { amount, units, offset, offset_amount } = carbonFootprint;

  // If there's no carbon footprint data, don't show the component
  if (!amount && !offset_amount) {
    return null;
  }

  return (
    <div className={cn('rounded-md border', className)}>
      <h3 className="text-lg font-medium p-4 bg-gray-50 border-b">
        Carbon Footprint
      </h3>
      <div className="p-4">
        <div className="flex items-center gap-4 mb-4">
          <div className="p-3 bg-green-100 text-green-700 rounded-full">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>
          <div>
            <p className="text-xl font-medium">
              {amount} {units}
            </p>
            <p className="text-gray-500">Carbon footprint for this order</p>
          </div>
        </div>

        {offset && (
          <div className="mt-4 p-4 bg-green-50 rounded-md">
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <svg
                  className="h-5 w-5 text-green-400"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-green-800">
                  Carbon offset purchased
                </h3>
                <div className="mt-2 text-sm text-green-700">
                  <p>
                    This order's carbon footprint has been offset with{' '}
                    {offset_amount} {units} of carbon credits.
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CarbonFootprintDetails;
