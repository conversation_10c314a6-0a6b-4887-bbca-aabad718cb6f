import React from 'react';
import { cn } from '../../../../lib/utils';
import { OrderItem } from '../../types/orders';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '../../../../components/ui/table';

interface OrderItemsProps {
  items: OrderItem[];
  className?: string;
}

const OrderItems: React.FC<OrderItemsProps> = ({ items, className }) => {
  return (
    <div className={cn('rounded-md border', className)}>
      <h3 className="text-lg font-medium p-4 bg-gray-50 border-b">
        Order Items
      </h3>
      <div className="p-4">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[80px]"></TableHead>
              <TableHead>Product</TableHead>
              <TableHead>Quantity</TableHead>
              <TableHead>Price</TableHead>
              <TableHead>Total</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {items.map((item, index) => (
              <TableRow key={index}>
                <TableCell>
                  {item.image ? (
                    <img
                      src={item.image}
                      alt={item.name}
                      className="w-16 h-16 object-cover rounded"
                    />
                  ) : (
                    <div className="w-16 h-16 bg-gray-100 rounded flex items-center justify-center">
                      <span className="text-gray-400">No image</span>
                    </div>
                  )}
                </TableCell>
                <TableCell className="font-medium">
                  {item.permalink ? (
                    <a
                      href={item.permalink}
                      className="text-blue-600 hover:underline"
                    >
                      {item.name}
                    </a>
                  ) : (
                    item.name
                  )}
                </TableCell>
                <TableCell>{item.quantity}</TableCell>
                <TableCell>
                  <div
                    dangerouslySetInnerHTML={{
                      __html: item.formatted_subtotal
                    }}
                  />
                </TableCell>
                <TableCell>
                  <div
                    dangerouslySetInnerHTML={{ __html: item.formatted_total }}
                  />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
};

export default OrderItems;
