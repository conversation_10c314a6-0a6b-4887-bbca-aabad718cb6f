import React from 'react';
import { cn } from '../../../../lib/utils';
import { OrderNote } from '../../types/orders';

interface OrderNotesProps {
  notes: OrderNote[];
  customerNote: string;
  className?: string;
}

const OrderNotes: React.FC<OrderNotesProps> = ({
  notes,
  customerNote,
  className
}) => {
  // If there are no notes and no customer note, don't show the component
  if (notes.length === 0 && !customerNote) {
    return null;
  }

  return (
    <div className={cn('rounded-md border', className)}>
      <h3 className="text-lg font-medium p-4 bg-gray-50 border-b">
        Order Notes
      </h3>
      <div className="p-4">
        {customerNote && (
          <div className="mb-4 p-3 bg-yellow-50 border border-yellow-100 rounded-md">
            <h4 className="font-medium text-yellow-800 mb-1">Customer Note</h4>
            <p className="text-gray-700">{customerNote}</p>
          </div>
        )}

        {notes.length > 0 ? (
          <div className="space-y-3">
            {notes.map((note, index) => (
              <div key={index} className="bg-gray-50 p-3 rounded-md">
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <p className="text-gray-700">{note.content}</p>
                  </div>
                  <span className="text-xs text-gray-500 whitespace-nowrap ml-2">
                    {note.formatted_date}
                  </span>
                </div>
              </div>
            ))}
          </div>
        ) : !customerNote ? (
          <p className="text-gray-500 italic">No notes for this order.</p>
        ) : null}
      </div>
    </div>
  );
};

export default OrderNotes;
