import React from 'react';
import { cn } from '../../../../lib/utils';
import { OrderTotal } from '../../types/orders';

interface OrderSummaryProps {
  status: string;
  status_name: string;
  order_number: string;
  date: string;
  payment_method: string;
  totals: OrderTotal[];
  className?: string;
}

const OrderSummary: React.FC<OrderSummaryProps> = ({
  status,
  status_name,
  order_number,
  date,
  payment_method,
  totals,
  className
}) => {
  // Helper function to get status badge color
  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'processing':
        return 'bg-blue-100 text-blue-800';
      case 'on-hold':
        return 'bg-yellow-100 text-yellow-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      case 'refunded':
        return 'bg-purple-100 text-purple-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      case 'pending':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className={cn('rounded-md border', className)}>
      <h3 className="text-lg font-medium p-4 bg-gray-50 border-b">
        Order Summary
      </h3>
      <div className="p-4 space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <p className="text-gray-500 text-sm">Order Number</p>
            <p className="font-medium">#{order_number}</p>
          </div>
          <div>
            <p className="text-gray-500 text-sm">Date</p>
            <p>{date}</p>
          </div>
          <div>
            <p className="text-gray-500 text-sm">Status</p>
            <p>
              <span
                className={cn(
                  'inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium',
                  getStatusBadgeClass(status)
                )}
              >
                {status_name}
              </span>
            </p>
          </div>
          <div>
            <p className="text-gray-500 text-sm">Payment Method</p>
            <p>{payment_method}</p>
          </div>
        </div>

        <div className="mt-6 pt-4 border-t">
          <h4 className="font-medium mb-2">Order Totals</h4>
          <div className="space-y-2">
            {totals.map((total, index) => (
              <div
                key={index}
                className={cn(
                  'flex justify-between py-1',
                  total.key === 'order_total' ? 'font-bold' : ''
                )}
              >
                <span dangerouslySetInnerHTML={{ __html: total.label }} />
                <span dangerouslySetInnerHTML={{ __html: total.value }} />
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrderSummary;
