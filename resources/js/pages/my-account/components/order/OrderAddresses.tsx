import React from 'react';
import { cn } from '../../../../lib/utils';
import { Address } from '../../types/orders';

interface OrderAddressesProps {
  billing: Address;
  shipping: Address;
  className?: string;
}

const OrderAddresses: React.FC<OrderAddressesProps> = ({
  billing,
  shipping,
  className
}) => {
  // Helper to format address
  const formatAddress = (address: Address) => {
    const parts = [
      address.first_name && address.last_name
        ? `${address.first_name} ${address.last_name}`
        : '',
      address.company || '',
      address.address_1 || '',
      address.address_2 || '',
      address.city
        ? `${address.city}, ${address.state} ${address.postcode}`
        : '',
      address.country || '',
      address.phone || '',
      address.email || ''
    ].filter(Boolean);

    return parts;
  };

  const billingFormatted = formatAddress(billing);
  const shippingFormatted = formatAddress(shipping);

  // Check if shipping is same as billing
  const isShippingSameAsBilling =
    shipping.first_name === billing.first_name &&
    shipping.last_name === billing.last_name &&
    shipping.address_1 === billing.address_1 &&
    shipping.city === billing.city &&
    shipping.postcode === billing.postcode;

  return (
    <div className={cn('rounded-md border', className)}>
      <h3 className="text-lg font-medium p-4 bg-gray-50 border-b">Addresses</h3>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 p-4">
        <div>
          <h4 className="font-medium mb-2">Billing Address</h4>
          <div className="text-gray-600 space-y-1">
            {billingFormatted.map((line, index) => (
              <p key={index}>{line}</p>
            ))}
          </div>
        </div>

        <div>
          <h4 className="font-medium mb-2">Shipping Address</h4>
          {isShippingSameAsBilling ? (
            <p className="text-gray-600 italic">Same as billing address</p>
          ) : (
            <div className="text-gray-600 space-y-1">
              {shippingFormatted.map((line, index) => (
                <p key={index}>{line}</p>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default OrderAddresses;
