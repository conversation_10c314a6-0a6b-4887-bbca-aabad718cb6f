import React from 'react';
import { cn } from '../../../lib/utils';
import { Customers } from '../types';

interface CustomersCardProps {
  customers: Customers;
  className?: string;
}

const CustomersCard: React.FC<CustomersCardProps> = ({
  customers,
  className
}) => {
  const isPositive = customers.percentageChange >= 0;

  return (
    <div
      className={cn(
        'bg-white p-6 rounded-md border border-gray-200',
        className
      )}
    >
      <div className="flex items-center mb-3">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-5 w-5 mr-2 text-gray-700"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
          />
        </svg>
        <h3 className="uppercase text-gray-800 font-bold">Customers</h3>
      </div>

      <div className="flex flex-col">
        <span className="text-3xl font-semibold mb-1">+{customers.count}</span>
        <span
          className={cn(
            'text-sm',
            isPositive ? 'text-green-600' : 'text-red-600'
          )}
        >
          +{customers.percentageChange}% from last month
        </span>
      </div>
    </div>
  );
};

export default CustomersCard;
