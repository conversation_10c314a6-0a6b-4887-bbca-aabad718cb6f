import React from 'react';
import { cn } from '../../../lib/utils';
import { Message } from '../types';

interface MessagesCardProps {
  messages: Message[];
  className?: string;
}

const MessagesCard: React.FC<MessagesCardProps> = ({ messages, className }) => {
  return (
    <div
      className={cn(
        'bg-white p-6 rounded-md border border-gray-200',
        className
      )}
    >
      <div className="flex items-center mb-4">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-5 w-5 mr-2 text-gray-700"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z"
          />
        </svg>
        <h3 className="uppercase text-gray-800 font-bold">New Messages</h3>
      </div>

      <div className="space-y-4">
        {messages.map(message => (
          <div key={message.id} className="flex items-start">
            <div className="w-8 h-8 rounded-full bg-green-100 flex-shrink-0 flex items-center justify-center mr-3">
              <span className="text-green-600 text-sm">
                {message.sender.charAt(0)}
              </span>
            </div>
            <div className="flex-1">
              <div className="flex justify-between mb-1">
                <span className="font-medium">{message.sender}</span>
                <span className="text-xs text-gray-500">{message.time}</span>
              </div>
              <p className="text-sm text-gray-600 line-clamp-1">
                {message.message}
              </p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default MessagesCard;
