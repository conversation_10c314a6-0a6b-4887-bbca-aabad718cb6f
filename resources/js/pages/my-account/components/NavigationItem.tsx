import React from 'react';
import { NavLink } from 'react-router-dom';
import { cn } from '../../../lib/utils';

interface NavigationItemProps {
  name: string;
  to: string;
  icon: React.ReactNode;
}

const NavigationItem: React.FC<NavigationItemProps> = ({ name, to, icon }) => {
  return (
    <li>
      <NavLink
        to={to}
        className={({ isActive }) =>
          cn(
            'flex items-center gap-2 p-3 rounded-md transition-colors',
            isActive
              ? 'bg-ox-green-200 text-gray-900'
              : 'hover:bg-gray-100 text-gray-700'
          )
        }
      >
        {icon}
        {name}
      </NavLink>
    </li>
  );
};

export default NavigationItem;
