import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { useMutation, useQuery } from '@tanstack/react-query';
import { ProductFormData, FormOptions, Product } from '../types/products';
import toast from 'react-hot-toast';
import { useConfirm } from '../../../components/ui/confirm-dialog';
import ImageUpload from './ImageUpload';

interface ProductFormProps {
  allowedTypes: string[];
  onSuccess: () => void;
  onCancel: () => void;
  product?: Product; // Optional product for editing
}

const fetchFormOptions = async (): Promise<FormOptions> => {
  const response = await fetch('/api/v1/products/form-options', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json'
    },
    credentials: 'include'
  });

  const data = await response.json();
  if (!data.success) {
    throw new Error(data.message || 'Failed to fetch form options');
  }

  return data.data;
};

const submitProduct = async (
  formData: ProductFormData,
  productId?: number
): Promise<any> => {
  const url = productId ? `/api/v1/products/${productId}` : '/api/v1/products';
  const method = productId ? 'PUT' : 'POST';

  // Create FormData for file upload
  const formDataToSend = new FormData();

  // Add all form fields
  Object.entries(formData).forEach(([key, value]) => {
    if (key === 'image' && value instanceof File) {
      formDataToSend.append('image', value);
    } else if (key === 'standard' || key === 'consulting_subcategories') {
      // Handle arrays
      if (Array.isArray(value)) {
        value.forEach((item, index) => {
          formDataToSend.append(`${key}[${index}]`, item.toString());
        });
      }
    } else if (value !== undefined && value !== null) {
      formDataToSend.append(key, value.toString());
    }
  });

  const response = await fetch(url, {
    method,
    credentials: 'include',
    body: formDataToSend
  });

  const data = await response.json();
  if (!data.success) {
    throw new Error(
      data.message || `Failed to ${productId ? 'update' : 'create'} product`
    );
  }

  return data.data;
};

const ProductForm: React.FC<ProductFormProps> = ({
  allowedTypes,
  onSuccess,
  onCancel,
  product
}) => {
  const [selectedProductType, setSelectedProductType] = useState<string>('');
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [removeImage, setRemoveImage] = useState(false);
  const isEditing = !!product;
  const confirm = useConfirm();

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
    reset
  } = useForm<ProductFormData>();

  const { data: formOptions, isLoading: optionsLoading } = useQuery({
    queryKey: ['formOptions'],
    queryFn: fetchFormOptions
  });

  const mutation = useMutation({
    mutationFn: (formData: ProductFormData) =>
      submitProduct(formData, product?.id),
    onSuccess: data => {
      toast.success(
        `Product ${isEditing ? 'updated' : 'created'} successfully!`
      );
      onSuccess();
    },
    onError: error => {
      console.error(
        `Error ${isEditing ? 'updating' : 'creating'} product:`,
        error
      );
      toast.error(
        error.message || `Failed to ${isEditing ? 'update' : 'create'} product`
      );
    }
  });

  const deleteMutation = useMutation({
    mutationFn: async (productId: number) => {
      const response = await fetch(`/api/v1/products/${productId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include'
      });

      const data = await response.json();
      if (!data.success) {
        throw new Error(data.message || 'Failed to delete product');
      }

      return data;
    },
    onSuccess: () => {
      toast.success('Product deleted successfully!');
      onSuccess();
    },
    onError: error => {
      console.error('Error deleting product:', error);
      toast.error(error.message || 'Failed to delete product');
    }
  });

  const handleDelete = async () => {
    if (!product?.id) return;

    try {
      const confirmed = await confirm({
        title: 'Delete Product',
        body: `Are you sure you want to delete "${product.name}"? This action cannot be undone and the product will no longer be visible to customers.`,
        actionButton: 'Delete',
        actionButtonVariant: 'destructive'
      });

      if (confirmed) {
        deleteMutation.mutate(product.id);
      }
    } catch (error) {
      console.error('Error in delete confirmation:', error);
    }
  };

  useEffect(() => {
    if (product) {
      // Pre-fill form with existing product data
      const productType = product.categories?.[0] || '';
      setSelectedProductType(productType);
      setValue(
        'product_type',
        productType as 'compensation-project' | 'consult-service'
      );
      setValue('name', product.name);
      setValue('price', product.price);
      setValue('short_description', product.short_description || '');
      setValue('description', product.description || '');

      // Fill ACF fields
      if (product.acf_fields) {
        setValue('location', product.acf_fields.location || '');
        setValue('country', product.acf_fields.country || '');
        setValue('local_vat', product.acf_fields.local_vat || 24);
        setValue(
          'can_ask_for_offer',
          product.acf_fields.can_ask_for_offer || false
        );

        // Compensation project fields
        if (product.acf_fields.project_provider) {
          setValue(
            'project_provider',
            product.acf_fields.project_provider || ''
          );
          setValue('project_id', product.acf_fields.project_id || '');
          setValue(
            'available_tonnes',
            product.acf_fields.available_tonnes || 0
          );
          setValue('active', product.acf_fields.active || false);
          setValue(
            'impact_description',
            product.acf_fields.impact_description || ''
          );
          setValue('webpage', product.acf_fields.webpage || '');
          setValue('address', product.acf_fields.address || '');
          setValue('contact', product.acf_fields.contact || '');
          setValue('certified', product.acf_fields.certified || '');
          setValue('certificate', product.acf_fields.certificate || '');
        }

        // Consulting service fields
        if (product.acf_fields.company_name) {
          setValue('service_id', product.acf_fields.service_id || undefined);
          setValue(
            'consultant_id',
            product.acf_fields.consultant_id || undefined
          );
          setValue('company_name', product.acf_fields.company_name || '');
          setValue('time_estimate', product.acf_fields.time_estimate || '');
          setValue('standard', product.acf_fields.standard || []);
        }
      }
    } else if (allowedTypes.length === 1) {
      setSelectedProductType(allowedTypes[0]);
      setValue(
        'product_type',
        allowedTypes[0] as 'compensation-project' | 'consult-service'
      );
    }
  }, [allowedTypes, setValue, product]);

  const onSubmit = (data: ProductFormData) => {
    // Add image data to form
    if (selectedImage) {
      data.image = selectedImage;
    }
    if (removeImage) {
      data.remove_image = true;
    }

    mutation.mutate(data);
  };

  const handleImageChange = (file: File | null) => {
    setSelectedImage(file);
    setRemoveImage(false);
  };

  const handleRemoveImage = () => {
    setSelectedImage(null);
    setRemoveImage(true);
  };

  // Reset image states when product changes
  useEffect(() => {
    if (product) {
      // Reset image states when editing a different product
      setSelectedImage(null);
      setRemoveImage(false);
    } else {
      // Reset for new product creation
      setSelectedImage(null);
      setRemoveImage(false);
    }
  }, [product?.id]); // Only reset when product ID changes

  const getProductTypeLabel = (type: string) => {
    switch (type) {
      case 'compensation-project':
        return 'Compensation Project';
      case 'consult-service':
        return 'Consulting Service';
      default:
        return type;
    }
  };

  if (optionsLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Product Type Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Product Type *
          </label>
          {isEditing ? (
            <div className="px-3 py-2 bg-gray-100 border border-gray-300 rounded-md">
              {getProductTypeLabel(selectedProductType)}
            </div>
          ) : allowedTypes.length > 1 ? (
            <select
              {...register('product_type', {
                required: 'Product type is required'
              })}
              onChange={e => setSelectedProductType(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
            >
              <option value="">Select product type...</option>
              {allowedTypes.map(type => (
                <option key={type} value={type}>
                  {getProductTypeLabel(type)}
                </option>
              ))}
            </select>
          ) : (
            <div className="px-3 py-2 bg-gray-100 border border-gray-300 rounded-md">
              {getProductTypeLabel(allowedTypes[0])}
            </div>
          )}
          {errors.product_type && (
            <p className="mt-1 text-sm text-red-600">
              {errors.product_type.message}
            </p>
          )}
        </div>

        {/* Basic Product Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Product Name *
            </label>
            <input
              type="text"
              {...register('name', { required: 'Product name is required' })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
              placeholder="Enter product name"
            />
            {errors.name && (
              <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Price (€) *
            </label>
            <input
              type="number"
              step="0.01"
              {...register('price', {
                required: 'Price is required',
                min: { value: 0, message: 'Price must be positive' }
              })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
              placeholder="0.00"
            />
            {errors.price && (
              <p className="mt-1 text-sm text-red-600">
                {errors.price.message}
              </p>
            )}
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Short Description
          </label>
          <input
            type="text"
            {...register('short_description')}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
            placeholder="Brief description of the product"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Description
          </label>
          <textarea
            rows={4}
            {...register('description')}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
            placeholder="Detailed description of the product"
          />
        </div>

        {/* Product Image */}
        <ImageUpload
          currentImageUrl={product?.images?.[0]}
          onImageChange={handleImageChange}
          onRemoveImage={handleRemoveImage}
          label="Product Image"
        />

        {/* Common Fields */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Location
            </label>
            <input
              type="text"
              {...register('location')}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
              placeholder="Enter location"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Country
            </label>
            <select
              {...register('country')}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
            >
              <option value="">Select country...</option>
              {formOptions?.countries.map(country => (
                <option key={country} value={country}>
                  {country}
                </option>
              ))}
            </select>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              VAT Percentage (%)
            </label>
            <input
              type="number"
              min="0"
              max="100"
              defaultValue={24}
              {...register('local_vat')}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
              placeholder="24"
            />
          </div>

          <div className="flex items-center mt-8">
            <input
              type="checkbox"
              {...register('can_ask_for_offer')}
              className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
            />
            <label className="ml-2 block text-sm text-gray-900">
              Can ask for offer
            </label>
          </div>
        </div>

        {/* Compensation Project Specific Fields */}
        {selectedProductType === 'compensation-project' && (
          <div className="space-y-6 border-t pt-6">
            <h3 className="text-lg font-medium text-gray-900">
              Compensation Project Details
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Project Provider
                </label>
                <input
                  type="text"
                  {...register('project_provider')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                  placeholder="Enter project provider"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Project ID
                </label>
                <input
                  type="text"
                  {...register('project_id')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                  placeholder="Enter project ID"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Available Tonnes
                </label>
                <input
                  type="number"
                  min="0"
                  {...register('available_tonnes')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                  placeholder="0"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Address
                </label>
                <input
                  type="text"
                  {...register('address')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                  placeholder="Project address"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Certificate
              </label>
              <select
                {...register('certificate')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
              >
                <option value="">Select certificate...</option>
                {formOptions?.certifications.map(cert => (
                  <option key={cert.slug} value={cert.name}>
                    {cert.name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Impact Description
              </label>
              <textarea
                rows={3}
                {...register('impact_description')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                placeholder="Describe the impact of this project"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Webpage
                </label>
                <input
                  type="url"
                  {...register('webpage')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                  placeholder="https://example.com"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Certified By
                </label>
                <input
                  type="text"
                  {...register('certified')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                  placeholder="Enter certifying organization"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Contact Information
              </label>
              <textarea
                rows={3}
                {...register('contact')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                placeholder="Contact details for this project"
              />
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                {...register('active')}
                className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
              />
              <label className="ml-2 block text-sm text-gray-900">
                Project is active
              </label>
            </div>
          </div>
        )}

        {/* Consulting Service Specific Fields */}
        {selectedProductType === 'consult-service' && (
          <div className="space-y-6 border-t pt-6">
            <h3 className="text-lg font-medium text-gray-900">
              Consulting Service Details
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Company Name
                </label>
                <input
                  type="text"
                  {...register('company_name')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                  placeholder="Enter company name"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Time Estimate
                </label>
                <input
                  type="text"
                  {...register('time_estimate')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                  placeholder="e.g., 2-3 weeks"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Service ID
                </label>
                <input
                  type="number"
                  {...register('service_id')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                  placeholder="Service ID"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Consultant ID
                </label>
                <input
                  type="number"
                  {...register('consultant_id')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                  placeholder="Consultant ID"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Service Categories
              </label>
              <div className="space-y-2 max-h-48 overflow-y-auto border border-gray-300 rounded-md p-3">
                {formOptions?.consulting_subcategories.map(subcategory => (
                  <div key={subcategory.id} className="flex items-center">
                    <input
                      type="checkbox"
                      value={subcategory.id}
                      {...register('consulting_subcategories')}
                      className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                    />
                    <label className="ml-2 block text-sm text-gray-900">
                      {subcategory.name}
                    </label>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Standards
              </label>
              <div className="space-y-2 max-h-48 overflow-y-auto border border-gray-300 rounded-md p-3">
                {formOptions?.standards.map(standard => (
                  <div key={standard} className="flex items-center">
                    <input
                      type="checkbox"
                      value={standard}
                      {...register('standard')}
                      className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                    />
                    <label className="ml-2 block text-sm text-gray-900">
                      {standard}
                    </label>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Form Actions */}
        <div className="flex justify-between items-center pt-6 border-t">
          <div>
            {isEditing && (
              <button
                type="button"
                onClick={handleDelete}
                disabled={deleteMutation.isPending}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {deleteMutation.isPending ? 'Deleting...' : 'Delete'}
              </button>
            )}
          </div>

          <div className="flex space-x-4">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={mutation.isPending}
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {mutation.isPending
                ? isEditing
                  ? 'Updating...'
                  : 'Creating...'
                : isEditing
                ? 'Update Product'
                : 'Create Product'}
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default ProductForm;
