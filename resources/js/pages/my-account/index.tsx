import React, { useEffect } from 'react';
import {
  BrowserRouter as Router,
  Route,
  Routes,
  Navigate
} from 'react-router-dom';
import { useAccountStore } from './store/accountStore';
import AccountLayout from './layouts/AccountLayout';
import DashboardView from './views/DashboardView';
import ChatView from './views/ChatView';
import OrdersView from './views/OrdersView';
import SingleOrderView from './views/SingleOrderView';
import QueryProvider from './providers/QueryProvider';
import { User } from './types/index';
import { AlertDialogProvider } from '../../components/ui/confirm-dialog';

// Import our fully implemented views
import AccountView from './views/AccountView';
import ProductsView from './views/ProductsView';
import ProductsListView from './views/ProductsListView';
import ProductCreateView from './views/ProductCreateView';
import ProductEditView from './views/ProductEditView';
import OffersView from './views/OffersView';
import CustomerOrdersView from './views/CustomerOrdersView';
import OrganizationView from './views/OrganizationView';

interface MyAccountProps {
  user?: User;
}

const MyAccount: React.FC<MyAccountProps> = ({ user: userProp }) => {
  const {
    user,
    carbonGoal,
    revenue,
    customers,
    messages,
    orders,
    setInitialData
  } = useAccountStore();

  useEffect(() => {
    if (userProp) {
      setInitialData({ user: userProp });
    }
  }, [userProp, setInitialData]);

  // Check if user has privileged roles (consultant, compensation-project-owner, administrator)
  const hasPrivilegedRole = (): boolean => {
    if (!user.roles || user.roles.length === 0) return false;
    return user.roles.some(
      role =>
        role &&
        ['consultant', 'compensation-project-owner', 'administrator'].includes(
          role.role
        )
    );
  };

  // Protected Route Component
  const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({
    children
  }) => {
    if (!hasPrivilegedRole()) {
      return <Navigate to="dashboard" replace />;
    }
    return <>{children}</>;
  };

  return (
    <QueryProvider>
      <AlertDialogProvider>
        <Router basename="/my-account">
          <AccountLayout user={user}>
            <Routes>
              <Route path="/" element={<Navigate to="dashboard" />} />
              <Route
                path="dashboard"
                element={
                  <DashboardView
                    carbonGoal={carbonGoal}
                    revenue={revenue}
                    customers={customers}
                    messages={messages}
                    orders={orders}
                  />
                }
              />
              <Route path="chat" element={<ChatView />} />
              <Route path="orders" element={<OrdersView />} />
              <Route path="orders/:orderId" element={<SingleOrderView />} />
              <Route
                path="customer-orders"
                element={
                  <ProtectedRoute>
                    <CustomerOrdersView />
                  </ProtectedRoute>
                }
              />
              <Route path="offers" element={<OffersView />} />
              <Route path="account" element={<AccountView />} />
              <Route path="organization" element={<OrganizationView />} />
              <Route
                path="products"
                element={
                  <ProtectedRoute>
                    <ProductsView />
                  </ProtectedRoute>
                }
              >
                <Route index element={<ProductsListView />} />
                <Route path="create" element={<ProductCreateView />} />
                <Route path=":id" element={<ProductEditView />} />
              </Route>
            </Routes>
          </AccountLayout>
        </Router>
      </AlertDialogProvider>
    </QueryProvider>
  );
};

export default MyAccount;
