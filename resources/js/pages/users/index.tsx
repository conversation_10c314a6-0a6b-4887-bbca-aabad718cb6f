import React from 'react';
import Sidebar from '../../components/dashboard/Sidebar';
import CarbonGoalCard from '../../components/dashboard/CarbonGoalCard';
import StatCard from '../../components/dashboard/StatCard';
import MessagesCard from '../../components/dashboard/MessagesCard';
import OrdersTable from '../../components/dashboard/OrdersTable';

const Index: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50 relative flex gap-2">
      <Sidebar />
      <div className="pl-4 p-8 w-full">
        <h1 className="text-2xl font-bold mb-8">DASHBOARD</h1>

        <div className="flex gap-6 mb-8">
          <CarbonGoalCard percentage={100} />
          <StatCard
            title="TOTAL REVENUE"
            value="€45,231.89"
            icon="💰"
            change={{ value: 20.1, period: 'last month' }}
          />
          <StatCard
            title="CUSTOMERS"
            value="+2350"
            icon="👥"
            change={{ value: 180.1, period: 'last month' }}
          />
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <MessagesCard />
          <OrdersTable />
        </div>
      </div>
    </div>
  );
};

export default Index;
