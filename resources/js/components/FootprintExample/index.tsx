import { useEffect, useState } from 'react';

interface FootprintExampleProps {
    scope1: number;
    scope2: number;
    scope3: number;
    total: number;
    projects: Array<{
        name: string;
        id: number;
        price: number;
    }>;
}

export default function FootprintExample({ scope1, scope2, scope3, total, projects }: FootprintExampleProps) {
    const [activeScope, setActiveScope] = useState<'scope12' | 'scopeTotal'>('scope12');
    const [selectedProject, setSelectedProject] = useState<number | null>(null);
    
    // ... rest of the logic
} 