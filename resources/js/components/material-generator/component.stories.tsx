import { Meta, StoryObj } from '@storybook/react';
import Index from './index';

const meta: Meta<typeof Index> = {
  title: 'Components/MaterialGenerator',
  component: Index
};

export default meta;

type Story = StoryObj<typeof Index>;

export const Default: Story = {
  args: {
    process: {
      company: 'Example Ltd.',
      ghgScopes: [1, 2, 3],
      description:
        'Company has offset its carbon footprint by 200 tons of CO2e in 2023 by investing in a reforestation project in Finland.',
      date: '2023-10-01',
      country: 'Finland',
      verified: true,
      verifiedBy: 'Macon',
      carbonFootprint: 100,
      carbonOffset: 200,
      compensationTarget: 'All hotels in Finland, 2023',
      logoUrl: 'https://picsum.photos/100/100'
    }
  },
  render: args => <Index {...args} />
};
