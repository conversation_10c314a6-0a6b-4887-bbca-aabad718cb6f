import React, { JSX } from 'react'; // Import React and JSX
import {
  BaseTemplate,
  Field,
  OffsetProcess,
  TemplateRenderProps
} from '../base';
import { useStore } from 'zustand'; // Import useStore hook
import { Textfit } from 'react-textfit'; // Import Textfit for responsive text
import OverlayWrapper from '@/components/ui/overlay';
import forestImg from './forest.jpg';

export class ForestBasicTemplate extends BaseTemplate {
  constructor(
    offsetProcess: OffsetProcess,
    mainColor?: string, // Make colors optional to use store defaults
    secondaryColor?: string
  ) {
    // Pass colors from store if not provided
    super(offsetProcess, mainColor, secondaryColor);
    // Initialize the store using the base template method
    this.initializeStore();
  }

  // Override to add template-specific translatable content
  protected addTemplateSpecificContent(content: Record<string, string>): void {
    content['template.ghgScopes'] = 'GHG Scopes';
    content['template.forestText'] =
      'Carbon offset through forest conservation';
  }

  // Renamed from fields() to avoid conflict and clarify purpose
  getAllFieldDefinitions(): Field[] {
    // Define fields without render functions, values come from store
    return [
      {
        name: 'logoUrl',
        label: 'Logo URL',
        type: 'image',
        defaultValue: this.offsetProcess.logoUrl || '', // Provide default empty string
        canBeEdited: true,
        canBeRemoved: true,
        template: 'extensive',
        valueTranslatable: false
      },
      {
        name: 'company',
        label: 'Company Name',
        type: 'text',
        defaultValue: this.offsetProcess.company,
        canBeEdited: true,
        canBeRemoved: true,
        template: 'simple',
        valueTranslatable: false // Company names typically aren't translated
      },
      {
        name: 'mainText',
        label: 'Main Text',
        type: 'text',
        defaultValue: this.mainText, // Use getter from BaseTemplate
        canBeEdited: true, // Allow editing main text now
        canBeRemoved: true,
        template: 'simple',
        valueTranslatable: true // Main text should be translated
      },
      {
        name: 'compensationTarget',
        label: 'Compensation Target',
        type: 'text',
        defaultValue: this.offsetProcess.compensationTarget,
        canBeEdited: false, // Keep as non-editable for this example
        canBeRemoved: true,
        template: 'simple',
        valueTranslatable: true
      },
      {
        name: 'ghgScopes',
        label: 'GHG Scopes',
        type: 'text', // Representing as text for simplicity, could be multi-select later
        defaultValue: this.ghgScopes, // Use getter from BaseTemplate
        canBeEdited: false,
        canBeRemoved: true,
        template: 'extensive',
        valueTranslatable: false // Just numbers, no need to translate
      },
      {
        name: 'description',
        label: 'Description',
        type: 'text', // Should be 'textarea' ideally, handle in edit panel
        defaultValue: this.offsetProcess.description,
        canBeEdited: true,
        canBeRemoved: true,
        template: 'extensive',
        valueTranslatable: true // Description content should be translated
      },
      {
        name: 'verified',
        label: 'Verified By',
        type: 'boolean', // Or text if displaying 'Verified By: Name'
        defaultValue: this.offsetProcess.verified
          ? this.offsetProcess.verifiedBy
          : 'Not Verified', // Store display text
        canBeEdited: false, // Verification status usually not editable directly
        canBeRemoved: true,
        template: 'normal',
        valueTranslatable: true // "Verified By" or "Not Verified" text should be translated
      }
    ];
  }

  // Helper to get a single field definition
  getFieldDefinition(name: string): Field | undefined {
    return this.getAllFieldDefinitions().find(f => f.name === name);
  }

  // The actual template rendering logic, using store values and order
  template(props: TemplateRenderProps) {
    // Use the hook within the component part
    const { fields, fieldOrder } = useStore(this.getStoreHook()); // Use getStoreHook() from base class
    const mainColor = this.mainColor; // Use colors from BaseTemplate instance
    const secondaryColor = this.secondaryColor;

    // Use React.useEffect to persist state to localStorage when component unmounts
    React.useEffect(() => {
      // This will run when fieldOrder changes or component unmounts
      return () => {
        try {
          if (Array.isArray(fieldOrder) && fieldOrder.length > 0) {
            localStorage.setItem(
              'material-generator-field-order',
              JSON.stringify(fieldOrder)
            );
          }
        } catch (e) {
          console.error(
            'Failed to save field order to localStorage on unmount',
            e
          );
        }
      };
    }, [fieldOrder]);

    // Helper to create the element based on field name
    const createElement = (name: string): JSX.Element | null => {
      const fieldDefinition = this.getFieldDefinition(name);
      if (!fieldDefinition) return null; // Should not happen if fieldOrder is correct

      switch (name) {
        case 'logoUrl':
          return fields.logoUrl ? (
            <img
              key={name} // Add key for list rendering
              className="w-20 h-20 object-contain mb-2"
              src={fields.logoUrl}
              alt="Logo"
            />
          ) : null;
        case 'company':
          return (
            <div key={name} className="w-full max-w-[80%]">
              <Textfit
                mode="single"
                max={36}
                style={{ color: mainColor }}
                className="font-light text-center"
              >
                {fields.company}
              </Textfit>
            </div>
          );
        case 'mainText':
          return (
            <div key={name} className="w-full max-w-[80%] mt-4">
              <Textfit
                mode="multi"
                max={48}
                style={{ color: mainColor }}
                className="font-medium uppercase text-center break-words"
              >
                {fields.mainText}
              </Textfit>
            </div>
          );
        case 'compensationTarget':
          return (
            <div
              key={name}
              style={{ backgroundColor: mainColor }}
              className="px-4 py-2 rounded-md mt-4 max-w-[90%]"
            >
              <Textfit
                mode="single"
                max={18}
                style={{ color: secondaryColor }}
                className="font-light text-center"
              >
                Compensation Target: {fields.compensationTarget}
              </Textfit>
            </div>
          );
        case 'ghgScopes':
          return (
            <div key={name} className="w-full max-w-[80%]">
              {' '}
              <Textfit
                mode="single"
                max={16}
                style={{ color: mainColor }}
                className="font-light text-center"
              >
                GHG Scopes {fields.ghgScopes}
              </Textfit>
            </div>
          );
        case 'description':
          return (
            <div key={name} className="w-full max-w-[80%] mt-2">
              <Textfit
                mode="multi"
                min={10}
                max={16}
                style={{ color: mainColor }}
                className="text-center"
              >
                {fields.description}
              </Textfit>
            </div>
          );
        case 'verified':
          return (
            <div key={name} className="mt-2">
              <Textfit
                mode="single"
                max={14}
                style={{ color: mainColor }}
                className="font-semibold text-center"
              >
                {fields.verified}
              </Textfit>
            </div>
          );
        default:
          console.warn(`No rendering defined for field: ${name}`);
          return null;
      }
    };

    return (
      <div className="flex flex-col items-center justify-center h-full w-full relative gap-1">
        {/* Background forest image */}
        <OverlayWrapper
          color={secondaryColor}
          brightness={0.6}
          className="absolute inset-0"
        >
          <img
            src={forestImg}
            alt="Forest Background"
            className="absolute inset-0 w-full h-full object-cover"
          />
        </OverlayWrapper>
        <div className="flex flex-col items-center justify-center w-full h-full relative max-w-[60%] max-h-[60%]">
          {/* Render fields based on the fieldOrder */}
          {Array.isArray(fieldOrder) && fieldOrder.length > 0
            ? fieldOrder.map((fieldName, index) => {
                const element = createElement(fieldName);
                // Add position data attribute for debugging
                return element
                  ? React.cloneElement(element, {
                      'data-position': index,
                      'data-field-name': fieldName,
                      key: `${fieldName}-${index}`
                    })
                  : null;
              })
            : // Fallback if fieldOrder is not properly defined
              Object.keys(fields).map((fieldName, index) => {
                const element = createElement(fieldName);
                return element
                  ? React.cloneElement(element, {
                      'data-position': index,
                      'data-field-name': fieldName,
                      key: `${fieldName}-${index}`
                    })
                  : null;
              })}
        </div>
        {/* Add QR Code or other static elements if needed */}
      </div>
    );
  }
}

export default ForestBasicTemplate;
