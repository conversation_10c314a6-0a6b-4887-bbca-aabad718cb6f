import { Meta, StoryObj } from '@storybook/react';
import Template from './index';
import { OffsetProcess, TemplateRenderProps } from '../base';
import { useState, useEffect } from 'react';
import * as htmlToImage from 'html-to-image';

const API = import.meta.env.APP_URL;

const meta: Meta<typeof Template> = {
  title: 'Templates/Main'
};

export default meta;

type Story = StoryObj<TemplateRenderProps & { offsetProcess: OffsetProcess }>;

export const Default: Story = {
  args: {
    logoProps: {
      position: 'bottom-left',
      isPreview: true
    },
    offsetProcess: {
      company: 'Example Ltd.',
      ghgScopes: [1, 2, 3],
      description:
        'Company has offset its carbon footprint by 200 tons of CO2e in 2023 by investing in a reforestation project in Finland.',
      date: '2023-10-01',
      country: 'Finland',
      verified: true,
      verifiedBy: 'Macon',
      carbonFootprint: 100,
      carbonOffset: 200,
      compensationTarget: 'All hotels in Finland, 2023',
      logoUrl: 'https://picsum.photos/100/100'
    }
  },
  render: args => {
    // Use React hooks for state management
    const [offsetProcess, setOffsetProcess] = useState<OffsetProcess>(
      args.offsetProcess
    );

    const [targetLang, setTargetLang] = useState('en');
    const [isTranslating, setIsTranslating] = useState(false);

    const [textToTranslate, setTextToTranslate] = useState(undefined);

    const template = new Template(offsetProcess, textToTranslate);

    // Update state when args change
    useEffect(() => {
      setOffsetProcess(args.offsetProcess);
    }, [args.offsetProcess]);

    const handleTranslate = async () => {
      if (isTranslating || targetLang === 'en') return;

      setIsTranslating(true);

      try {
        // Get CSRF token from meta tag
        const csrfToken =
          document
            .querySelector('meta[name="csrf-token"]')
            ?.getAttribute('content') || '';

        const process = args.offsetProcess;
        const template = new Template(process);

        // Prepare texts to translate
        const textsToTranslate = {
          description: process.description,
          compensationTarget: process.compensationTarget,
          mainText: template.mainText
        };

        // Make API request
        const response = await fetch(`${API}/api/v1/translate`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Accept: 'application/json',
            'X-CSRF-TOKEN': csrfToken
          },
          body: JSON.stringify({
            text: JSON.stringify(textsToTranslate),
            target: targetLang,
            source: 'en'
          })
        });

        const result = await response.json();

        if (result.success) {
          // Parse the translated JSON
          const {
            description,
            compensationTarget,
            mainText,
            ...textsToTranslate
          } = JSON.parse(result.translation);

          // Update the offsetProcess with translations
          setOffsetProcess({
            ...offsetProcess,
            description: description,
            compensationTarget: compensationTarget,
            mainText: mainText
          });
          setTextToTranslate(textsToTranslate);
        }
      } catch (error) {
        console.error('Translation error:', error);
      } finally {
        setIsTranslating(false);
      }
    };

    // Create template instance with current offsetProcess

    // Return the template and translation controls
    return (
      <div>
        <div className="w-[600px] h-[600px]" id="template">
          {template.render(args)}
        </div>

        <div className="mt-4 flex items-center gap-2">
          <select
            className="border border-gray-300 rounded px-2 py-1"
            value={targetLang}
            onChange={e => setTargetLang(e.target.value)}
          >
            <option value="en">English</option>
            <option value="es">Spanish</option>
            <option value="fr">French</option>
            <option value="de">German</option>
            <option value="it">Italian</option>
            <option value="fi">Finnish</option>
            <option value="sv">Swedish</option>
          </select>

          <button
            className="px-4 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-gray-300"
            onClick={handleTranslate}
            disabled={isTranslating || targetLang === 'en'}
          >
            {isTranslating ? 'Translating...' : 'Translate'}
          </button>
          <button
            className="px-4 py-1 bg-green-500 text-white rounded hover:bg-green-600"
            onClick={() => {
              const node = document.getElementById('template');
              if (node) {
                htmlToImage
                  .toPng(node, { canvasWidth: 1800, canvasHeight: 1800 })
                  .then(dataUrl => {
                    const link = document.createElement('a');
                    link.href = dataUrl;
                    link.download = 'template.png';
                    link.click();
                  })
                  .catch(error => {
                    console.error('Error generating image:', error);
                  });
              }
            }}
          >
            Download
          </button>
        </div>
      </div>
    );
  }
};
