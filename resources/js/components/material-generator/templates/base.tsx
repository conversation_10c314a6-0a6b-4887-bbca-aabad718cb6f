import React, { JSX } from 'react';
import { twMerge } from 'tailwind-merge';
import Logo from '../components/logo';
import { create, StoreApi } from 'zustand';
const API = import.meta.env.APP_URL;

export interface OffsetProcess {
  company: string;
  ghgScopes: (1 | 2 | 3)[];
  description: string;
  compensationTarget: string;
  date: string;
  country: string;
  verified: boolean;
  verifiedBy: string;
  carbonFootprint: number;
  carbonOffset: number;
  logoUrl?: string;
  mainText?: string;
}

export type FieldType =
  | 'image'
  | 'text'
  | 'number'
  | 'date'
  | 'boolean'
  | 'select';

export type Field = {
  name: string;
  label: string;
  type: FieldType;
  options?: string[];
  defaultValue?: string | number | boolean;
  canBeRemoved: boolean;
  canBeEdited: boolean;
  template: 'simple' | 'normal' | 'extensive';
  valueTranslatable?: boolean; // Whether the field value should be translated
};

// Define template view types
export type TemplateView = 'simple' | 'normal' | 'extensive' | 'custom';

type Position = 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';

type LogoProps = {
  position: Position;
  isPreview?: boolean;
};

export type TemplateRenderProps = {
  logoProps: LogoProps;
  templateSize?: { width: number; height: number };
};

// Define the universal shape of the template store's state
export interface TranslationState {
  currentLanguage: string;
  translatedLabels: Record<string, string>; // Store translated field labels
  originalValues: Record<string, any>; // Store original values before translation
  isTranslating: boolean; // Flag to indicate translation in progress
}

export interface TemplateState {
  fields: Record<string, any>; // Store current values of fields
  visibleFields: string[]; // Store names of currently visible fields
  fieldOrder: string[]; // Store the order of fields
  templateView: TemplateView; // Current template view mode
  currentLanguage: string; // Current language of the template
  translatedLabels: Record<string, string>; // Store translated field labels
  originalValues: Record<string, any>; // Store original values before translation
  isTranslating: boolean; // Flag to indicate translation in progress
  setFieldValue: (name: string, value: any) => void;
  toggleFieldVisibility: (name: string) => void;
  resetFieldValue: (name: string) => void;
  setFieldOrder: (newOrder: string[]) => void;
  resetFieldOrder: () => void;
  setTemplateView: (view: TemplateView) => void; // Function to set template view mode
  setLanguage: (
    language: string,
    translatedLabels: Record<string, string>,
    translatedValues?: Record<string, any>
  ) => void;
  resetTranslation: () => void;
  getInitialState: <T extends BaseTemplate>(
    templateInstance: T
  ) => Partial<TemplateState>; // Function to initialize
}

// Universal factory function to create the template store
export const createTemplateStore = <T extends BaseTemplate>(
  templateInstance: T
) =>
  create<TemplateState>((set, get) => ({
    fields: {}, // Initial empty fields
    visibleFields: [], // Initial empty visible fields
    fieldOrder: [], // Initial empty field order
    templateView: 'extensive', // Default to extensive view
    currentLanguage: 'en', // Default language is English
    translatedLabels: {}, // Initial empty translated labels
    originalValues: {}, // Initial empty original values
    isTranslating: false, // Initially not in translation state

    setFieldValue: (name, value) =>
      set(state => ({
        fields: { ...state.fields, [name]: value }
      })),

    toggleFieldVisibility: name =>
      set(state => {
        const isVisible = state.visibleFields.includes(name);
        const fieldDefinition = templateInstance.getFieldDefinition(name);
        if (!fieldDefinition?.canBeRemoved) return state; // Don't toggle if not removable

        const newVisibleFields = isVisible
          ? state.visibleFields.filter(fName => fName !== name)
          : [...state.visibleFields, name];

        const newFieldOrder = isVisible
          ? state.fieldOrder.filter(fName => fName !== name)
          : [...state.fieldOrder, name];

        // Update localStorage when visibility changes affect order
        try {
          localStorage.setItem(
            'material-generator-field-order',
            JSON.stringify(newFieldOrder)
          );
        } catch (e) {
          console.error(
            'Failed to save field order to localStorage on toggle',
            e
          );
        }

        return {
          visibleFields: newVisibleFields,
          fieldOrder: newFieldOrder,
          templateView: 'custom' // When manually toggling fields, switch to custom view
        };
      }),

    resetFieldValue: name =>
      set(state => {
        const fieldDefinition = templateInstance.getFieldDefinition(name);
        if (!fieldDefinition) return state;
        return {
          fields: { ...state.fields, [name]: fieldDefinition.defaultValue }
        };
      }),

    setLanguage: (language, translatedLabels, translatedValues = {}) =>
      set(state => {
        // First translation - backup original values
        const originalValues =
          state.currentLanguage === 'en' && language !== 'en'
            ? { ...state.fields } // Only store originals the first time we translate
            : state.originalValues;

        // Update field values for translatable fields
        let updatedFields = { ...state.fields };
        Object.entries(translatedValues).forEach(([field, value]) => {
          updatedFields[field] = value;
        });

        return {
          currentLanguage: language,
          translatedLabels,
          originalValues,
          fields: updatedFields,
          isTranslating: false
        };
      }),

    resetTranslation: () =>
      set(state => {
        // Only reset if we're not in English
        if (state.currentLanguage === 'en') {
          return state;
        }

        // Restore original field values
        return {
          currentLanguage: 'en',
          translatedLabels: {},
          fields: state.originalValues,
          isTranslating: false
        };
      }),

    setTemplateView: view => {
      // Get all fields and filter based on the template view
      const allFields = templateInstance.getAllFieldDefinitions();
      let newVisibleFields: string[] = [];

      switch (view) {
        case 'simple':
          // Show only simple fields
          newVisibleFields = allFields
            .filter(field => field.template === 'simple')
            .map(field => field.name);
          break;

        case 'normal':
          // Show simple and normal fields
          newVisibleFields = allFields
            .filter(
              field =>
                field.template === 'simple' || field.template === 'normal'
            )
            .map(field => field.name);
          break;

        case 'extensive':
          // Show all fields
          newVisibleFields = allFields.map(field => field.name);
          break;

        case 'custom':
          // Don't change visible fields for custom view
          return set({ templateView: view });

        default:
          console.warn(`Unknown template view: ${view}`);
          return; // Do nothing for unknown views
      }

      // Update the field order to match visible fields
      const currentFieldOrder = get().fieldOrder || [];
      const newFieldOrder = [
        // Keep the original order for fields that are still visible
        ...currentFieldOrder.filter(name => newVisibleFields.includes(name)),
        // Add any new fields that weren't in the original order
        ...newVisibleFields.filter(name => !currentFieldOrder.includes(name))
      ];

      // Update localStorage
      try {
        localStorage.setItem(
          'material-generator-field-order',
          JSON.stringify(newFieldOrder)
        );
      } catch (e) {
        console.error(
          'Failed to save field order to localStorage on template change',
          e
        );
      }

      return set({
        templateView: view,
        visibleFields: newVisibleFields,
        fieldOrder: newFieldOrder
      });
    },

    setFieldOrder: newOrder => {
      // Store the field order in localStorage for persistence
      try {
        localStorage.setItem(
          'material-generator-field-order',
          JSON.stringify(newOrder)
        );
      } catch (e) {
        console.error('Failed to save field order to localStorage', e);
      }

      set(state => ({
        fieldOrder: newOrder
      }));
    },

    resetFieldOrder: () => {
      // Clear the stored order from localStorage
      try {
        localStorage.removeItem('material-generator-field-order');
      } catch (e) {
        console.error('Failed to remove field order from localStorage', e);
      }

      const initialState = get().getInitialState(templateInstance);
      set(state => ({
        fieldOrder: initialState.fieldOrder || []
      }));
    },

    // Helper to initialize state based on template fields
    getInitialState: instance => {
      // Initialize fields with default values
      const initialFields: Record<string, any> = {};
      const allFields = instance.getAllFieldDefinitions();
      const allFieldNames: string[] = [];

      allFields.forEach(field => {
        initialFields[field.name] = field.defaultValue;
        allFieldNames.push(field.name);
      });

      // Default to extensive view showing all fields
      const defaultView: TemplateView = 'extensive';

      // Try to load saved field order from localStorage
      let fieldOrder = [...allFieldNames]; // Default to all fields

      try {
        const savedOrder = localStorage.getItem(
          'material-generator-field-order'
        );
        if (savedOrder) {
          const savedFieldOrder = JSON.parse(savedOrder);

          // Ensure all fields are included
          const missingFields = allFieldNames.filter(
            name => !savedFieldOrder.includes(name)
          );
          fieldOrder = [...savedFieldOrder, ...missingFields];
        }
      } catch (e) {
        console.error('Failed to load field order from localStorage', e);
      }

      return {
        fields: initialFields,
        visibleFields: allFieldNames, // All fields visible by default
        fieldOrder: fieldOrder,
        templateView: defaultView,
        currentLanguage: 'en', // Default language is English
        translatedLabels: {}, // No translations initially
        originalValues: {}, // No original values to store yet
        isTranslating: false // Not translating initially
      };
    }
  }));

export abstract class BaseTemplate {
  protected storeHook!: StoreApi<TemplateState>; // '!' tells TypeScript that this will be initialized

  constructor(
    public offsetProcess: OffsetProcess,
    public mainColor: string = '#A5E6AA',
    public secondaryColor: string = '#286444'
  ) {}

  abstract template(props: TemplateRenderProps): JSX.Element;

  // Abstract methods that must be implemented by subclasses
  abstract getAllFieldDefinitions(): Field[];
  abstract getFieldDefinition(name: string): Field | undefined;

  /**
   * Gets all content that needs translation from the template
   * This includes field labels and translatable field values
   * @returns Object containing content that needs translation
   */
  getTranslatableContent(): Record<string, string> {
    const allFields = this.getAllFieldDefinitions();
    const { fields } = this.storeHook.getState();
    const content: Record<string, string> = {};

    // Add field labels for translation
    allFields.forEach(field => {
      content[`label.${field.name}`] = field.label;
    });

    // Add field values that are marked as translatable
    allFields.forEach(field => {
      if (field.valueTranslatable && typeof fields[field.name] === 'string') {
        content[`value.${field.name}`] = fields[field.name] as string;
      }
    });

    // Add any template-specific texts
    this.addTemplateSpecificContent(content);

    return content;
  }

  /**
   * Override this method in subclasses to add template-specific
   * text content that needs translation
   */
  protected addTemplateSpecificContent(content: Record<string, string>): void {
    // Default implementation does nothing
    // Subclasses can override to add template-specific content
  }

  /**
   * Apply translated content to the template
   * @param translatedContent Object containing translated content
   * @param language Target language code
   * @returns Promise that resolves when translation is applied
   */
  async applyTranslation(
    translatedContent: Record<string, string>,
    language: string
  ): Promise<void> {
    const { setLanguage } = this.storeHook.getState();

    // Extract translated labels and values
    const translatedLabels: Record<string, string> = {};
    const translatedValues: Record<string, string> = {};

    // Template-specific content goes to translatedValues for proper handling
    const templateSpecificValues: Record<string, string> = {};

    // Process the translated content from the API response
    for (const [key, value] of Object.entries(translatedContent)) {
      // Skip non-string values or empty values
      if (typeof value !== 'string' || value === '') {
        continue;
      }

      if (key.startsWith('label.')) {
        const fieldName = key.replace('label.', '');
        translatedLabels[fieldName] = value;
      } else if (key.startsWith('value.')) {
        const fieldName = key.replace('value.', '');
        translatedValues[fieldName] = value;
      } else if (key.startsWith('template.')) {
        const templateKey = key.replace('template.', '');
        templateSpecificValues[templateKey] = value;
      }
    }

    // Map template-specific translations to appropriate fields
    Object.entries(templateSpecificValues).forEach(([key, value]) => {
      // Check if we should apply this template text to a specific field
      // For example, template.ghgScopes might apply to the ghgScopes field label
      if (key === 'mainText' && !translatedValues.mainText) {
        translatedValues.mainText = value;
      }
    });

    console.log('Applying translations:', {
      language,
      labels: translatedLabels,
      values: translatedValues
    });

    // Update store with translated content
    setLanguage(language, translatedLabels, translatedValues);
  }

  /**
   * Main translation method - calls the translation API and applies the result
   * @param language Target language code
   * @returns Promise that resolves when translation is complete
   */
  async translate(language: string): Promise<void> {
    const state = this.storeHook.getState();

    if (state.isTranslating) {
      throw new Error('Translation already in progress');
    }

    if (state.currentLanguage === language) {
      return; // Already translated to this language
    }

    // Set translating state
    this.storeHook.setState({ isTranslating: true });

    try {
      // Get content to translate
      const content = this.getTranslatableContent();

      // Call translation API
      const translatedContent = await this.callTranslationAPI(
        content,
        language
      );

      // Apply translated content
      await this.applyTranslation(translatedContent, language);
    } catch (error) {
      this.storeHook.setState({ isTranslating: false });
      throw error;
    }
  }

  /**
   * Reset translation state to original language
   */
  resetTranslation(): void {
    const { resetTranslation } = this.storeHook.getState();
    resetTranslation();
  }
  /**
   * Call the translation API to translate content using WordPress WPML
   * @param content Content to translate
   * @param targetLanguage Target language code
   * @returns Promise that resolves with translated content
   */
  protected async callTranslationAPI(
    content: Record<string, string>,
    targetLanguage: string
  ): Promise<Record<string, string>> {
    try {
      const csrfToken =
        document
          .querySelector('meta[name="csrf-token"]')
          ?.getAttribute('content') || '';

      const response = await fetch(`${API}/api/v1/translate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
          'X-CSRF-TOKEN': csrfToken
        },
        body: JSON.stringify({
          text: JSON.stringify(content),
          source: 'en',
          target: targetLanguage
        })
      });

      if (!response.ok) {
        throw new Error(`Translation API error: ${response.statusText}`);
      }

      const result = await response.json();
      const translatedContent = JSON.parse(result.translation);
      console.log('🚀 ~ BaseTemplate ~ translatedContent:', translatedContent);

      if (!translatedContent || typeof translatedContent !== 'object') {
        throw new Error('Invalid response from translation API');
      }

      return translatedContent;
    } catch (error) {
      console.error('Translation API error:', error);
      // On error, return original content
      return content;
    }
  }

  // Method to get the store hook that was created for this template
  getStoreHook(): StoreApi<TemplateState> {
    return this.storeHook;
  }

  // Method to initialize the store hook - called by subclasses
  protected initializeStore(): void {
    this.storeHook = createTemplateStore(this);
    const initialState = this.storeHook.getState().getInitialState(this);
    this.storeHook.setState(initialState);
  }

  hasCompensated(): boolean {
    return (
      this.offsetProcess.carbonFootprint <= this.offsetProcess.carbonOffset
    );
  }

  isCarbonNeutral(): boolean {
    return (
      this.offsetProcess.carbonFootprint === this.offsetProcess.carbonOffset
    );
  }

  isCarbonNegative(): boolean {
    return this.offsetProcess.carbonFootprint < this.offsetProcess.carbonOffset;
  }

  isCarbonPositive(): boolean {
    return this.offsetProcess.carbonFootprint > this.offsetProcess.carbonOffset;
  }

  get ghgScopes(): string {
    const scopes = this.offsetProcess.ghgScopes;
    const min = Math.min(...scopes);
    const max = Math.max(...scopes);
    if (min === max) {
      return `${min}`;
    }
    return `${min}-${max}`;
  }

  get mainText(): string {
    if (this.offsetProcess.mainText) {
      return this.offsetProcess.mainText;
    }
    if (this.isCarbonNeutral()) {
      return "We're carbon neutral";
    } else if (this.isCarbonNegative()) {
      return "We're carbon negative";
    } else if (this.isCarbonPositive()) {
      return 'Low-carbon operations';
    } else {
      return 'Working towards carbon neutrality';
    }
  }

  render(props: React.PropsWithChildren<TemplateRenderProps>): JSX.Element {
    const mainColor = this.mainColor;
    const secondaryColor = this.secondaryColor;

    function WithLogo({
      logoProps: { position, isPreview = false },
      children
    }: React.PropsWithChildren<TemplateRenderProps>) {
      return (
        <div className="relative w-full h-full">
          {/* Logo will be placed here based on the position */}
          <div
            className={twMerge(
              'absolute z-10',
              position === 'top-left' && 'top-5 left-5',
              position === 'top-right' && 'top-5 right-5',
              position === 'bottom-left' && 'bottom-5 left-5',
              position === 'bottom-right' && 'bottom-5 right-5'
            )}
          >
            <div className="flex space-x-1 items-center">
              <Logo className="h-8" color={mainColor} />
              <div className="space-y-0.5">
                <div
                  style={{ color: mainColor }}
                  className="text-xs font-light text-white leading-none"
                >
                  MADE WITH
                </div>
                <div
                  style={{ color: mainColor }}
                  className="text-md font-medium leading-none"
                >
                  CO<sub>2</sub>MARKET.IO
                </div>
              </div>
            </div>
          </div>
          {/* Main content will be placed here */}
          <div className="flex flex-col items-center justify-center z-10 w-full h-full">
            {children}
          </div>

          {/* Preview text */}
          {isPreview && (
            <div className="absolute inset-0 flex items-center justify-center z-0 pointer-events-none">
              <span className="text-[150px] uppercase text-white opacity-10 transform rotate-45">
                PREVIEW
              </span>
            </div>
          )}
        </div>
      );
    }

    return (
      <div
        style={{
          backgroundColor: secondaryColor,
          width: '100%',
          height: '100%'
        }}
      >
        <WithLogo {...props}>
          {props.children ? props.children : this.template(props)}
        </WithLogo>
      </div>
    );
  }
}
