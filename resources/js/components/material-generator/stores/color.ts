import { create } from 'zustand';

interface ColorPair {
  name: string;
  mainColor: string;
  secondaryColor: string;
}

interface ColorState {
  mainColor: string;
  secondaryColor: string;
  availableColorPairs: ColorPair[];
  selectedPairName: string | null;
  setColorPair: (pair: ColorPair) => void;
}

export const colorPairs: ColorPair[] = [
  { name: 'Default Green', secondaryColor: '#1A4D2E', mainColor: '#F5EFE6' },
  { name: 'Ocean Blue', secondaryColor: '#0077B6', mainColor: '#ADE8F4' },
  { name: 'Sunset Orange', secondaryColor: '#FF8C42', mainColor: '#FFF2E6' },
  { name: 'Forest Green', secondaryColor: '#2F4F4F', mainColor: '#90EE90' },
  { name: 'Royal Purple', secondaryColor: '#4B0082', mainColor: '#E6E6FA' },
];

export const useColorStore = create<ColorState>((set) => ({
  mainColor: colorPairs[0].mainColor, // Default to first pair
  secondaryColor: colorPairs[0].secondaryColor,
  availableColorPairs: colorPairs,
  selectedPairName: colorPairs[0].name,
  setColorPair: (pair) => set({
    mainColor: pair.mainColor,
    secondaryColor: pair.secondaryColor,
    selectedPairName: pair.name,
  }),
}));