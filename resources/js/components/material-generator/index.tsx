import React, { useState, useMemo, useRef } from 'react';
import { motion } from 'motion/react';
import { FiCloud, FiPlus, FiMinus } from 'react-icons/fi';
import Sidebar from './components/sidebar';
import { OffsetProcess, BaseTemplate } from './templates/base';
import * as templates from './templates';
import { useColorStore } from './stores/color';
import { useTemplateStore } from './stores/template';
import { Button } from '../ui/button';
import { Download } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '../ui/dropdown-menu';
import * as htmlToImage from 'html-to-image';
import { jsPDF } from 'jspdf';

interface DesignEditorProps {
  process: OffsetProcess;
}

type TemplateType = typeof templates;
type TemplateKey = keyof TemplateType;
type TemplateConstructor = new (
  process: OffsetProcess,
  translations?: any,
  mainColor?: string,
  secondaryColor?: string
) => BaseTemplate;

export default function DesignEditor(props: DesignEditorProps) {
  const [zoom, setZoom] = useState(100);
  const [fileName, setFileName] = useState('material-design');
  const [isGenerating, setIsGenerating] = useState(false);
  // Set default template size (square 600x600)
  const [templateSize, setTemplateSize] = useState({ width: 600, height: 600 });
  const { process } = props;
  const { mainColor, secondaryColor } = useColorStore();

  // Get selected template name from zustand store
  const selectedTemplateName = useTemplateStore(
    (state: { selectedTemplate: string }) => state.selectedTemplate
  );

  // Create a reference to the template div
  const templateRef = useRef<HTMLDivElement>(null);

  // Memoize the template instance to avoid recreating it on every render
  const templateInstance = useMemo(() => {
    // Select template based on store state
    const templateEntries = Object.entries(templates);
    const templateEntry = templateEntries.find(
      ([name]) => name === selectedTemplateName
    );

    // Fall back to MainTemplate if selected template not found
    const TemplateClass = templateEntry
      ? (templateEntry[1] as TemplateConstructor)
      : (templates.MainTemplate as TemplateConstructor);

    return new TemplateClass(process, mainColor, secondaryColor);
  }, [process, mainColor, secondaryColor, selectedTemplateName]); // Recreate when template changes

  // Function to download the template in different formats
  const downloadTemplate = async (format: 'png' | 'svg' | 'pdf') => {
    if (!templateRef.current) return;

    setIsGenerating(true);
    const node = templateRef.current;
    const scale = 3; // Higher resolution for exports

    // Calculate canvas dimensions based on template size
    const canvasWidth = templateSize.width * scale;
    const canvasHeight = templateSize.height * scale;

    try {
      switch (format) {
        case 'png':
          const pngDataUrl = await htmlToImage.toPng(node, {
            canvasWidth,
            canvasHeight,
            pixelRatio: 3
          });
          downloadFile(pngDataUrl, `${fileName}.png`);
          break;

        case 'svg':
          const svgDataUrl = await htmlToImage.toSvg(node, {
            canvasWidth,
            canvasHeight
          });
          downloadFile(svgDataUrl, `${fileName}.svg`);
          break;

        case 'pdf':
          const pdfDataUrl = await htmlToImage.toJpeg(node, {
            canvasWidth,
            canvasHeight,
            quality: 1
          });

          const nodeWidth = node.offsetWidth;
          const nodeHeight = node.offsetHeight;

          const pdfWidthMm = nodeWidth * 0.264583;
          const pdfHeightMm = nodeHeight * 0.264583;

          const pdf = new jsPDF({
            orientation: pdfWidthMm > pdfHeightMm ? 'landscape' : 'portrait',
            unit: 'mm',
            format: [pdfWidthMm, pdfHeightMm]
          });

          // Add image with compression options to reduce file size
          pdf.addImage(pdfDataUrl, 'PNG', 0, 0, pdfWidthMm, pdfHeightMm);

          pdf.save(`${fileName}.pdf`);
          break;
      }
    } catch (error) {
      console.error(`Error generating ${format}:`, error);
    } finally {
      setIsGenerating(false);
    }
  };

  // Helper function to trigger download
  const downloadFile = (dataUrl: string, filename: string) => {
    const link = document.createElement('a');
    link.href = dataUrl;
    link.download = filename;
    link.click();
  };

  // Get the store hook from the instance
  const templateStoreHook = templateInstance.getStoreHook();

  return (
    <div className="flex h-screen w-full bg-white text-green-900 border-2 border-ox-green-200 rounded-2xl overflow-hidden">
      {/* Pass the template instance to the Sidebar */}
      <Sidebar
        templateInstance={templateInstance}
        templateSize={templateSize}
        onSizeChange={setTemplateSize}
        process={process}
      />

      {/* Canvas Area */}
      <main className="flex-1 relative flex items-center justify-center p-8 overflow-hidden bg-gray-50">
        {/* Top‑right badge */}
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4 }}
          className="absolute top-6 right-6 flex items-center gap-2 rounded-md bg-ox-green-200 px-3 py-2 text-sm font-normal"
        >
          <FiCloud className="h-4 w-4" /> {process.compensationTarget}
        </motion.div>

        {/* Canvas container with checkerboard background */}
        <div className="relative rounded-lg shadow-inner overflow-hidden max-w-[90%] max-h-[90%]">
          {/* Checkerboard pattern */}
          <div
            className="absolute inset-0"
            style={{
              backgroundImage:
                'linear-gradient(45deg, #f0f0f0 25%, transparent 25%, transparent 75%, #f0f0f0 75%, #f0f0f0), linear-gradient(45deg, #f0f0f0 25%, transparent 25%, transparent 75%, #f0f0f0 75%, #f0f0f0)',
              backgroundSize: '20px 20px',
              backgroundPosition: '0 0, 10px 10px',
              opacity: 0.5
            }}
          ></div>

          {/* Design preview wrapper for centering */}
          <div
            className="flex items-center justify-center p-8 h-full w-full overflow-hidden"
            style={{ minHeight: '80vh', minWidth: '80vw' }}
          >
            {/* Size indicator */}
            <div className="absolute top-4 left-4 bg-white/80 backdrop-blur-sm px-3 py-1 rounded-md text-xs font-medium text-gray-700 shadow-sm z-20">
              {templateSize.width} × {templateSize.height}px
            </div>

            {/* Design preview container - Updated to fix centering issues */}
            <div className="absolute inset-0 flex items-center justify-center">
              <div
                className="transform-container"
                style={{
                  transform: `scale(${zoom / 100})`,
                  transition: 'transform 0.2s ease-out',
                  transformOrigin: 'center center'
                }}
              >
                <div
                  ref={templateRef}
                  className="cursor-move relative"
                  style={{
                    width: `${templateSize.width}px`,
                    height: `${templateSize.height}px`
                  }}
                >
                  {templateInstance.render({
                    logoProps: {
                      position: 'top-left', // Or get from state/props
                      isPreview: true
                    },
                    templateSize: templateSize // Pass template size to the render method
                  })}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Zoom controls */}
        <div className="absolute right-6 top-1/2 -translate-y-1/2 flex flex-col gap-2 bg-white/80 backdrop-blur-sm p-1 rounded-md shadow">
          <button
            className="h-8 w-8 flex items-center justify-center rounded hover:bg-gray-200 transition-colors"
            onClick={() => setZoom(z => Math.min(z + 25, 200))}
            aria-label="Zoom in"
          >
            <FiPlus className="h-4 w-4" />
          </button>
          <div className="text-center text-xs font-medium">{zoom}%</div>
          <button
            className="h-8 w-8 flex items-center justify-center rounded hover:bg-gray-200 transition-colors"
            onClick={() => setZoom(z => Math.max(z - 25, 25))}
            aria-label="Zoom out"
          >
            <FiMinus className="h-4 w-4" />
          </button>
        </div>

        {/* Bottom file‑name field & save */}
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.1 }}
          className="absolute bottom-6 left-1/2 -translate-x-1/2 flex gap-4 w-full max-w-md px-4"
        >
          <input
            placeholder="File name"
            value={fileName}
            onChange={e => setFileName(e.target.value)}
            className="flex-1 bg-white/60 px-3 py-2 rounded border border-gray-300 focus:ring-1 focus:ring-ox-green-400 focus:outline-none"
          />
          <button
            className="button-green whitespace-nowrap"
            onClick={() => {
              // Get the store hook from the template instance
              const storeHook = templateInstance.getStoreHook();

              // Gather all states from Zustand stores
              const designState = {
                colors: {
                  main: mainColor,
                  secondary: secondaryColor
                },
                template: {
                  name: selectedTemplateName,
                  size: templateSize
                },
                fileName: fileName,
                process: process,
                // Include template-specific state
                templateState: {
                  // Use the templateStoreHook that's already available in the scope
                  // which is properly typed
                  fields: templateStoreHook?.getState().fields || {},
                  visibleFields:
                    templateStoreHook?.getState().visibleFields || [],
                  fieldOrder: templateStoreHook?.getState().fieldOrder || [],
                  templateView:
                    templateStoreHook?.getState().templateView || 'extensive'
                }
              };

              // Output the state as JSON
              console.log(
                'Design state:',
                JSON.stringify(designState, null, 2)
              );

              // You could also save this JSON to a file or send to server
              // Example with WP AJAX endpoint:
              // const data = new FormData();
              // data.append('action', 'save_material_design');
              // data.append('design_state', JSON.stringify(designState));
              //
              // fetch(ajaxurl, {
              //   method: 'POST',
              //   body: data
              // }).then(response => response.json())
              //   .then(result => console.log('Design saved:', result))
              //   .catch(error => console.error('Error saving design:', error));
            }}
            disabled={isGenerating}
          >
            {isGenerating ? 'Saving...' : 'Save design'}
          </button>
          <DropdownMenu>
            <DropdownMenuTrigger>
              <Button>
                <Download className="h-4 w-4 mr-2" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="bg-white">
              <DropdownMenuLabel>Download</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onSelect={() => downloadTemplate('png')}>
                <span>PNG</span>
              </DropdownMenuItem>
              <DropdownMenuItem onSelect={() => downloadTemplate('svg')}>
                <span>SVG</span>
              </DropdownMenuItem>
              <DropdownMenuItem onSelect={() => downloadTemplate('pdf')}>
                <span>PDF</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </motion.div>
      </main>
    </div>
  );
}
