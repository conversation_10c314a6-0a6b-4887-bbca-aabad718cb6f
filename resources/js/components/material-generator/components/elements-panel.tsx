import React, { useState } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import {
  FiEdit2,
  FiTrash2,
  FiPlus,
  FiX,
  FiRotateCcw,
  FiMove
} from 'react-icons/fi';
import { useStore, StoreApi } from 'zustand';
import {
  BaseTemplate,
  Field,
  FieldType,
  TemplateState
} from '../templates/base';
import { BsThreeDotsVertical } from 'react-icons/bs';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
  useSortable
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

// Use TemplateState from base.tsx instead of redefining it here

type TemplateWithStore = BaseTemplate & {
  getStoreHook: () => StoreApi<TemplateState>;
  getAllFieldDefinitions: () => Field[];
  getFieldDefinition: (name: string) => Field | undefined;
};

interface ElementsPanelProps {
  isOpen: boolean;
  onClose: () => void;
  templateInstance: TemplateWithStore;
}

interface EditElementPanelProps {
  field: Field;
  store: StoreApi<TemplateState>;
  onClose: () => void;
}

const EditElementPanel: React.FC<EditElementPanelProps> = ({
  field,
  store,
  onClose
}) => {
  const { fields, setFieldValue, resetFieldValue } = useStore(store);
  const currentValue = fields[field.name];

  const handleInputChange = (
    event: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const value =
      event.target.type === 'checkbox'
        ? (event.target as HTMLInputElement).checked
        : event.target.value;
    setFieldValue(field.name, value);
  };

  const renderInput = () => {
    switch (field.type) {
      case 'text':
      case 'number':
      case 'date':
        return (
          <input
            type={field.type === 'number' ? 'number' : 'text'}
            value={currentValue || ''}
            onChange={handleInputChange}
            className="w-full p-2 border border-gray-300 rounded focus:ring-1 focus:ring-ox-green-400 focus:outline-none"
          />
        );
      case 'boolean':
        return (
          <input
            type="checkbox"
            checked={!!currentValue}
            onChange={handleInputChange}
            className="form-checkbox h-5 w-5 text-ox-green-600"
          />
        );
      case 'select':
        return (
          <select
            value={currentValue || ''}
            onChange={handleInputChange}
            className="w-full p-2 border border-gray-300 rounded focus:ring-1 focus:ring-ox-green-400 focus:outline-none"
          >
            {field.options?.map(option => (
              <option key={option} value={option}>
                {option}
              </option>
            ))}
          </select>
        );
      case 'image':
        return (
          <div>
            <input
              type="text"
              value={currentValue || ''}
              onChange={handleInputChange}
              placeholder="Image URL"
              className="w-full p-2 border border-gray-300 rounded focus:ring-1 focus:ring-ox-green-400 focus:outline-none mb-2"
            />
            {currentValue && (
              <img
                src={currentValue}
                alt="Preview"
                className="max-w-full h-auto mt-2 border rounded"
              />
            )}
          </div>
        );
      default:
        return <p className="text-xs text-gray-500">Unsupported field type</p>;
    }
  };

  return (
    <motion.div
      key={field.name}
      initial={{ x: '-100%', opacity: 0 }}
      animate={{ x: 0, opacity: 1 }}
      exit={{ x: '-100%', opacity: 0 }}
      transition={{ duration: 0.3, ease: 'easeInOut' }}
      className="absolute left-0 top-0 bottom-0 w-56 bg-white shadow-lg z-20 p-4 border-r border-ox-green-200 overflow-y-auto"
    >
      <div className="flex justify-between items-center mb-4">
        <h4 className="text-sm font-semibold text-green-900">{field.label}</h4>
        <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
          <FiX className="h-4 w-4" />
        </button>
      </div>
      <div className="space-y-3">
        {renderInput()}
        <button
          onClick={() => resetFieldValue(field.name)}
          className="flex items-center gap-1 text-xs text-gray-600 hover:text-ox-green-600 transition-colors"
        >
          <FiRotateCcw className="h-3 w-3" />
          Reset to Default
        </button>
      </div>
    </motion.div>
  );
};

interface SortableElementItemProps {
  id: string;
  field: Field;
  value: any;
  onEditClick: (field: Field) => void;
  onRemoveClick: (fieldName: string) => void;
}

const SortableElementItem: React.FC<SortableElementItemProps> = ({
  id,
  field,
  value,
  onEditClick,
  onRemoveClick
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging
  } = useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    zIndex: isDragging ? 10 : undefined,
    opacity: isDragging ? 0.8 : 1
  };

  let displayValue: React.ReactNode = String(value);
  if (typeof value === 'boolean') {
    displayValue = value ? 'Visible' : 'Hidden';
  } else if (value === null || value === undefined || value === '') {
    displayValue = <span className="text-gray-500 italic">Empty</span>;
  } else if (
    field.type === 'image' &&
    typeof value === 'string' &&
    value.length > 30
  ) {
    displayValue = value.substring(0, 27) + '...';
  }

  return (
    <div
      ref={setNodeRef}
      style={style}
      className="p-2 border border-gray-200 rounded bg-ox-green-200 mb-2 touch-none"
    >
      <div className="flex justify-between items-center">
        <button
          {...attributes}
          {...listeners}
          className="p-1 text-ox-green-600 cursor-grab active:cursor-grabbing mr-1"
          aria-label="Drag to reorder"
        >
          <FiMove className="h-4 w-4" />
        </button>
        <div className="flex-1 min-w-0 mr-1">
          <p className="text-xs font-normal text-ox-green-600 truncate">
            {field.label}
          </p>
          <p className="text-xs text-ox-green-600 truncate">{displayValue}</p>
        </div>
        <div className="flex flex-shrink-0 items-center">
          {field.canBeEdited && (
            <button
              onClick={() => onEditClick(field)}
              className="p-1 text-gray-700 hover:text-blue-600 transition-colors"
              aria-label={`Edit ${field.label}`}
            >
              <FiEdit2 className="h-3 w-3" />
            </button>
          )}
          {field.canBeRemoved && (
            <button
              onClick={() => onRemoveClick(field.name)}
              className="p-1 text-gray-700 hover:text-red-600 transition-colors"
              aria-label={`Remove ${field.label}`}
            >
              <FiTrash2 className="h-3 w-3" />
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

const ElementsPanel: React.FC<ElementsPanelProps> = ({
  isOpen,
  onClose,
  templateInstance
}) => {
  const store = templateInstance.getStoreHook();
  const {
    fields,
    visibleFields,
    fieldOrder,
    toggleFieldVisibility,
    setFieldOrder,
    resetFieldOrder,
    setTemplateView
  } = useStore(store);
  const allFields = templateInstance.getAllFieldDefinitions();
  const [editingField, setEditingField] = useState<Field | null>(null);

  const hiddenFields = allFields.filter(
    f => f.canBeRemoved && !visibleFields.includes(f.name)
  );

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 10
      }
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates
    })
  );

  const handleEditClick = (field: Field) => {
    setEditingField(field);
  };

  const handleCloseEditPanel = () => {
    setEditingField(null);
  };

  const handleAddElement = (fieldName: string) => {
    toggleFieldVisibility(fieldName);
  };

  const handleRemoveElement = (fieldName: string) => {
    toggleFieldVisibility(fieldName);
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      const currentFieldOrder = fieldOrder; // Use fieldOrder from useStore hook

      if (!Array.isArray(currentFieldOrder)) {
        console.error('fieldOrder is not an array in handleDragEnd');
        return;
      }

      const oldIndex = currentFieldOrder.indexOf(active.id as string);
      const newIndex = currentFieldOrder.indexOf(over.id as string);

      if (oldIndex !== -1 && newIndex !== -1) {
        const newOrder = arrayMove(currentFieldOrder, oldIndex, newIndex);

        // Debug logs to track the state change
        console.log('Drag end - Moving element', {
          element: active.id,
          from: oldIndex,
          to: newIndex,
          oldOrder: [...currentFieldOrder],
          newOrder: [...newOrder]
        });

        setFieldOrder(newOrder); // Use setFieldOrder from useStore hook

        // Verify the order was updated in the store
        setTimeout(() => {
          const updatedOrder = store.getState().fieldOrder;
          console.log('Order after update:', updatedOrder);
        }, 0);
      }
    }
  };

  return (
    <AnimatePresence>
      {isOpen && !editingField && (
        <motion.div
          initial={{ x: '-100%', opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          exit={{ x: '-100%', opacity: 0 }}
          transition={{ duration: 0.3, ease: 'easeInOut' }}
          className="absolute left-0 top-0 bottom-0 w-64 bg-white shadow-lg z-10 p-4 border-r border-ox-green-200 flex flex-col"
        >
          <div className="flex justify-between items-center mb-4 flex-shrink-0">
            <h3 className="text-sm font-semibold text-green-900">Elements</h3>
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700"
            >
              <FiX className="h-4 w-4" />
            </button>
          </div>
          <div className="flex-grow overflow-y-auto mb-4">
            <DndContext
              sensors={sensors}
              collisionDetection={closestCenter}
              onDragEnd={handleDragEnd}
            >
              {Array.isArray(fieldOrder) ? (
                <SortableContext
                  items={fieldOrder}
                  strategy={verticalListSortingStrategy}
                >
                  {fieldOrder.map(fieldName => {
                    const field = templateInstance.getFieldDefinition(
                      fieldName
                    );
                    if (!field) return null;
                    const value = fields[fieldName];

                    // Get translated label if available
                    const translatedField = {
                      ...field,
                      label:
                        store.getState().translatedLabels[fieldName] ||
                        field.label
                    };

                    return (
                      <SortableElementItem
                        key={fieldName}
                        id={fieldName}
                        field={translatedField}
                        value={value}
                        onEditClick={handleEditClick}
                        onRemoveClick={handleRemoveElement}
                      />
                    );
                  })}
                </SortableContext>
              ) : (
                <p className="text-xs text-gray-500">Loading elements...</p>
              )}
            </DndContext>
          </div>
          <div className="mb-4 flex-shrink-0">
            <button
              onClick={() => {
                resetFieldOrder();
                setTemplateView('extensive'); // Reset to extensive template view
              }}
              className="w-full flex items-center justify-center gap-1 text-xs text-gray-600 hover:text-ox-green-600 transition-colors p-1.5 rounded border border-gray-300 hover:bg-gray-100"
            >
              <FiRotateCcw className="h-3 w-3" />
              Reset Order
            </button>
          </div>
          {hiddenFields.length > 0 && (
            <div className="pt-4 border-t border-gray-200 flex-shrink-0">
              <h4 className="text-xs font-semibold mb-2 text-gray-700">
                Add Element
              </h4>
              <div className="space-y-1">
                {hiddenFields.map(field => (
                  <button
                    key={field.name}
                    onClick={() => handleAddElement(field.name)}
                    className="w-full flex items-center justify-between text-left p-1.5 rounded hover:bg-gray-100 text-xs text-green-700"
                  >
                    <span>{field.label}</span>
                    <FiPlus className="h-3 w-3" />
                  </button>
                ))}
              </div>
            </div>
          )}
        </motion.div>
      )}
      <AnimatePresence>
        {editingField && (
          <EditElementPanel
            field={editingField}
            store={store}
            onClose={handleCloseEditPanel}
          />
        )}
      </AnimatePresence>
    </AnimatePresence>
  );
};

export default ElementsPanel;
