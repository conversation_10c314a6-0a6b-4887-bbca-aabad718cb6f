import * as React from 'react';
import { SVGProps } from 'react';

interface Props extends SVGProps<SVGSVGElement> {
  color?: string;
}

const SvgComponent = (props: Props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    data-name="Layer 2"
    viewBox="0 0 1290.64 1290.62"
    {...props}
  >
    <path
      d="m1270.54 498.02-2.89-19.45c-14.36-53.58-80.72-84.83-107.16-130.71-27.37-47.46-21.64-120.37-59.53-158.2-38.43-38.36-111.54-32.4-158.27-59.38-46.72-26.98-78.12-93.27-130.56-107.38-51.71-13.91-111.99 27.51-166.78 27.55-52.95.03-113.2-41.81-166.78-27.45-53.57 14.36-84.83 80.72-130.71 107.16-47.46 27.36-120.37 21.63-158.19 59.52-38.37 38.43-32.41 111.55-59.38 158.27-26.97 46.72-93.27 78.12-107.38 130.56C9 530.21 50.42 590.49 50.45 645.28c.04 52.95-41.81 113.2-27.45 166.77 14.36 53.58 80.72 84.83 107.16 130.71 27.36 47.46 21.64 120.37 59.53 158.2 38.43 38.36 111.54 32.4 158.27 59.38 46.72 26.98 78.12 93.28 130.56 107.38 51.71 13.91 111.99-27.51 166.77-27.55 52.96-.03 113.2 41.81 166.78 27.45 53.57-14.36 84.83-80.71 130.71-107.16 47.46-27.36 120.37-21.63 158.19-59.52 38.36-38.43 32.41-111.55 59.38-158.27s93.27-78.12 107.38-130.56c13.91-51.7-27.51-111.98-27.54-166.77-.04-52.95 41.81-113.2 27.45-166.77"
      data-name="Layer 3"
      style={{
        fill: 'none',
        stroke: props.color || '#a5e6aa',
        strokeMiterlimit: 10,
        strokeWidth: 40
      }}
    />
  </svg>
);
export default SvgComponent;
