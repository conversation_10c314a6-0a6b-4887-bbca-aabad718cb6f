import React, { useEffect, useRef, useState, useCallback } from 'react';
import * as htmlToImage from 'html-to-image';
import { BaseTemplate } from '../templates/base';

interface TemplateImageRendererProps {
  instance: BaseTemplate;
  onImageRendered: (imageUrl: string) => void;
  onError: () => void;
}

// This component renders the template once and converts it to an image
export const TemplateImageRenderer: React.FC<TemplateImageRendererProps> = ({
  instance,
  onImageRendered,
  onError
}) => {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Render the template to an image once mounted, with a small delay
    const renderTemplateToImage = async () => {
      try {
        if (containerRef.current) {
          // Add small delay to ensure the template is fully rendered
          await new Promise(resolve => setTimeout(resolve, 100));

          const dataUrl = await htmlToImage.toPng(containerRef.current, {
            pixelRatio: 3, // Slightly higher quality
            cacheBust: true // Prevent caching issues
          });

          onImageRendered(dataUrl);
        }
      } catch (error) {
        console.error('Failed to render template to image:', error);
        onError();
      }
    };

    // Use setTimeout to ensure the component is fully rendered
    const timeoutId = setTimeout(renderTemplateToImage, 50);
    return () => clearTimeout(timeoutId);
  }, [instance, onImageRendered, onError]);

  // Render the template in a hidden container
  return (
    <div
      ref={containerRef}
      className="absolute left-0 top-0"
      style={{ width: '600px', height: '600px', pointerEvents: 'none' }}
    >
      {instance.render({
        logoProps: {
          position: 'top-left',
          isPreview: false
        },
        templateSize: { width: 600, height: 600 }
      })}
    </div>
  );
};

// This component handles loading state and showing the rendered image
interface TemplatePreviewProps {
  instance?: BaseTemplate;
  error?: boolean;
  templateName: string;
}

export const TemplatePreview: React.FC<TemplatePreviewProps> = React.memo(
  ({ instance, error, templateName }) => {
    const [imageUrl, setImageUrl] = useState<string | null>(null);
    const [hasError, setHasError] = useState<boolean>(!!error);
    const [isLoading, setIsLoading] = useState<boolean>(!!instance);

    const handleImageRendered = useCallback((url: string) => {
      setImageUrl(url);
      setIsLoading(false);
    }, []);

    const handleError = useCallback(() => {
      setHasError(true);
      setIsLoading(false);
    }, []);

    // If there's already an error or no instance, show appropriate message
    if (hasError || !instance) {
      return (
        <div className="flex items-center justify-center h-full w-full bg-red-50 text-red-500 text-xs p-2 text-center">
          {hasError ? 'Error loading template' : 'Template not found'}
        </div>
      );
    }

    return (
      <>
        {instance && isLoading && (
          <TemplateImageRenderer
            instance={instance}
            onImageRendered={handleImageRendered}
            onError={handleError}
          />
        )}

        {isLoading && (
          <div className="flex items-center justify-center h-full w-full bg-gray-50 text-gray-500 z-50">
            <div className="animate-pulse flex flex-col items-center">
              <div className="h-8 w-8 bg-gray-200 rounded-full mb-2"></div>
              <div className="h-2 w-16 bg-gray-200 rounded"></div>
            </div>
          </div>
        )}

        {imageUrl && !isLoading && (
          <img
            src={imageUrl}
            alt={`${templateName} preview`}
            className={`w-full h-full object-contain transition-opacity duration-300 ${
              isLoading ? 'opacity-0' : 'opacity-100'
            }`}
          />
        )}

        {/* Render the template off-screen to generate the image */}
      </>
    );
  }
);
