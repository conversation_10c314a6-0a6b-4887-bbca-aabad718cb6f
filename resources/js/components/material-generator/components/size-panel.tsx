import React, { useState, useEffect } from 'react';
import { FiX, FiCheck } from 'react-icons/fi';
import { motion } from 'motion/react';
import { useColorStore } from '../stores/color';

// Define template size presets
export interface TemplateSize {
  name: string;
  width: number;
  height: number;
  label: string;
  description?: string;
}

export const TEMPLATE_SIZES: TemplateSize[] = [
  {
    name: 'square',
    width: 600,
    height: 600,
    label: 'Square',
    description: '600×600px'
  },
  {
    name: 'instagram-post',
    width: 1080,
    height: 1080,
    label: 'Instagram Post',
    description: '1080×1080px'
  },
  {
    name: 'instagram-story',
    width: 1080,
    height: 1920,
    label: 'Instagram Story',
    description: '1080×1920px'
  },
  {
    name: 'facebook-post',
    width: 1200,
    height: 630,
    label: 'Facebook Post',
    description: '1200×630px'
  },
  {
    name: 'twitter-post',
    width: 1200,
    height: 675,
    label: 'Twitter Post',
    description: '1200×675px'
  },
  {
    name: 'linkedin-post',
    width: 1104,
    height: 736,
    label: 'LinkedIn Post',
    description: '1104×736px'
  },
  {
    name: 'a4-portrait',
    width: 816,
    height: 1056,
    label: 'A4 Portrait',
    description: '816×1056px'
  },
  {
    name: 'a4-landscape',
    width: 1056,
    height: 816,
    label: 'A4 Landscape',
    description: '1056×816px'
  }
];

interface SizePanelProps {
  isOpen: boolean;
  onClose: () => void;
  currentSize: { width: number; height: number };
  onSizeChange: (size: { width: number; height: number }) => void;
}

const SizePanel: React.FC<SizePanelProps> = ({
  isOpen,
  onClose,
  currentSize,
  onSizeChange
}) => {
  // State for custom width and height inputs
  const [customWidth, setCustomWidth] = useState<number>(currentSize.width);
  const [customHeight, setCustomHeight] = useState<number>(currentSize.height);
  const [isCustom, setIsCustom] = useState<boolean>(false);

  // Track current selection
  const [selectedSize, setSelectedSize] = useState<string>('square');

  // Calculate aspect ratio for display
  const getAspectRatio = (width: number, height: number): string => {
    if (!width || !height) return '';

    // Calculate greatest common divisor
    const gcd = (a: number, b: number): number => {
      return b ? gcd(b, a % b) : a;
    };

    const divisor = gcd(width, height);
    const ratioWidth = width / divisor;
    const ratioHeight = height / divisor;

    // For common ratios, show the common name
    if (ratioWidth === 1 && ratioHeight === 1) return '1:1';
    if (ratioWidth === 16 && ratioHeight === 9) return '16:9';
    if (ratioWidth === 9 && ratioHeight === 16) return '9:16';
    if (ratioWidth === 4 && ratioHeight === 3) return '4:3';
    if (ratioWidth === 3 && ratioHeight === 2) return '3:2';

    // For "simple" ratios where one side is under 25, show as ratio
    if (ratioWidth < 25 && ratioHeight < 25) {
      return `${Math.round(ratioWidth)}:${Math.round(ratioHeight)}`;
    }

    // Otherwise show as decimal
    return (width / height).toFixed(2);
  };

  // Get color from store for selected state
  const { mainColor } = useColorStore();

  useEffect(() => {
    // Find if current size matches any preset
    const matchingPreset = TEMPLATE_SIZES.find(
      size =>
        size.width === currentSize.width && size.height === currentSize.height
    );

    if (matchingPreset) {
      setSelectedSize(matchingPreset.name);
      setIsCustom(false);
    } else {
      setIsCustom(true);
      setCustomWidth(currentSize.width);
      setCustomHeight(currentSize.height);
    }
  }, [currentSize]);

  // Handle preset selection
  const handleSelectSize = (size: TemplateSize) => {
    setSelectedSize(size.name);
    setIsCustom(false);
    onSizeChange({ width: size.width, height: size.height });
  };

  // Handle custom size submission
  const handleCustomSizeSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (customWidth > 0 && customHeight > 0) {
      onSizeChange({ width: customWidth, height: customHeight });
      setIsCustom(true);
    }
  };

  if (!isOpen) return null;

  return (
    <motion.div
      initial={{ x: '-100%', opacity: 0 }}
      animate={{ x: 0, opacity: 1 }}
      exit={{ x: '-100%', opacity: 0 }}
      transition={{ duration: 0.3, ease: 'easeInOut' }}
      className="absolute left-0 top-0 bottom-0 w-56 md:w-64 bg-white shadow-lg z-40 p-4 border-r border-ox-green-200 overflow-y-auto"
    >
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-sm font-semibold text-green-900">Template Size</h3>
        <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
          <FiX className="h-4 w-4" />
        </button>
      </div>

      {/* Presets */}
      <div className="mb-6">
        <h4 className="text-xs font-medium text-gray-700 mb-2">Preset Sizes</h4>
        <div className="grid grid-cols-2 gap-2">
          {TEMPLATE_SIZES.map(size => {
            // Calculate aspect ratio for preview
            const aspectRatio = size.width / size.height;
            const previewHeight = aspectRatio < 1 ? 60 : 30;
            const previewWidth = previewHeight * aspectRatio;

            return (
              <button
                key={size.name}
                onClick={() => handleSelectSize(size)}
                className={`
                  flex flex-col items-center justify-center p-2 rounded-md border transition-colors h-[120px]
                  ${
                    selectedSize === size.name && !isCustom
                      ? 'bg-ox-green-200 border-ox-green-300'
                      : 'bg-white border-gray-200 hover:bg-gray-50'
                  }
                `}
              >
                {/* Size preview */}
                <div
                  className="mb-2 border border-gray-300 bg-white flex items-center justify-center"
                  style={{
                    width: `${previewWidth}px`,
                    height: `${previewHeight}px`,
                    maxWidth: '100%'
                  }}
                >
                  {selectedSize === size.name && !isCustom && (
                    <FiCheck style={{ color: mainColor }} className="h-4 w-4" />
                  )}
                </div>
                <div className="text-center">
                  <span className="font-medium text-xs block">
                    {size.label}
                  </span>
                  <span className="text-xs text-gray-500 block">
                    {size.description}
                  </span>
                </div>
              </button>
            );
          })}
        </div>
      </div>

      {/* Custom size */}
      <div>
        <h4 className="text-xs font-medium text-gray-700 mb-2">Custom Size</h4>
        <form onSubmit={handleCustomSizeSubmit} className="space-y-3">
          <div
            className={`p-3 rounded-md border ${
              isCustom
                ? 'bg-ox-green-200 border-ox-green-300'
                : 'bg-white border-gray-200'
            }`}
          >
            <div className="flex items-center justify-between mb-2">
              <span className="font-medium text-sm">Custom Dimensions</span>
              {isCustom && (
                <FiCheck style={{ color: mainColor }} className="h-4 w-4" />
              )}
            </div>

            {/* Show a custom size preview */}
            {isCustom && (
              <div className="flex flex-col items-center mb-3">
                <div
                  className="bg-white border border-gray-300 flex items-center justify-center"
                  style={{
                    width: `${Math.min(80, customWidth / 8)}px`,
                    height: `${Math.min(80, customHeight / 8)}px`
                  }}
                >
                  <span className="text-[8px] text-gray-500">
                    {customWidth}×{customHeight}
                  </span>
                </div>
                {/* Show calculated aspect ratio */}
                <div className="mt-1 text-xs text-gray-500">
                  Aspect ratio: {getAspectRatio(customWidth, customHeight)}
                </div>
              </div>
            )}

            <div className="grid grid-cols-2 gap-2">
              <div>
                <label
                  htmlFor="width"
                  className="block text-xs text-gray-500 mb-1"
                >
                  Width (px)
                </label>
                <input
                  id="width"
                  type="number"
                  min="100"
                  max="3000"
                  value={customWidth}
                  onChange={e => setCustomWidth(parseInt(e.target.value) || 0)}
                  className="w-full text-xs p-2 border border-gray-300 rounded"
                />
              </div>
              <div>
                <label
                  htmlFor="height"
                  className="block text-xs text-gray-500 mb-1"
                >
                  Height (px)
                </label>
                <input
                  id="height"
                  type="number"
                  min="100"
                  max="3000"
                  value={customHeight}
                  onChange={e => setCustomHeight(parseInt(e.target.value) || 0)}
                  className="w-full text-xs p-2 border border-gray-300 rounded"
                />
              </div>
            </div>

            {/* Common aspect ratios */}
            <div className="mt-3">
              <label className="block text-xs font-medium text-gray-700 mb-2">
                Common Aspect Ratios
              </label>
              <div className="grid grid-cols-2 gap-2">
                <button
                  type="button"
                  onClick={() => {
                    setCustomWidth(1000);
                    setCustomHeight(1000);
                  }}
                  className="px-3 py-2 text-xs bg-white border border-gray-200 hover:bg-gray-50 rounded-md flex items-center justify-center transition-colors"
                >
                  <span className="font-medium">1:1</span>
                </button>
                <button
                  type="button"
                  onClick={() => {
                    setCustomWidth(1600);
                    setCustomHeight(900);
                  }}
                  className="px-3 py-2 text-xs bg-white border border-gray-200 hover:bg-gray-50 rounded-md flex items-center justify-center transition-colors"
                >
                  <span className="font-medium">16:9</span>
                </button>
                <button
                  type="button"
                  onClick={() => {
                    setCustomWidth(900);
                    setCustomHeight(1600);
                  }}
                  className="px-3 py-2 text-xs bg-white border border-gray-200 hover:bg-gray-50 rounded-md flex items-center justify-center transition-colors"
                >
                  <span className="font-medium">9:16</span>
                </button>
                <button
                  type="button"
                  onClick={() => {
                    setCustomWidth(1200);
                    setCustomHeight(900);
                  }}
                  className="px-3 py-2 text-xs bg-white border border-gray-200 hover:bg-gray-50 rounded-md flex items-center justify-center transition-colors"
                >
                  <span className="font-medium">4:3</span>
                </button>
              </div>
            </div>
          </div>

          <button
            type="submit"
            style={{ backgroundColor: mainColor }}
            className="w-full py-2 text-white text-sm rounded-md hover:opacity-90 transition-opacity flex items-center justify-center gap-2"
          >
            <FiCheck className="h-4 w-4" /> Apply Custom Size
          </button>
        </form>
      </div>
    </motion.div>
  );
};

export default SizePanel;
