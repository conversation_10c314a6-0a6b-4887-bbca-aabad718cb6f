import React, { useState } from 'react';
import { motion } from 'motion/react';
import { FiX, FiRotateCcw, FiGlobe } from 'react-icons/fi';
import { BaseTemplate } from '../templates/base';
import { useStore, StoreApi } from 'zustand';

interface TranslatePanelProps {
  isOpen: boolean;
  onClose: () => void;
  templateInstance: BaseTemplate;
}

interface LanguageOption {
  code: string;
  name: string;
}

const languages: LanguageOption[] = [
  { code: 'en', name: 'English' },
  { code: 'fi', name: 'Finnish' },
  { code: 'sv', name: 'Swedish' },
  { code: 'de', name: 'German' },
  { code: 'fr', name: 'French' },
  { code: 'es', name: 'Spanish' }
];

const TranslatePanel: React.FC<TranslatePanelProps> = ({
  isOpen,
  onClose,
  templateInstance
}) => {
  const [selectedLanguage, setSelectedLanguage] = useState<string>('en');
  const [isTranslating, setIsTranslating] = useState(false);
  const [translationError, setTranslationError] = useState<string | null>(null);

  const store = templateInstance.getStoreHook();
  const { currentLanguage } = useStore(store);

  const handleTranslate = async () => {
    if (selectedLanguage === currentLanguage) {
      setTranslationError('Already translated to this language');
      return;
    }

    try {
      setIsTranslating(true);
      setTranslationError(null);
      await templateInstance.translate(selectedLanguage);
    } catch (error) {
      console.error('Translation error:', error);
      setTranslationError(
        `Translation failed: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`
      );
    } finally {
      setIsTranslating(false);
    }
  };

  const handleReset = () => {
    templateInstance.resetTranslation();
  };

  if (!isOpen) return null;

  return (
    <motion.div
      initial={{ x: '-100%', opacity: 0 }}
      animate={{ x: 0, opacity: 1 }}
      exit={{ x: '-100%', opacity: 0 }}
      transition={{ duration: 0.3, ease: 'easeInOut' }}
      className="absolute left-0 top-0 bottom-0 w-64 bg-white shadow-lg z-10 p-4 border-r border-ox-green-200"
    >
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-sm font-semibold text-green-900">
          Translate Template
        </h3>
        <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
          <FiX className="h-4 w-4" />
        </button>
      </div>

      <div className="space-y-4">
        <div>
          <label
            htmlFor="language-select"
            className="block text-sm font-medium text-gray-700 mb-1"
          >
            Select Language
          </label>
          <div className="relative">
            <select
              id="language-select"
              value={selectedLanguage}
              onChange={e => setSelectedLanguage(e.target.value)}
              className="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-ox-green-500 focus:border-ox-green-500 sm:text-sm rounded-md"
            >
              {languages.map(lang => (
                <option key={lang.code} value={lang.code}>
                  {lang.name}
                </option>
              ))}
            </select>
          </div>
        </div>

        {translationError && (
          <div className="p-2 bg-red-50 text-red-700 text-xs rounded-md border border-red-100">
            {translationError}
          </div>
        )}

        <div className="flex justify-between mt-4 space-x-2">
          <button
            onClick={handleTranslate}
            disabled={isTranslating || selectedLanguage === currentLanguage}
            className={`flex-1 flex items-center justify-center gap-1 px-3 py-2 rounded-md text-white bg-ox-green-500 text-sm font-medium ${
              isTranslating || selectedLanguage === currentLanguage
                ? 'opacity-70 cursor-not-allowed'
                : 'hover:bg-ox-green-600'
            }`}
          >
            {isTranslating ? (
              <>
                <svg
                  className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                Translating...
              </>
            ) : (
              'Translate'
            )}
          </button>

          <button
            onClick={handleReset}
            disabled={isTranslating || currentLanguage === 'en'}
            className={`flex items-center justify-center gap-1 px-3 py-2 rounded-md border text-sm font-medium ${
              isTranslating || currentLanguage === 'en'
                ? 'opacity-70 cursor-not-allowed border-gray-300 text-gray-500'
                : 'border-gray-300 text-gray-700 hover:bg-gray-50'
            }`}
          >
            <FiRotateCcw className="h-3 w-3" />
            Reset
          </button>
        </div>

        {currentLanguage !== 'en' && (
          <div className="mt-2 p-2 bg-blue-50 text-xs rounded-md border border-blue-100">
            <p className="font-medium text-blue-700">
              Currently translated to:{' '}
              {languages.find(l => l.code === currentLanguage)?.name ||
                currentLanguage}
            </p>
          </div>
        )}
      </div>
    </motion.div>
  );
};

export default TranslatePanel;
