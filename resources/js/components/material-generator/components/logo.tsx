import * as React from 'react';
import { SVGProps } from 'react';

interface Props extends SVGProps<SVGSVGElement> {
  color?: string;
}
const SvgComponent = (props: Props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    id="Layer_2"
    data-name="Layer 2"
    viewBox="0 0 135.27 208.73"
    {...props}
  >
    <defs>
      <style>{`.cls-1{fill:${props.color || '#a5e6aa}'}`}</style>
    </defs>
    <g id="Layer_2-2" data-name="Layer 2">
      <g id="c">
        <path
          d="M55.63 0H0l74.28 128.67c5.59 9.7 15.4 14.55 25.18 14.55s19.58-4.85 25.18-14.55l10.63-18.42-55.63-96.36A27.777 27.777 0 0 0 55.6 0h.03ZM108 152.44c-3.82 1.88-8.59 4.24-22.21 11.32-12.01 6.23-23.9 9.38-35.35 9.38-7.37 0-14.39-1.33-20.86-3.9l-14.02 24.3c9.7 9.83 21.86 15.19 34.35 15.19 16.8 0 34.13-9.73 46.64-31.38 6.1-10.57 11.24-19.45 15.56-26.95-1.48.74-2.76 1.38-4.11 2.04Z"
          className="cls-1"
        />
        <path
          d="m66.58 133.14-17.46-30.27L4.73 125.9c4.82 22.53 22.18 38.32 45.72 38.32 9.54 0 20.09-2.6 31.25-8.37 3.6-1.88 6.6-3.42 9.12-4.72-10.12-2.33-18.84-8.69-24.22-17.99h-.02Z"
          className="cls-1"
        />
      </g>
    </g>
  </svg>
);
export default SvgComponent;
