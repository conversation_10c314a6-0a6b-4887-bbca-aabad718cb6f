import React from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { colorPairs, useColorStore } from '../stores/color';

interface ColorPickerPanelProps {
  isOpen: boolean;
  onClose: () => void;
}

const ColorPickerPanel: React.FC<ColorPickerPanelProps> = ({
  isOpen,
  onClose
}) => {
  const { setColorPair, selectedPairName } = useColorStore();

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ x: '-100%', opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          exit={{ x: '-100%', opacity: 0 }}
          transition={{ duration: 0.3, ease: 'easeInOut' }}
          className="absolute left-0 top-0 bottom-0 w-48 bg-white shadow-lg z-10 p-4 border-r border-ox-green-200 overflow-y-auto"
        >
          <h3 className="text-sm font-semibold mb-4 text-green-900">
            Color Pairs
          </h3>
          <div className="space-y-2">
            {colorPairs.map(pair => (
              <button
                key={pair.name}
                onClick={() => {
                  setColorPair(pair);
                  // Optionally close panel on selection: onClose();
                }}
                className={`w-full p-2 rounded border text-left text-xs transition-colors duration-150 ${
                  selectedPairName === pair.name
                    ? 'bg-ox-green-200 border-ox-green-400 ring-1 ring-ox-green-400'
                    : 'bg-white border-gray-200 hover:bg-gray-100'
                }`}
              >
                <div className="flex items-center gap-2 mb-1">
                  <div
                    className="w-4 h-4 rounded-full border border-gray-300"
                    style={{ backgroundColor: pair.mainColor }}
                  ></div>
                  <div
                    className="w-4 h-4 rounded-full border border-gray-300"
                    style={{ backgroundColor: pair.secondaryColor }}
                  ></div>
                </div>
                <span className="font-medium text-green-900">{pair.name}</span>
              </button>
            ))}
          </div>
          {/* Optional close button */}
          {/* <button onClick={onClose} className="mt-4 text-xs text-gray-500 hover:text-gray-700">Close</button> */}
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default ColorPickerPanel;
