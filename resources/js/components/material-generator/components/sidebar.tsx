import React, { useState } from 'react';
import { FiGrid, FiX, FiGlobe } from 'react-icons/fi';
import { HiOutlineSquares2X2 } from 'react-icons/hi2';
import { TbSquareRounded } from 'react-icons/tb';
import { MdPalette } from 'react-icons/md';
import { motion } from 'motion/react';
import { CgColorBucket } from 'react-icons/cg';
import { FiLayout } from 'react-icons/fi';
import ColorPickerPanel from './color-picker-panel';
import ElementsPanel from './elements-panel';
import SizePanel from './size-panel';
import ThemePanel from './theme-panel';
import TemplatePanel from './template-panel';
import TranslatePanel from './translate-panel';
import { BaseTemplate, OffsetProcess } from '../templates/base';

interface SidebarProps {
  templateInstance: BaseTemplate & {
    getStoreHook: () => any;
    getAllFieldDefinitions: () => any[];
    getFieldDefinition: (name: string) => any | undefined;
  };
  templateSize: { width: number; height: number };
  onSizeChange: (size: { width: number; height: number }) => void;
  process: OffsetProcess; // Add process prop for ThemePanel
}

const Sidebar: React.FC<SidebarProps> = ({
  templateInstance,
  templateSize,
  onSizeChange,
  process
}) => {
  const [activePanel, setActivePanel] = useState<string | null>(null);

  const togglePanel = (panelName: string) => {
    setActivePanel(activePanel === panelName ? null : panelName);
  };

  return (
    <div className="relative flex h-full">
      <aside className="w-20 bg-ox-green-400 py-6 flex flex-col items-center gap-4 shadow-xl h-full z-50 relative flex-shrink-0">
        <NavItem
          icon={MdPalette}
          label="Theme"
          onClick={() => togglePanel('theme')}
          isActive={activePanel === 'theme'}
        />
        <NavItem
          icon={TbSquareRounded}
          label="Size"
          onClick={() => togglePanel('size')}
          isActive={activePanel === 'size'}
        />
        <NavItem
          icon={FiLayout}
          label="Template"
          onClick={() => togglePanel('template')}
          isActive={activePanel === 'template'}
        />
        <NavItem
          icon={HiOutlineSquares2X2}
          label="Elements"
          onClick={() => togglePanel('elements')}
          isActive={activePanel === 'elements'}
        />
        <NavItem
          icon={CgColorBucket}
          label="Color"
          onClick={() => togglePanel('color')}
          isActive={activePanel === 'color'}
        />
        <NavItem
          icon={FiGlobe}
          label="Translate"
          onClick={() => togglePanel('translate')}
          isActive={activePanel === 'translate'}
        />
      </aside>

      <div className="relative h-full z-50">
        <ColorPickerPanel
          isOpen={activePanel === 'color'}
          onClose={() => setActivePanel(null)}
        />
        <ElementsPanel
          isOpen={activePanel === 'elements'}
          onClose={() => setActivePanel(null)}
          templateInstance={templateInstance}
        />
        <ThemePanel
          isOpen={activePanel === 'theme'}
          onClose={() => setActivePanel(null)}
          process={process}
          title="Design Theme"
        />
        <SizePanel
          isOpen={activePanel === 'size'}
          onClose={() => setActivePanel(null)}
          currentSize={templateSize}
          onSizeChange={onSizeChange}
        />
        <TemplatePanel
          isOpen={activePanel === 'template'}
          onClose={() => setActivePanel(null)}
          templateInstance={templateInstance}
          title="Template View"
        />
        <TranslatePanel
          isOpen={activePanel === 'translate'}
          onClose={() => setActivePanel(null)}
          templateInstance={templateInstance}
        />
      </div>
    </div>
  );
};

export default Sidebar;

const PlaceholderPanel: React.FC<{ name: string; onClose: () => void }> = ({
  name,
  onClose
}) => (
  <motion.div
    initial={{ x: '-100%', opacity: 0 }}
    animate={{ x: 0, opacity: 1 }}
    exit={{ x: '-100%', opacity: 0 }}
    transition={{ duration: 0.3, ease: 'easeInOut' }}
    className="absolute left-0 top-0 bottom-0 w-48 bg-white shadow-lg z-40 p-4 border-r border-ox-green-200" // Changed left-20 to left-0
  >
    <div className="flex justify-between items-center mb-4">
      <h3 className="text-sm font-semibold text-green-900">{name} Panel</h3>
      <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
        <FiX className="h-4 w-4" />
      </button>
    </div>
    <p className="text-xs text-gray-500">Content for {name} goes here.</p>
  </motion.div>
);

interface NavItemProps {
  icon: React.ComponentType<{ className?: string }>;
  label: string;
  onClick?: () => void;
  isActive?: boolean;
}

function NavItem({
  icon: Icon,
  label,
  onClick,
  isActive = false
}: NavItemProps) {
  return (
    <motion.button
      whileTap={{ scale: 0.9 }}
      className={`flex flex-col items-center gap-1 text-xs font-medium ${
        isActive
          ? 'bg-white text-ox-green-600'
          : 'text-ox-green-600 hover:text-ox-green-100'
      } focus:outline-none w-full text-center py-2 ${
        isActive ? 'shadow-sm' : ''
      }`}
      onClick={onClick}
    >
      <Icon className="h-6 w-6" />
      {label}
    </motion.button>
  );
}
