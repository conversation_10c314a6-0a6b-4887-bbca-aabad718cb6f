import React, { useEffect } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { FiX, FiCheck } from 'react-icons/fi';
import { useStore } from 'zustand';
import {
  BaseTemplate,
  Field,
  TemplateState,
  TemplateView
} from '../templates/base';

interface TemplatePanelProps {
  isOpen: boolean;
  onClose: () => void;
  templateInstance: BaseTemplate & {
    getStoreHook: () => any;
    getAllFieldDefinitions: () => Field[];
  };
  title?: string;
}

const TemplatePanel: React.FC<TemplatePanelProps> = ({
  isOpen,
  onClose,
  templateInstance,
  title = 'Template Selection'
}) => {
  const store = templateInstance.getStoreHook();
  const { visibleFields, templateView, setTemplateView } = useStore(store);

  // Fields categorized by template type
  const allFields = templateInstance.getAllFieldDefinitions();
  const simpleFields = allFields
    .filter(field => field.template === 'simple')
    .map(field => field.name);
  const normalFields = allFields
    .filter(field => field.template === 'simple' || field.template === 'normal')
    .map(field => field.name);
  const extensiveFields = allFields.map(field => field.name); // Apply template by updating visible fields
  const applyTemplate = (view: TemplateView) => {
    if (view === 'custom') {
      return; // Don't change anything for custom
    }

    // Log the current fields and the fields that should be displayed for this view
    console.log('Applying template view:', view);
    console.log('Simple fields:', simpleFields);
    console.log('Normal fields:', normalFields);
    console.log('All fields:', extensiveFields);

    // Use the setTemplateView function from the store
    setTemplateView(view);

    // Ensure the panel stays open by not auto-closing
    // We also could close it with: onClose();

    // After a short delay, log the updated visible fields
    setTimeout(() => {
      console.log('Updated visible fields:', store.getState().visibleFields);
      console.log('Updated field order:', store.getState().fieldOrder);
    }, 100);
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ x: '-100%', opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          exit={{ x: '-100%', opacity: 0 }}
          transition={{ duration: 0.3, ease: 'easeInOut' }}
          className="absolute left-0 top-0 bottom-0 w-56 bg-white shadow-lg z-10 p-4 border-r border-ox-green-200"
        >
          <div className="flex justify-between items-center mb-6">
            <h3 className="text-sm font-semibold text-green-900">{title}</h3>
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700"
            >
              <FiX className="h-4 w-4" />
            </button>
          </div>

          <div className="space-y-4">
            <h4 className="text-xs font-medium text-gray-700">
              Select Template View
            </h4>

            <TemplateOption
              name="Simple"
              description="Basic fields only"
              isActive={templateView === 'simple'}
              onClick={() => applyTemplate('simple')}
              fieldsCount={simpleFields.length}
            />

            <TemplateOption
              name="Normal"
              description="Standard set of fields"
              isActive={templateView === 'normal'}
              onClick={() => applyTemplate('normal')}
              fieldsCount={normalFields.length}
            />

            <TemplateOption
              name="Extensive"
              description="All available fields"
              isActive={templateView === 'extensive'}
              onClick={() => applyTemplate('extensive')}
              fieldsCount={extensiveFields.length}
            />

            {templateView === 'custom' && (
              <TemplateOption
                name="Custom"
                description="Custom field selection"
                isActive={true}
                isCustom={true}
                fieldsCount={visibleFields.length}
              />
            )}
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

interface TemplateOptionProps {
  name: string;
  description: string;
  isActive: boolean;
  onClick?: () => void;
  isCustom?: boolean;
  fieldsCount: number;
}

const TemplateOption: React.FC<TemplateOptionProps> = ({
  name,
  description,
  isActive,
  onClick,
  isCustom = false,
  fieldsCount
}) => {
  return (
    <div
      className={`
        p-3 rounded-md border cursor-pointer transition-colors
        ${
          isActive
            ? 'border-ox-green-400 bg-ox-green-50'
            : 'border-gray-200 hover:border-ox-green-300 hover:bg-ox-green-50/50'
        }
      `}
      onClick={isCustom ? undefined : onClick}
    >
      <div className="flex justify-between items-center">
        <div>
          <div className="font-medium text-sm text-ox-green-800">{name}</div>
        </div>
        {isActive && (
          <div className="h-6 w-6 bg-ox-green-400 rounded-full flex items-center justify-center">
            <FiCheck className="h-4 w-4 text-white" />
          </div>
        )}
      </div>
    </div>
  );
};

export default TemplatePanel;
