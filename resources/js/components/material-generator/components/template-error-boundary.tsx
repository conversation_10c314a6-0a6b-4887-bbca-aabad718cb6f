import React, { Component, ErrorInfo, ReactNode } from 'react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
}

class TemplateErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(): State {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Template Error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        this.props.fallback || (
          <div className="flex items-center justify-center h-full w-full bg-red-50 p-4 rounded">
            <div className="text-center">
              <p className="text-red-500 text-sm font-medium mb-2">
                Template Error
              </p>
              <p className="text-xs text-red-400">
                Could not render template preview
              </p>
            </div>
          </div>
        )
      );
    }

    return this.props.children;
  }
}

export default TemplateErrorBoundary;
