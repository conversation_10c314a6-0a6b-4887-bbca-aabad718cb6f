import React, { useMemo, Suspense } from 'react';
import { Fi<PERSON>, FiCheck } from 'react-icons/fi';
import { motion, AnimatePresence } from 'motion/react';
import * as templates from '../templates';
import { useTemplateStore } from '../stores/template';
import { useColorStore } from '../stores/color';
import { OffsetProcess, BaseTemplate } from '../templates/base';
import { TemplatePreview } from './template-preview-renderer';

interface ThemePanelProps {
  isOpen: boolean;
  onClose: () => void;
  process: OffsetProcess;
  title?: string;
}

const ThemePanel: React.FC<ThemePanelProps> = ({
  isOpen,
  onClose,
  process,
  title = 'Template Gallery'
}) => {
  const { selectedTemplate, setSelectedTemplate } = useTemplateStore();
  const { mainColor } = useColorStore();

  // Force re-render when needed
  const [refreshKey, setRefreshKey] = React.useState<number>(0);

  // Create dummy data for templates
  const dummyProcess = useMemo<OffsetProcess>(
    () => ({
      company: 'Example Ltd.',
      ghgScopes: [1, 2, 3],
      description: '',
      compensationTarget: 'All operations, 2025',
      date: '2025-05-01',
      country: 'Finland',
      verified: true,
      verifiedBy: 'Verification Agency',
      carbonFootprint: 100,
      carbonOffset: 150,
      logoUrl:
        'http://[::1]:5173/app/themes/sage/public/build/resources/images/logo.webp'
    }),
    []
  );

  // Track loading state for the panel
  const [isLoadingTemplates, setIsLoadingTemplates] = React.useState(false);

  // Get templates with instances when the panel is open - but only process them if isOpen is true
  const availableTemplates = useMemo(() => {
    if (!isOpen) return [];

    // Start loading
    setIsLoadingTemplates(true);

    const templates_arr = Object.entries(templates)
      .filter(([name]) => name !== 'BaseTemplate')
      .map(([name, TemplateConstructor]) => {
        try {
          // Create template instance with dummy data
          const instance = new TemplateConstructor(dummyProcess, undefined);
          return { name, instance };
        } catch (error) {
          console.error(`Error creating template ${name}:`, error);
          return { name, error: true };
        }
      });

    // Done loading
    setIsLoadingTemplates(false);
    return templates_arr;
  }, [isOpen, refreshKey, dummyProcess, mainColor]);

  // Function to select a template
  const handleSelectTemplate = (templateName: string) => {
    setSelectedTemplate(templateName);
    // Close the panel after selection for better UX
    setTimeout(() => onClose(), 300);
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ x: '-100%', opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          exit={{ x: '-100%', opacity: 0 }}
          transition={{ duration: 0.3, ease: 'easeInOut' }}
          className="absolute left-0 top-0 bottom-0 w-full sm:w-72 max-w-[90vw] bg-white shadow-lg z-40 p-4 border-r border-ox-green-200 overflow-y-auto"
        >
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-sm font-semibold text-green-900">{title}</h3>
            <div className="flex items-center gap-2">
              <button
                onClick={() => setRefreshKey(prevKey => prevKey + 1)}
                className="text-gray-500 hover:text-gray-700 p-1 rounded hover:bg-gray-100"
                title="Refresh templates"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-3.5 w-3.5"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                  />
                </svg>
              </button>
              <button
                onClick={onClose}
                className="text-gray-500 hover:text-gray-700"
              >
                <FiX className="h-4 w-4" />
              </button>
            </div>
          </div>

          <p className="text-xs text-gray-600 mb-4">
            {title === 'Template Gallery'
              ? 'Select a template design for your marketing material.'
              : 'Choose a design theme for your content.'}
          </p>

          {/* Templates Grid */}
          <div className="grid grid-cols-2 gap-3 auto-rows-fr">
            {isLoadingTemplates ? (
              // Loading skeleton
              <>
                {[1, 2, 3, 4].map(i => (
                  <div
                    key={i}
                    className="aspect-square bg-gray-100 animate-pulse rounded"
                  >
                    <div className="h-4/5 bg-gray-200"></div>
                    <div className="h-1/5 bg-white border-t flex items-center justify-center">
                      <div className="w-3/4 h-2 bg-gray-200 rounded"></div>
                    </div>
                  </div>
                ))}
              </>
            ) : availableTemplates.length === 0 ? (
              <div className="col-span-2 flex flex-col items-center justify-center p-4 border rounded border-gray-200 bg-gray-50">
                <p className="text-sm text-gray-600 mb-2">
                  No templates available.
                </p>
                <button
                  onClick={() => setRefreshKey(prevKey => prevKey + 1)}
                  className="px-3 py-1 text-xs bg-green-100 text-green-800 rounded hover:bg-green-200"
                >
                  Refresh list
                </button>
              </div>
            ) : (
              availableTemplates.map(({ name, instance, error }) => {
                const isSelected = selectedTemplate === name;
                const displayName = name
                  .replace(/([A-Z])/g, ' $1')
                  .trim()
                  .replace('Template', '');

                return (
                  <div
                    key={name}
                    className={`
                      relative border rounded-md overflow-hidden cursor-pointer transition-all
                      ${
                        isSelected
                          ? 'ring-2 ring-offset-1'
                          : 'hover:border-ox-green-300'
                      }
                    `}
                    onClick={() => handleSelectTemplate(name)}
                  >
                    {/* Template Preview */}
                    <div className="relative w-full aspect-square bg-white flex flex-col">
                      {/* Preview container */}
                      <div className="flex-1 flex items-center justify-center overflow-hidden">
                        <Suspense
                          fallback={
                            <div className="w-full h-full flex items-center justify-center bg-gray-50">
                              <div className="animate-pulse flex flex-col items-center">
                                <div className="h-8 w-8 bg-gray-200 rounded-full mb-2"></div>
                                <div className="h-2 w-16 bg-gray-200 rounded"></div>
                              </div>
                            </div>
                          }
                        >
                          <TemplatePreview
                            instance={instance}
                            error={error}
                            templateName={name}
                          />
                        </Suspense>
                      </div>
                    </div>

                    {/* Selection Badge */}
                    {isSelected && (
                      <div
                        className="absolute top-2 right-2 rounded-full w-5 h-5 flex items-center justify-center"
                        style={{ backgroundColor: mainColor }}
                      >
                        <FiCheck className="text-white h-3 w-3" />
                      </div>
                    )}
                  </div>
                );
              })
            )}
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default ThemePanel;
