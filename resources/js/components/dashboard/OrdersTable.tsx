import React from 'react';

interface Order {
  id: string;
  customer: string;
  date: string;
  total: string;
  status: 'delivered' | 'pending' | 'action needed';
}

const OrdersTable: React.FC = () => {
  const orders: Order[] = [
    {
      id: 'ORD001',
      customer: '<PERSON>',
      date: '7.5.2024',
      total: '€4,000',
      status: 'delivered',
    },
    {
      id: 'ORD002',
      customer: '<PERSON>',
      date: '7.5.2024',
      total: '€4,000',
      status: 'pending',
    },
    {
      id: 'ORD003',
      customer: '<PERSON>',
      date: '7.5.2024',
      total: '€4,000',
      status: 'action needed',
    },
  ];

  const getStatusStyle = (status: Order['status']) => {
    switch (status) {
      case 'delivered':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-blue-100 text-blue-800';
      case 'action needed':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-lg font-semibold">ORDER STATUSES</h2>
        <span className="text-xl">📦</span>
      </div>
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="text-left text-sm text-gray-500">
              <th className="pb-4">ORDER ID</th>
              <th className="pb-4">CUSTOMER</th>
              <th className="pb-4">DATE</th>
              <th className="pb-4">TOTAL</th>
              <th className="pb-4">STATUS</th>
            </tr>
          </thead>
          <tbody>
            {orders.map((order) => (
              <tr key={order.id} className="border-t">
                <td className="py-4">{order.id}</td>
                <td className="py-4">{order.customer}</td>
                <td className="py-4">{order.date}</td>
                <td className="py-4">{order.total}</td>
                <td className="py-4">
                  <span
                    className={`px-3 py-1 rounded-full text-xs capitalize ${getStatusStyle(
                      order.status
                    )}`}
                  >
                    {order.status}
                  </span>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default OrdersTable;
