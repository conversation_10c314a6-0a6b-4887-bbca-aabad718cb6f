import React from 'react';

interface StatCardProps {
  title: string;
  value: string | number;
  icon: string;
  change: {
    value: number;
    period: string;
  };
}

const StatCard: React.FC<StatCardProps> = ({ title, value, icon, change }) => {
  return (
    <div className="bg-white p-6 rounded-lg shadow-sm w-full">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-semibold">{title}</h2>
        <span className="text-xl">{icon}</span>
      </div>
      <div className="space-y-2">
        <div className="text-3xl font-bold">{value}</div>
        <div className="text-sm text-gray-600">
          {change.value >= 0 ? '+' : ''}
          {change.value}% from {change.period}
        </div>
      </div>
    </div>
  );
};

export default StatCard;
