import React from 'react';

interface CarbonGoalCardProps {
  percentage: number;
}

const CarbonGoalCard: React.FC<CarbonGoalCardProps> = ({ percentage }) => {
  const circumference = 2 * Math.PI * 45; // radius = 45
  const offset = circumference - (percentage / 100) * circumference;

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm w-full">
      <h2 className="text-lg font-semibold mb-4">CARBON GOAL</h2>
      <div className="relative w-32 h-32 mx-auto">
        <svg className="w-full h-full transform -rotate-90">
          <circle
            className="text-gray-200"
            strokeWidth="8"
            stroke="currentColor"
            fill="transparent"
            r="45"
            cx="64"
            cy="64"
          />
          <circle
            className="text-green-400"
            strokeWidth="8"
            strokeDasharray={circumference}
            strokeDashoffset={offset}
            strokeLinecap="round"
            stroke="currentColor"
            fill="transparent"
            r="45"
            cx="64"
            cy="64"
          />
        </svg>
        <div className="absolute inset-0 flex items-center justify-center">
          <span className="text-2xl font-bold">{percentage}%</span>
        </div>
      </div>
      <div className="text-center mt-4">
        <p className="text-sm">Your business is</p>
        <p className="text-sm font-medium">100% carbon neutral</p>
      </div>
    </div>
  );
};

export default CarbonGoalCard;
