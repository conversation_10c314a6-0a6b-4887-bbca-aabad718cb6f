import React from 'react';

interface Message {
  id: number;
  sender: string;
  preview: string;
  time: string;
  unread: number;
  avatar: string;
}

const MessagesCard: React.FC = () => {
  const messages: Message[] = [
    {
      id: 1,
      sender: '<PERSON><PERSON>',
      preview: 'Lorem ipsum dolor sit...',
      time: '2:00 PM',
      unread: 2,
      avatar: '👤',
    },
    {
      id: 2,
      sender: '<PERSON> Kent',
      preview: 'consectetur adipiscing...',
      time: '2:00 PM',
      unread: 1,
      avatar: '👤',
    },
    {
      id: 3,
      sender: '<PERSON>',
      preview: 'ut labore et dolore magna...',
      time: '2:00 PM',
      unread: 3,
      avatar: '👤',
    },
  ];

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm w-full">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-lg font-semibold">NEW MESSAGES</h2>
        <span className="text-xl">💬</span>
      </div>
      <div className="space-y-4">
        {messages.map((message) => (
          <div key={message.id} className="flex items-center gap-4">
            <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
              {message.avatar}
            </div>
            <div className="flex-1">
              <div className="flex items-center justify-between">
                <h3 className="font-medium">{message.sender}</h3>
                <span className="text-sm text-gray-500">{message.time}</span>
              </div>
              <p className="text-sm text-gray-600">{message.preview}</p>
            </div>
            {message.unread > 0 && (
              <div className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                {message.unread}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default MessagesCard;
