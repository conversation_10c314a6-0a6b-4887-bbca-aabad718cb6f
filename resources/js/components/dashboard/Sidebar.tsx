import React from 'react';

interface NavItem {
  label: string;
  icon: string;
  active?: boolean;
}

const Sidebar: React.FC = () => {
  const navItems: NavItem[] = [
    { label: 'Dashboard', icon: '🏠', active: true },
    { label: 'Chat', icon: '💬' },
    { label: 'Orders', icon: '📦' },
    { label: 'Customer orders', icon: '👥' },
    { label: 'Offers', icon: '🏷️' },
    { label: 'Account', icon: '👤' },
    { label: 'Products', icon: '📝' },
  ];

  return (
    <div className="bg-gray-50 h-screen w-64 p-4 shadow-lg">
      <div className="flex items-center gap-3 mb-8">
        <div className="w-12 h-12 bg-green-200 rounded-full flex items-center justify-center">
          👤
        </div>
        <div>
          <h3 className="font-medium">Firstname Lastname</h3>
          <p className="text-sm text-gray-500">Company name</p>
        </div>
      </div>

      <nav>
        {navItems.map((item) => (
          <a
            key={item.label}
            href="#"
            className={`flex items-center gap-3 p-3 rounded-lg mb-2 ${
              item.active ? 'bg-green-200 text-green-800' : 'hover:bg-gray-100'
            }`}
          >
            <span>{item.icon}</span>
            <span>{item.label}</span>
          </a>
        ))}
      </nav>
    </div>
  );
};

export default Sidebar;
