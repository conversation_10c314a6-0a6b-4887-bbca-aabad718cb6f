import { cn } from '@/lib/utils';
import React from 'react';

interface OverlayWrapperProps {
  className?: string;
  children: React.ReactNode;
  color: string; // Any valid CSS color (e.g., '#000', 'rgba(255,0,0,0.5)')
  brightness: number; // Opacity value between 0 (transparent) and 1 (opaque)
}

const OverlayWrapper: React.FC<OverlayWrapperProps> = ({
  className = '',
  children,
  color,
  brightness
}) => {
  return (
    <div className={cn('relative w-full h-full', className)}>
      {children}
      <div
        className="absolute inset-0 pointer-events-none"
        style={{
          backgroundColor: color,
          opacity: brightness
        }}
      />
    </div>
  );
};

export default OverlayWrapper;
