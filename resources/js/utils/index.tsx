import { JSX, Suspense } from 'react';
import { createRoot } from 'react-dom/client';

export function mount(Component: any, el?: HTMLElement | string) {
  let element;
  if (typeof el === 'string') {
    element = document.querySelector(el);
    console.log('🚀 ~ mount ~ element:', element, el);
  } else {
    element = el;
  }

  if (element) {
    // Extract props from data-props attribute if it exists
    let props = {};
    const dataProps = element.getAttribute('data-props');

    if (dataProps) {
      try {
        props = JSON.parse(dataProps);
        console.log('🚀 ~ mount ~ props:', props);
      } catch (error) {
        console.error('Failed to parse data-props attribute:', error);
      }
    }

    createRoot(element).render(
      <Suspense fallback={<p>loading testapp...</p>}>
        <Component {...props} />
      </Suspense>
    );
  }
}
