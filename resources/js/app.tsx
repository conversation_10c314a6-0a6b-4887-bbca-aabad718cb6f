import.meta.glob(['../images/**', '../fonts/**', '../svg/**']);
import './bootstrap';

import { createInertiaApp } from '@inertiajs/react';
import { createRoot, hydrateRoot } from 'react-dom/client';
import { resolvePageComponent } from 'laravel-vite-plugin/inertia-helpers';

console.log('🚀 ~ file: app.tsx:8 ~ import.meta.env');
createInertiaApp({
  id: 'app',
  resolve: name => {
    return resolvePageComponent(
      `./pages/${name}.tsx`,
      import.meta.glob('./pages/**/*.tsx', { eager: true }) as Record<
        string,
        () => Promise<unknown>
      >
    );
  },
  title: title => (title ? `${title} - Ping CRM` : 'Ping CRM'),
  setup({ el, App, props }) {
    const appElement = <App {...props} />;
    if (import.meta.env.SSR) {
      hydrateRoot(el, appElement);
      return;
    }
    createRoot(el).render(appElement);
  }
});
