interface Project {
    name: string;
    id: number;
    price: number;
}

export function initFootprintExample() {
    const container = document.querySelector('.footprint-example-page');
    if (!container) return;

    // Scoped selectors
    const dropdownContent = container.querySelector<HTMLElement>(".sidebar-dropdown-content");
    const selectedProjectBtn = container.querySelector<HTMLElement>("#selectedProject");
    const totalAmountDisplay = container.querySelector<HTMLElement>(".total-amount .bolded-num");

    const buttons = {
        scope1: container.querySelector<HTMLElement>("#scope1-btn"),
        scope2: container.querySelector<HTMLElement>("#scope2-btn")
    };

    const values = {
        val12: container.querySelector<HTMLElement>(".scope-val12"),
        valTotal: container.querySelector<HTMLElement>(".scope-valTotal")
    };

    // Ensure all required elements exist
    if (!dropdownContent || !selectedProjectBtn || !totalAmountDisplay || 
        !buttons.scope1 || !buttons.scope2 || !values.val12 || !values.valTotal) {
        console.warn('Some required elements for footprint example not found');
        return;
    }

    function updateValuesDisplay(): void {
        Object.values(values).forEach(val => {
            if (val) val.style.display = "none";
        });

        if (buttons.scope1?.classList.contains("active")) {
            values.val12!.style.display = "block";
        } else if (buttons.scope2?.classList.contains("active")) {
            values.valTotal!.style.display = "block";
        }

        const selectedProjectPrice = parseFloat(selectedProjectBtn.dataset.price || "0");
        updateTotalAmount(selectedProjectPrice);
    }

    function toggleButton(button: HTMLElement): void {
        Object.values(buttons).forEach(btn => {
            btn?.classList.remove("active");
        });
        button.classList.add("active");
        updateValuesDisplay();
    }

    function getCurrentVisibleCarbonValue(): number {
        const visibleValue = Array.from(container.querySelectorAll<HTMLElement>(".scope-val12, .scope-valTotal"))
            .find(val => val.style.display === "block");
        return visibleValue ? parseFloat(visibleValue.textContent || "0") : 0;
    }

    function updateTotalAmount(projectPrice: number): void {
        const currentCarbonValue = getCurrentVisibleCarbonValue();
        const totalAmount = currentCarbonValue * projectPrice;
        if (totalAmountDisplay) {
            totalAmountDisplay.textContent = totalAmount.toFixed(0);
        }
    }

    function updateDropdownButton(projectName: string, projectPrice: number): void {
        selectedProjectBtn.innerHTML = projectName + " ";
        selectedProjectBtn.dataset.price = projectPrice.toString();
    }

    // Event Listeners
    buttons.scope1?.addEventListener("click", () => toggleButton(buttons.scope1!));
    buttons.scope2?.addEventListener("click", () => toggleButton(buttons.scope2!));

    dropdownContent.addEventListener("click", (event) => {
        const target = (event.target as HTMLElement).closest<HTMLAnchorElement>("a");
        
        if (!target || target.classList.contains("default-event-link")) return;

        event.preventDefault();
        
        const { name, price } = target.dataset;
        if (name && price) {
            updateDropdownButton(name, parseFloat(price));
            updateTotalAmount(parseFloat(price));
        }
    });

    // Initialize state
    buttons.scope1?.classList.add("active");
    values.val12!.style.display = "block";

    // Initialize Swiper if it exists
    if (container.querySelector('.example-page-swiper')) {
        new Swiper(".example-page-swiper", {
            effect: "coverflow",
            grabCursor: true,
            centeredSlides: true,
            slidesPerView: "auto",
            initialSlide: 1,
            coverflowEffect: {
                rotate: 20,
                stretch: 0,
                depth: 700,
                modifier: 1,
                slideShadows: true,
            },
        });
    }
}

// Initialize when DOM is ready
document.addEventListener("DOMContentLoaded", initFootprintExample); 