<div class="swiper projects-slider">
    <div class="swiper-wrapper">
        @foreach($products as $product)
            <div class="swiper-slide">
                <x-project-card :type="$product->type" :title="$product->name" :description="$product->description" 
                    :location="$product->get_meta('country') ?: __('Global', CO2MARKET_TEXT_DOMAIN)" :score="$product->get_meta('score', true) ?? 90"
                    :price="$product->get_price()" :image="get_the_post_thumbnail_url($product->get_id(), 'full')"
                    :rating="$product->get_average_rating()" :comments="$product->get_review_count()"
                    :tonnes="$product->get_meta('tonnes', true) ?? 0" />
            </div>
        @endforeach
    </div>
    <div class="swiper-pagination"></div>
    <div class="swiper-button-prev"></div>
    <div class="swiper-button-next"></div>
</div>

<script>
    new Swiper('.projects-slider', {
        slidesPerView: 1,
        spaceBetween: 30,
        pagination: {
            el: '.swiper-pagination',
            clickable: true,
        },
        navigation: {
            nextEl: '.swiper-button-next',
            prevEl: '.swiper-button-prev',
        },
        breakpoints: {
            640: {
                slidesPerView: 2,
            },
            1024: {
                slidesPerView: 3,
            },
        },
    });
</script>