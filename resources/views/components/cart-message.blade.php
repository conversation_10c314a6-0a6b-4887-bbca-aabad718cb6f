@props([
    'type' => 'success',
    'message' => '',
])

@php
    $styles = [
        'success' => 'bg-ox-green-100 border-ox-green-400 text-ox-green-600',
        'error' => 'bg-red-100 border-red-400 text-red-600',
    ];
    $containerClass = $styles[$type] ?? $styles['success'];
@endphp

<div x-data="{ show: true }" 
     x-init="setTimeout(() => show = false, 5000)" 
     x-show="show"
     x-transition.opacity.duration.500ms
     class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mt-4">
    <div class="{{ $containerClass }} px-4 py-3 rounded relative border flex items-center space-x-2" role="alert">
        @if ($type === 'success')
            @svg('alert_checkmark', ['class' => 'w-8 h-8'])
        @else
            @svg('alert_x', ['class' => 'w-8 h-8'])
        @endif
        <span class="block sm:inline">{{ $message }}</span>
    </div>
</div>
