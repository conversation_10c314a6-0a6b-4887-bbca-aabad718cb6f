@props(['process', 'isSelected' => false])

@php
    /** @var $process \App\Models\OffsetProcess */
    $step = $process->step;
    $type = $process->type;
@endphp

<a href="#" 
   @click.prevent="setSelectedProcess('{{ $process->id }}'); open = false"
   class="block px-4 py-3 rounded-md"
   :class="{ 'bg-ox-green-200': selectedProcessId == '{{ $process->id }}' }">
    <div class="flex justify-between items-center w-full">
        <div class="emission-item flex flex-col">
            <x-process-selector.emissions :process="$process" />
            <span class="text-ox-black">{{ $process?->name }}</span>
        </div>

        <div class="flex gap-6 items-center">
            <x-process-selector.step-icon :completed="$step->hasCompletedOffsetStep()" name="{{ __('offset') }}">
                @svg('main_offset')
            </x-process-selector.step-icon>

            <x-process-selector.step-icon :completed="$step->hasCompletedCommunicateStep()" name="{{ __('communicate') }}">
                @svg('main_communicate')
            </x-process-selector.step-icon>

            @if ($type)
                <x-badge :color="$type->toColor()">
                    {{ Str::title($type->toLabel()) }}
                </x-badge>
            @endif
        </div>
    </div>
</a>
