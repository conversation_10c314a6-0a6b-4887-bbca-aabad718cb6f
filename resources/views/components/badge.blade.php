@props(['color' => 'blue'])

@php
    $baseClasses = 'inline-flex items-center rounded-md px-2.5 py-1 text-xs font-medium';
    $colorClasses = [
        'blue' => 'bg-ox-blue-400 text-ox-blue-600',
        'green' => 'bg-ox-green-400 text-ox-green-600',
        'red' => 'bg-ox-warning-secondary text-ox-black',
        'orange' => 'bg-ox-orange-400 text-ox-orange-600',
        'gray' => 'bg-gray-100 text-gray-800',
    ];
@endphp

<span {{ $attributes->merge(['class' => $baseClasses . ' ' . ($colorClasses[(string) $color] ?? $colorClasses['gray'])]) }}>
    {{ $slot }}
</span>