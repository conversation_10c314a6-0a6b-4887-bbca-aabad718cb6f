@props(['products'])

<div class="max-w-[1000px] w-full" x-data="{ 
    scrollToTop() {
        window.scrollTo({
            top: 0,
            behavior: 'instant'
        });
    }
}">
    <!-- Loading State -->
     <div wire:loading.class.remove="hidden" class="w-full hidden">
        <div class="w-full flex justify-center items-center min-h-[400px]">
            <i class="fa-solid fa-spinner fa-spin text-4xl text-ox-green-600"></i>
        </div>
    </div>

    


    <!-- Products Grid - Hide when loading -->
    <div wire:loading.remove>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            @foreach($products['items'] as $product)
                <div class="w-full">
                    <x-project-card :title="$product->get_name()" :price="$product->get_price()"
                        :image="wp_get_attachment_url($product->get_image_id())" :product="$product" />
                </div>
            @endforeach
        </div>

        @if($products['last_page'] > 1)
            <div class="mt-8 flex justify-between items-center">
                <!-- Empty left section -->
                <div class="w-1/3"></div>

                <!-- Center pagination buttons -->
                <div class="w-1/3 flex justify-center gap-2">
                    @if($products['current_page'] > 1)
                        <button wire:click="previousPage" x-on:click="scrollToTop()"
                            class="px-4 py-2 rounded-md bg-ox-green-400 text-ox-green-600 hover:bg-ox-green-200">
                            {{ __('Previous', CO2MARKET_TEXT_DOMAIN) }}
                        </button>
                    @endif

                    @if($products['current_page'] < $products['last_page'])
                        <button wire:click="nextPage" x-on:click="scrollToTop()"
                            class="px-4 py-2 rounded-md bg-ox-green-400 text-ox-green-600 hover:bg-ox-green-200">
                            {{ __('Next', CO2MARKET_TEXT_DOMAIN) }}
                        </button>
                    @endif
                </div>

                <!-- Right side page navigation -->
                <div class="w-1/3 flex justify-end items-center gap-2">
                    <button wire:click="previousPage" x-on:click="scrollToTop()"
                        class="w-10 h-10 flex items-center justify-center text-ox-green-600 hover:text-ox-green-400 disabled:opacity-50 disabled:cursor-not-allowed"
                        {{ $products['current_page'] === 1 ? 'disabled' : '' }}>
                        <i class="fa-solid fa-chevron-left"></i>
                    </button>

                    <input wire:model.live="page" 
                           x-on:change="scrollToTop()" 
                           x-on:keyup.enter="$wire.setPage($event.target.value); scrollToTop()"
                           type="number" 
                           min="1"
                           max="{{ $products['last_page'] }}"
                           class="w-10 h-10 text-center border-2 rounded-md border-ox-green-200 focus:border-ox-green-600 focus:outline-none [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                           value="{{ $products['current_page'] }}">

                    <button wire:click="nextPage" x-on:click="scrollToTop()"
                        class="w-10 h-10 flex items-center justify-center text-ox-green-600 hover:text-ox-green-400 disabled:opacity-50 disabled:cursor-not-allowed"
                        {{ $products['current_page'] === $products['last_page'] ? 'disabled' : '' }}>
                        <i class="fa-solid fa-chevron-right"></i>
                    </button>
                </div>
            </div>
        @endif
    </div>
</div>























