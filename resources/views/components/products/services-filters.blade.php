@props([
    'countries' => [],
    'types' => [],
    'service_types' => [],
    'min_price' => null,
    'max_price' => null,
    'min_stars' => null,
    'min_reviews' => null,
    'max_reviews' => null,
    'has_active_filters' => false,
])

<div class="w-full max-w-[300px] relative z-0">
    <x-accordion title="{{ __('country', CO2MARKET_TEXT_DOMAIN) }}">
        @php
            $all_countries = \App\Models\Product::get_all_countries();
            $countryItems = collect($all_countries)->map(function ($country) {
                return [
                    'value' => strtolower(str_replace(' ', '-', $country)),
                    'label' => $country
                ];
            })->toArray();
        @endphp
        <div class="flex flex-col gap-2 z-50">
            <x-multi-select
                :options="$countryItems"
                name="countries"
                placeholder="{{ __('Select countries...', CO2MARKET_TEXT_DOMAIN) }}"
                :initial-value="$countries"
                :on-change="'function(values) { $wire.set(\'countries\', values) }'"
            />
        </div>
    </x-accordion>

    <x-divider spacing="4" />

    <x-accordion title="{{ __('type', CO2MARKET_TEXT_DOMAIN) }}">
        <div class="flex flex-col gap-2 z-50">
            <x-multi-select
                :options="$service_types"
                name="types"
                placeholder="{{ __('Select types...', CO2MARKET_TEXT_DOMAIN) }}"
                :initial-value="$types"
                :on-change="'function(values) { $wire.set(\'types\', values) }'"
            />
        </div>
    </x-accordion>


    <x-divider spacing="4" />

    <x-accordion title="{{ __('price', CO2MARKET_TEXT_DOMAIN) }}">
        @php
            $maxPrice = \App\Models\Product::get_max_price_of_cat("consult-service");
        @endphp
        <x-range-slider
            :min="0"
            :max="$maxPrice"
            :step-size="1"
            :initial-min="$min_price"
            :initial-max="$max_price"
            x-on:min-change.debounce.500ms="$wire.update_price_range($event.detail, $wire.max_price)"
            x-on:max-change.debounce.500ms="$wire.update_price_range($wire.min_price, $event.detail)"
            class="price-range-slider"
        />
    </x-accordion>

    <x-divider spacing="4" />

    <x-accordion title="{{ __('ratings', CO2MARKET_TEXT_DOMAIN) }}">
        <div class="flex flex-col gap-2">
            @for($i = 5; $i >= 1; $i--)
                <button
                    wire:click="update_min_stars({{ $i }})"
                    class="flex items-center gap-2 px-3 py-2 rounded-lg transition-colors {{ $min_stars === $i ? 'bg-ox-green-200 text-ox-green-600' : 'hover:bg-ox-green-100' }}">
                    <div class="flex items-center">
                        @for($j = 1; $j <= 5; $j++)
                            @if($j <= $i)
                                @svg("general_star", ["class" => "w-4 h-4 " . ($min_stars === $i ? 'text-white' : 'text-ox-green-600')])
                            @else
                                @svg("general_star_empty", ["class" => "w-4 h-4 " . ($min_stars === $i ? 'text-white' : 'text-gray-300')])
                            @endif
                        @endfor
                    </div>
                    <span class="ml-2">{{ $i }} {{ $i === 1 ? 'Star' : 'Stars' }}</span>
                </button>
            @endfor
        </div>
    </x-accordion>

    <x-divider spacing="4" />

    <x-accordion title="{{ __('reviews', CO2MARKET_TEXT_DOMAIN) }}">
        <x-range-slider
            :min="0"
            :max="100"
            :step-size="1"
            :initial-min="$min_reviews"
            :initial-max="$max_reviews"
            x-on:min-change.debounce.500ms="$wire.update_reviews_range($event.detail, $wire.max_reviews)"
            x-on:max-change.debounce.500ms="$wire.update_reviews_range($wire.min_reviews, $event.detail)"
            class="review-range-slider"
        />
    </x-accordion>

    <x-divider spacing="4" />

    @if($has_active_filters)
        <div class="px-4 mt-4">
            <button
                wire:click="clear_all_filters"
                class="w-fit mx-auto py-2 px-4 border-2 border-ox-green-400 text-ox-green-600 rounded-lg hover:bg-ox-green-200 transition-colors flex items-center justify-center gap-2">
                <i class="fa-solid fa-xmark"></i>
                {{ __('Clear All Filters', CO2MARKET_TEXT_DOMAIN) }}
            </button>
        </div>
    @endif
</div>

