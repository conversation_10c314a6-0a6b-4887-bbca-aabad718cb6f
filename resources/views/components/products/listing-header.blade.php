@props(['title' => null, 'placeholder' => null])
<div class="max-w-[1000px]">
    <!-- Title and Controls Section -->
    <div class="mb-8">
        <h1 class="text-xl font-bold text-ox-black mb-6 uppercase">{{ $title }}</h1>

        <div class="flex items-center gap-6 justify-between w-full">
            <!-- Search Input Container -->
            <div class="flex-1 max-w-[500px] flex items-center gap-1">
                <div class="flex-1 relative">
                    <input
                        type="text"
                        wire:model.live.debounce.300ms="search"
                        placeholder="{{ __('Search...', CO2MARKET_TEXT_DOMAIN) }}"
                        class="w-full px-4 py-2 rounded-md border-2 border-ox-green-200 focus:border-ox-green-600 focus:outline-none">
                </div>
                <button 
                    class="flex items-center justify-center w-11 h-11 bg-ox-green-400 rounded-md"
                    wire:loading.class="opacity-50"
                    wire:loading.attr="disabled">
                    <svg xmlns="http://www.w3.org/2000/svg" 
                         class="h-5 w-5 text-ox-green-600" 
                         fill="none" 
                         viewBox="0 0 24 24" 
                         stroke="currentColor"
                         wire:loading.class="hidden">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                    <svg wire:loading class="animate-spin h-5 w-5 text-ox-green-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                </button>
            </div>

            <!-- Sort Controls -->
            <div class="flex items-center gap-3">
                <span class="text-sm font-medium text-gray-700">Sort By:</span>
                <div class="w-48 relative">
                    <select
                        wire:model.live="sort"
                        class="w-full px-4 pr-10 py-2 rounded-lg border-2 border-ox-green-200 focus:border-ox-green-600 focus:outline-none appearance-none">
                        <option value="default">{{ __('Default', CO2MARKET_TEXT_DOMAIN) }}</option>
                        <option value="rating_desc">{{ __('Rating (High to Low)', CO2MARKET_TEXT_DOMAIN) }}</option>
                        <option value="rating_asc">{{ __('Rating (Low to High)', CO2MARKET_TEXT_DOMAIN) }}</option>
                        <option value="price_desc">{{ __('Price (High to Low)', CO2MARKET_TEXT_DOMAIN) }}</option>
                        <option value="price_asc">{{ __('Price (Low to High)', CO2MARKET_TEXT_DOMAIN) }}</option>
                    </select>
                    <div class="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none text-ox-green-600">
                        <i class="fa-solid fa-chevron-down"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
