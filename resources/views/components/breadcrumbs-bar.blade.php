@php
    global $post;
    $post_type = get_post_type($post);
@endphp

<div class="breadcrumbs-container">
    <a class="back-button" href="/">
        <img src="/wp-content/uploads/2024/05/arrow_icon.svg" alt="arr" width="15">
        <span>Back to search</span>
    </a>
    
    <div class="custom-breadcrumbs">
        <a href="/">Home</a>
        
        @if (is_single())
            @if ($post_type === 'footprint-example')
                <a href="/calculate">Get footprint</a>
                <a href="/calculate/explore-examples">Business examples</a>
                <a style="opacity: 100%" href="{{ get_permalink() }}">{{ get_the_title() }}</a>
                
            @elseif ($post_type === 'company')
                <a href="/calculate">Get footprint</a>
                <a href="/calculate/search-consultants">Consultants</a>
                <a style="opacity: 100%" href="{{ get_permalink() }}">{{ get_the_title() }}</a>
                
            @elseif ($post_type === 'product')
                @php
                    $id = $post->ID;
                    $is_compensation = has_term('compensation-project', 'product_cat', $id) || 
                                     has_term('korvaushanke', 'product_cat', $id);
                @endphp
                
                @if ($is_compensation)
                    @php
                        $company = get_field('project_provider', $id);
                    @endphp
                    <a href="/offset">Offset</a>
                    <a href="/offset-projects">Offset projects</a>
                    @if ($company)
                        <a href="#">{{ $company }}</a>
                    @endif
                @else
                    @php
                        $company = get_field('company_name', $id);
                        $url_slug = str_replace(" ", "-", strtolower($company));
                    @endphp
                    <a href="/calculate">Get footprint</a>
                    <a href="/calculate/search-consultants">Consultants</a>
                    @if ($company)
                        <a href="/post/company/{{ $url_slug }}">{{ $company }}</a>
                    @endif
                @endif
                <a style="opacity: 100%" href="{{ get_permalink() }}">{{ get_the_title() }}</a>
            @endif
        @endif
    </div>
</div>