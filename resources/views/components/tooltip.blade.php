@props(['tooltipText'])

<div x-data="{ showTooltip: false }" class="relative inline-block">
    <!-- Slot Content -->
    <div @mouseover="showTooltip = true" @mouseleave="showTooltip = false">
        {{ $slot }}
    </div>

    <!-- Tooltip Portal -->
    <template x-portal>
        <div x-show="showTooltip" x-transition.opacity.duration.200ms
            class="absolute z-50 p-2 text-sm text-white bg-gray-800 rounded shadow-lg"
            style="top: 100%; left: 50%; transform: translateX(-50%);">
            {{ $tooltipText }}
        </div>
    </template>
</div>