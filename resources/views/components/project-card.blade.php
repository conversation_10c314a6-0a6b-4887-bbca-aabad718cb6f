@props([
    'rating' => 3.5,
    'comments' => 150,
    'location' => 'Korea',
    'title' => 'Taebaek Wind Park (Hasami Samcheok)',
    'projectId' => 'CDM Project UN 9165',
    'price' => "4.00",
    'tonnes' => 10209,
    "product" => null
])

@php
  /** @var \App\Models\Product $product */
  $sdgs = $product->get_sdgs();
  $main_project_type = $product->get_main_project_type();
  $score = $product->get_score();
  
  // Calculate width class based on score
  $widthClass = '';
  if ($score !== null) {
    $scoreValue = min($score, 100);
    if ($scoreValue <= 5) {
      $widthClass = 'w-[5%]';
    } elseif ($scoreValue <= 10) {
      $widthClass = 'w-[10%]';
    } elseif ($scoreValue <= 20) {
      $widthClass = 'w-[20%]';
    } elseif ($scoreValue <= 30) {
      $widthClass = 'w-[30%]';
    } elseif ($scoreValue <= 40) {
      $widthClass = 'w-[40%]';
    } elseif ($scoreValue <= 50) {
      $widthClass = 'w-[50%]';
    } elseif ($scoreValue <= 60) {
      $widthClass = 'w-[60%]';
    } elseif ($scoreValue <= 70) {
      $widthClass = 'w-[70%]';
    } elseif ($scoreValue <= 80) {
      $widthClass = 'w-[80%]';
    } elseif ($scoreValue <= 90) {
      $widthClass = 'w-[90%]';
    } else {
      $widthClass = 'w-full';
    }
  }
@endphp
@if($product)
  <a href="{{ $product->get_permalink() }}" class="block w-full">
    <div class="bg-ox-green-100 rounded-2xl overflow-hidden h-full transition-all duration-300  hover:-translate-y-1">
      {{-- Project Image Section --}}
      <div class="relative h-48">
        <img src="{{  $attributes->get('image', null) ?: @Vite::image('project-placeholder.jpg') }}"
             alt="{{ $title }}"
             class="w-full h-full object-cover">
        <div class="absolute top-4 left-4 right-4">
          <div class="flex gap-2 w-full justify-between">
            @if($main_project_type)
              <div class="flex space-x-2">
                <div class="w-10 h-10 rounded-md overflow-hidden bg-ox-green-200 flex items-center justify-center">
                  <img src="{{ $main_project_type['icon'] ?: @Vite::svg("projects/project_6_icon.svg") }}" alt="{{ $main_project_type['name'] }}"
                       class="w-full h-full text-ox-green-600">
                </div>
              </div>
            @endif

          <div class="flex gap-2">
            @foreach($sdgs as $sdg)
              <x-tooltip tooltipText="Ok">
                <div class="w-6 h-6 rounded-full bg-ox-green-200 flex items-center justify-center">
                  <img src="{{ $sdg['icon'] }}" alt="{{ $sdg['name'] }}" class="w-4 h-4 text-ox-green-600">
                </div>
              </x-tooltip>
            @endforeach
          </div>
        </div>
        </div>
      </div>

      {{-- Metrics Bar --}}
      <div class="px-4 py-2 border-gray-200 flex items-center justify-between gap-2 h-10">
        {{-- Score Bar --}}
        @if($score !== null)
        <div class="flex items-center space-x-2 w-3/5">
          <div class="w-full bg-ox-green-600 h-2 rounded-none relative">
            <div class="bg-ox-green-400 h-2 rounded-none absolute top-0 left-0 {{ $widthClass }}"></div>
          </div>
          <span class="text-ox-black font-bold text-[15px] whitespace-nowrap">{{ $score }}</span>
        </div>
        <div class="h-full w-1">|</div>
        @endif
        {{-- Rating and Comments --}}
        <div class="flex items-center space-x-4 flex-shrink-0">
          <div class="flex items-center">
            @svg("general_star", "!w-5 !h-auto")
            <span class="ml-1 text-sm text-ox-black">{{ $rating }}</span>
          </div>
          <div class="flex items-center">
            @svg("general_comment", "!w-5 !h-auto")
            <span class="ml-1 text-sm text-ox-black">{{ $comments }}</span>
          </div>
        </div>
      </div>

      {{-- Project Details --}}
      <div class="p-4">
        <div class="flex items-center text-gray-600 mb-2">
          @svg("general_location", "!w-6 !h-auto")
          <span class="text-sm">{{ $location ?: __('Global', CO2MARKET_TEXT_DOMAIN) }}</span>
        </div>

        <b class="text-sm text-left font-bold text-ox-black mb-4 overflow-hidden line-clamp-2"
           style="display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical;">
          {{ $title }}
        </b>

        <div class="flex flex-wrap gap-x-2 gap-y-2">
          <div class="badge green">
            @php
              $current_currency = get_woocommerce_currency() ?: 'EUR';
            @endphp
            <p class="text-sm text-ox-green-600">{{ $current_currency }}</p>
            <p class="text-sm font-bold text-ox-green-600">{{ number_format(ceil($price * 100) / 100, 2) }}</p>
          </div>
          <div class="badge green">
            <p class="text-sm text-ox-green-600">Tonnes</p>
            <p class="text-sm font-bold text-ox-green-600">{{ number_format($tonnes, 0) }}</p>
          </div>
          @php
            // Get the current currency
            $current_currency = get_woocommerce_currency();
            $priceValue = floatval($price);
          @endphp
          <div class="badge score" x-data>
            @svg("main_carbon_footprint", "!w-[22px] !h-[22px] mr-1")
            <div>
              <p class="text-sm text-ox-green-600">{{ $current_currency }} <span class="font-bold" x-text="(Math.ceil((($store.selectedProcess?.total_emissions || 1) * {{ $priceValue }}) * 100) / 100).toFixed(2)"></span></p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </a>
@else
  <div class="bg-ox-green-100 rounded-2xl overflow-hidden h-full transition-all duration-300 hover:shadow-lg hover:-translate-y-1">
    <div class="relative h-48">
      <img src="{{  $attributes->get('image', null) ?: @Vite::image('/project-placeholder.jpg') }}"
           alt="{{ $title }}"
           class="w-full h-full object-cover">
    </div>
  </div>
@endif
