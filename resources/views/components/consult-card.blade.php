@props([
"product" => null,
])

@php
/** @var \App\Models\Product $product */
@endphp


<!-- profile-card.blade.php -->
<a href="{{ $product->get_permalink() }}" class="block w-full">
    <div class="bg-ox-green-100 rounded-2xl h-full hover:-translate-y-1 transition-all duration-300 p-6 flex flex-col">
        <!-- Logo/Name Circle -->
        <div class="flex flex-col items-center mb-6">
            <div class="bg-white rounded-full w-24 h-24 flex items-center justify-center mb-4 overflow-hidden">

                <img src="{{  $attributes->get('image', null) ?: @Vite::image('project-placeholder.jpg') }}"
                    alt="{{ $title }}"
                    class="w-full h-full object-cover">

            </div>
        </div>

        <!-- Info Row -->
        <div class="flex items-center justify-start space-x-4 mb-2">
            <div class="flex items-center text-gray-600">
                @svg("general_location", "!w-6 !h-auto")
                <span class="text-sm">{{ $product->get_country() }}</span>
            </div>
            <div class="h-full w-1">|</div>
            <div class="flex items-center space-x-4">
                <div class="flex items-center">
                    @svg("general_star", "!w-5 !h-auto")
                    <span class="ml-1 text-sm text-ox-black">{{ $product->get_average_rating() }}</span>
                </div>
                <div class="flex items-center">
                    @svg("general_comment", "!w-5 !h-auto")
                    <span class="ml-1 text-sm text-ox-black">{{ $product->get_review_count() }}</span>
                </div>
            </div>
        </div>

        <!-- Company Name -->
        <bold class="text-[14px] font-bold text-gray-900 text-left w-full mb-4">{{ $product->get_name() }}</bold>

        <!-- Spacer to push badges to bottom -->
        <div class="flex-grow"></div>

        <!-- Action divs -->
        <div class="flex flex-wrap gap-2">
            @php
                $categories = $product->get_terms_objects();
                
                // Check if categories exist and are valid
                if (is_array($categories) && !empty($categories)) {
                    // Filter out the 'consult-service' category and 'uncategorized'
                    $filtered_categories = array_filter($categories, function($term) {
                        return $term && is_object($term) && isset($term->slug) && 
                               $term->slug !== 'consult-service' && 
                               $term->slug !== 'uncategorized';
                    });
                } else {
                    $filtered_categories = [];
                }
            @endphp
            
            @if(count($filtered_categories) > 0)
                @foreach($filtered_categories as $category)
                    <div class="badge green">{{ $category->name }}</div>
                @endforeach
            @else
                <div class="badge green">{{ __('Consulting', CO2MARKET_TEXT_DOMAIN) }}</div>
            @endif
        </div>
    </div>
</a>
