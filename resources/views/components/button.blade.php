@props([
    'type' => 'button',
    'variant' => 'primary', // primary, green, orange, blue
    'style' => 'filled', // filled, outline
    'disabled' => false,
    'class' => '',
    'rounded' => false,
])

<button type="{{ $type }}"
    {{ $attributes->merge([
        'class' =>
            'button ' .
            'button-' .
            $variant .
            ' button-' .
            $style .
            ($disabled ? ' button-disabled' : '') .
            ($rounded ? ' rounded-full' : '') .
            ' ' .
            $class,
    ]) }}
    @disabled($disabled)>
    {{ $slot }}
</button>
