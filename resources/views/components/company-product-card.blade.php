@props(['product', 'featured' => false])

@php
    $product_obj = wc_get_product($product->ID);
    $rating = floatval($product_obj->get_average_rating());
    $rating_count = $product_obj->get_rating_count();
    $categories = wp_get_post_terms($product->ID, 'product_cat');
    $image_id = $product_obj->get_image_id();
    $image_url = wp_get_attachment_image_url($image_id, 'full');
    $purchase_type = get_field('purchase_type', $product->ID);
    $time_estimate = get_field('time_estimate', $product->ID);
    $can_ask_for_offer = get_field('can_ask_for_offer', $product->ID);
    $price = $product_obj->get_price();
    $has_price = !empty($price) && $price !== '';
@endphp

<div {{ $attributes->merge(['class' => 'project-card' . ($featured ? ' featured' : '')]) }}>
    {{-- Left side - Image --}}
    <div class="project-image-container">
        @if($image_url)
            <img src="{{ $image_url }}" alt="{{ get_the_title($product->ID) }}">
        @else
            <div class="placeholder-image">
                <span>V</span>
            </div>
        @endif
    </div>

    {{-- Right side - Content --}}
    <div class="project-card-content">
        {{-- Top row - Categories and Meta --}}
        <div class="card-header">
            <div class="type-container">
                @foreach($categories as $category)
                    @if($category->name != "Uncategorized" && $category->name != "Consulting service")
                        <div class="type">{{ $category->name }}</div>
                    @endif
                @endforeach
            </div>

            <div class="meta-info">
                @if($rating > 0)
                    <div class="rating">
                        {!! file_get_contents(resource_path('svg/general_star.svg')) !!}
                        <span>{{ number_format($rating, 1) }}</span>
                    </div>
                @endif

                <div class="comments">
                    {!! file_get_contents(resource_path('svg/general_comment.svg')) !!}
                    <span>{{ get_comments_number($product->ID) }}</span>
                </div>
            </div>
        </div>

        {{-- Middle - Title and Info --}}
        <h3 class="product-title">
            <a href="{{ get_permalink($product->ID) }}">{{ get_the_title($product->ID) }}</a>
        </h3>

        <div class="info-grid">
            @if($purchase_type)
                <div class="info-row">
                    <span class="label">Purchase type</span>
                    <span class="value">{{ $purchase_type }}</span>
                </div>
            @endif

            @if($time_estimate)
                <div class="info-row">
                    <span class="label">Time estimate</span>
                    <span class="value">{{ $time_estimate }}</span>
                </div>
            @endif
        </div>

        {{-- Bottom - Price and Action --}}
        <div class="card-footer">
            @if($can_ask_for_offer)
                <a href="#" class="ask-for-offer-btn popmake-1077">
                    {{ __('Ask for offer') }}
                </a>
            @endif

            @if($has_price)
                <div class="price">€{{ number_format(ceil($price * 100) / 100, 2) }}</div>
                @php
                    $translated = has_term('korvaushanke', 'product_cat', $product->ID) ? "/fi/" : "/";
                @endphp
                <form class="cart" action="{{ $translated }}?post_type=product&amp;p={{ $product->ID }}" method="post" enctype="multipart/form-data">
                    <button type="submit" name="add-to-cart" value="{{ $product->ID }}" class="cart-button">
                        {!! file_get_contents(resource_path('svg/general_cart.svg')) !!}
                        Add to cart
                    </button>
                </form>
            @endif
        </div>
    </div>
</div>
