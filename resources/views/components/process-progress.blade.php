@php
  /** @var $processes array<\App\Models\OffsetProcess> */
  /** @var $selectedProcess \App\Models\OffsetProcess|null */

  // Make sure processes is defined and not empty
  $processes = $processes ?? [];
  // Define selectedProcess as a parameter with null default
  $selectedProcess = $selectedProcess ?? null;
  $selectedProcessId = $_COOKIE['selected_process_id'] ?? null;

  if (!$selectedProcess && $selectedProcessId && !empty($processes)) {
      $selectedProcess = collect($processes)->first(fn($p) => $p->id == $selectedProcessId);
  }

  // Ensure we have a default process for initial state
  $selectedProcess = $selectedProcess ?? (!empty($processes) ? collect($processes)->first() : null);
  $currentStep = $selectedProcess ? $selectedProcess->step->getStepNumber() : 0;
@endphp


<style>
.progress-step-3 {
    border-left: none;
}

.progress-step {
    position: relative;
    display: flex;
    align-items: stretch;
    height: 45px;
    min-width: 150px;
    cursor: pointer;
}

.progress-content {
    display: flex;
    flex: 1;
    border: 2.5px solid #E1F0DC;
    padding-inline: 1rem;
    background-color: #FAFAF5;
    justify-content: center;
    align-items: center;
    z-index: 1;
}

.progress-inner {
    display: flex;
    align-items: center;
}

.progress-cap {
    height: calc(100%);
    fill: none;
    stroke: #E1F0DC;
    stroke-width: 5px;
}

.progress-step:hover .progress-content {
    background-color: #E1F0DC;
}

.progress-step:hover .progress-cap path {
    fill: #E1F0DC;
}

@media (max-width: 639px) {
    .progress-step-2, .progress-step-3 {
        margin-left: -10px;
    }
}
</style>

<script>
  document.addEventListener('alpine:init', () => {
    Alpine.store('process', {
      currentStep: {{ $currentStep }},
      setStep(step) {
        this.currentStep = step;
      }
    });
  });
</script>
<div class="flex flex-col sm:flex-row min-h-[40px] gap-4 sm:gap-1 w-full xl:w-auto items-start sm:items-center justify-between xl:justify-start" x-data>
    <x-progress-bar.step1 class="flex-shrink-0 w-auto" icon="main_get_footprint" link="/calculate">
        <div class="ml-[5px] flex items-center">
            <span class="font-bold" style="padding-right: 4px;">1. </span>
            <span>Get footprint</span>
        </div>
    </x-progress-bar.step1>

    <x-progress-bar.step2 class="flex-shrink-0 w-auto" icon="main_offset" link="/offset">
       <div class="ml-[5px] flex items-center">
        <span class="font-bold" style="padding-right: 4px;">2. </span>
        <span>Offset</span>
       </div>
    </x-progress-bar.step2>

    <x-progress-bar.step3 class="flex-shrink-0 w-auto" icon="main_communicate" link="/communicate">
        <div class="ml-[5px] flex items-center">
            <span class="font-bold" style="padding-right: 4px;">3. </span>
            <span>Communicate</span>
        </div>
    </x-progress-bar.step3>
</div>
