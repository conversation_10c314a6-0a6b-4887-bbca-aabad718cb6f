@props([
    'addNewLink' => '/calculate',
])

@php
  /** @var $processes array<\App\Models\OffsetProcess> */

  $selectedProcessId = App\Models\User::getSelectedProcessId();

  if ($selectedProcessId) {
      $selectedProcess = collect($processes)->first(fn($p) => $p->id == $selectedProcessId);
  } else {
     $selectedProcess = $selectedProcess ?? collect($processes)->first();
  }

  // Ensure we have a default process for initial state
  $defaultProcess = $selectedProcess ?? collect($processes)->first();
  $initialProcessId = $defaultProcess ? $defaultProcess->id : null;
  $collection = collect($processes);
  $jsonProcesses = $collection->map(function ($process) {
    return $process->toArray();
  })->toJson();
@endphp

<div x-data="{
    open: false,
    selectedProcessId: '{{ $initialProcessId }}',
    processes: {{ $jsonProcesses}},
    selectedEmissions: {{ $selectedProcess?->calculateTotalEmissions() ?? 1 }}, // Default to 1 if no process or emissions
    init() {
        // Initialize the store with the default/initial process
        const initialProcess = this.getSelectedProcess();
        Alpine.store('selectedProcess', {
            name: initialProcess?.name ?? 'Default Footprint',
            total_emissions: initialProcess?.total_emissions ?? 1
        });
        // Call updateQuantity directly to ensure store is updated and event dispatched if needed
        this.updateQuantity();
    },
    setSelectedProcess(processId) {
        this.selectedProcessId = processId;
        document.cookie = `selected_process_id=${processId}; path=/`; //persist the selection
        this.updateQuantity();
    },
    getSelectedProcess() {
        return this.processes.find(p => p.id == this.selectedProcessId);
    },
    updateQuantity() {
        const process = this.getSelectedProcess();
        this.selectedEmissions = process?.total_emissions ?? 1; // Use 1 if no process or emissions
        // Update the Alpine store with the selected process details
        Alpine.store('selectedProcess', {
            name: process?.name ?? 'Default Footprint',
            total_emissions: this.selectedEmissions // Already calculated with default
        });

        // Dispatch event with the new quantity (keep for compatibility if needed by other components)
        this.$dispatch('process-selected', { quantity: this.selectedEmissions });

        // Update step in another store (keeping existing logic)
        const stepNumber = parseInt(process?.step?.step_number) || 1;
        Alpine.store('process').setStep(stepNumber); // Assuming 'process' store is for step tracking
    }
}" x-cloak>
    @if (!empty($processes))
        <div class="relative">
            <div class="flex gap-1 items-center">
                <div class="flex gap-1 items-center justify-center bg-ox-green-400 px-4 py-1 rounded-md">
                    @svg("main_carbon_footprint", "fill-ox-green-600")
                    <span class="text-ox-green-600 whitespace-nowrap">
                        <b>
                            <span x-text="selectedEmissions"></span>
                            t CO<sub>2</sub>e
                        </b>
                    </span>
                </div>
                <button @click="open = !open" class="w-[280px] h-[42px] border-2 border-ox-green-200 py-1 px-3 rounded-md">
                    <div class="flex justify-between items-center gap-2">
                        <template x-if="!selectedProcessId">
                            <span class="flex items-center">{{ $defaultProcess?->name }}</span>
                        </template>
                        <template x-if="selectedProcessId">
                            <span class="flex items-center truncate" x-text="getSelectedProcess().name"></span>
                        </template>
                        <div class="transform transition-transform" :class="{ 'rotate-180': open }">
                            @svg('general_arrow_head', 'w-[18px] h-auto')
                        </div>
                    </div>
                </button>
            </div>

            <div x-show="open" @click.away="open = false"
                class="absolute bg-white border-2 border-ox-green-200 w-[500px] max-w-[90vw] z-[9999] mt-2 rounded-md hidden"
                :class="{ 'hidden': !open }">
                <div class="flex flex-col h-full max-h-[min(400px,80vh)] gap-2 p-4 overflow-y-auto">
                    <div class="flex flex-col gap-2">
                        @foreach ($processes as $process)
                            <x-process-selector.calculation-item
                                :process="$process"
                                :is-selected="$process->id == $selectedProcess?->id"
                            />
                        @endforeach
                    </div>
                    <a href="{{ $addNewLink }}" class="flex gap-2 items-center justify-center">
                        @svg('general_plus', "w-6 h-6")
                        {{ __('Add new carbon footprint') }}
                    </a>
                </div>
            </div>
        </div>
    @else
        <a href="{{ $addNewLink }}" class="flex gap-1 items-center">
            <x-button variant="green" style="filled" class="!rounded-md w-8 h-8 !p-0">
                <div class="flex items-center">
                    @svg('general_plus', 'w-6 h-6')
                </div>
            </x-button>
            <div class="bg-white p-2 px-4 rounded-md justify-between whitespace-nowrap">
                {{ __('Add new carbon footprint') }}
            </div>
        </a>
    @endif
</div>
