@props([
    'min' => 0,
    'max' => 10000,
    'stepSize' => 1,
    'initialMin' => null,
    'initialMax' => null,
])


<div x-data="range({{ $min }}, {{ $max }}, {{ $stepSize }})" 
     x-init="$nextTick(() => {
        @if($initialMin !== null)
            minValue = {{ $initialMin }};
        @endif
        
        @if($initialMax !== null)
            maxValue = {{ $initialMax }};
        @endif
        
        if (minValue !== {{ $min }} || maxValue !== {{ $max }}) {
            mintrigger(false);
            maxtrigger(false);
        }
     })" 
     {{ $attributes->merge(['class' => 'flex flex-col space-y-8']) }}>
    <div class="flex w-full space-x-1">
        <div class="w-full">
            <input type="text" maxlength="5" x-on:input="minInputTrigger($event.target.value)"
                :value="minValue <= min ? '' : minValue" placeholder="Min"
                class="w-full px-3 py-1 border-2 border-ox-green-200 rounded-lg text-start focus:border-ox-green-400 focus:ring focus:ring-ox-green-200 focus:ring-opacity-50">
        </div>
        <div class="w-full">
            <input type="text" maxlength="5" x-on:input="maxInputTrigger($event.target.value)"
                :value="maxValue >= max ? '' : maxValue" placeholder="Max"
                class="w-full px-3 py-1 border-2 border-ox-green-200 rounded-lg text-start focus:border-ox-green-400 focus:ring focus:ring-ox-green-200 focus:ring-opacity-50">
        </div>
    </div>

    <div class="relative max-w-full w-full">
        <div>
            <input type="range" :step="stepSize" x-bind:min="min" x-bind:max="max"
                x-on:input="mintrigger" x-model.number="minValue"
                class="absolute pointer-events-none appearance-none z-20 h-1 w-full opacity-0 cursor-pointer">

            <input type="range" :step="stepSize" x-bind:min="min" x-bind:max="max"
                x-on:input="maxtrigger" x-model.number="maxValue"
                class="absolute pointer-events-none appearance-none z-20 h-1 w-full opacity-0 cursor-pointer">

            <div class="relative z-10 h-0.5">
                <div class="absolute z-10 left-0 right-0 bottom-0 top-0 rounded-md bg-ox-black"></div>
                <div class="absolute z-20 top-0 bottom-0 rounded-md bg-ox-green-400"
                    x-bind:style="'right:' + maxThumb + '%; left:' + minThumb + '%'"></div>

                <!-- Min Thumb with Label -->
                <div class="absolute z-30 w-4 h-4 top-0 -mt-1.5" style="margin-left: -8px;"
                    x-bind:style="'left: ' + minThumb + '%'">
                    <div class="relative">
                        <span
                            class="absolute -top-5 left-1/2 transform -translate-x-1/2 font-semibold text-ox-black text-xs"
                            x-text="minValue <= min ? 'Min' : minValue"></span>
                        <div class="w-4 h-4 bg-ox-green-400 rounded-full"></div>
                    </div>
                </div>

                <!-- Max Thumb with Label -->
                <div class="absolute z-30 w-4 h-4 top-0 -mt-1.5" style="margin-right: -8px;"
                    x-bind:style="'right: ' + maxThumb + '%'">
                    <div class="relative">
                        <span
                            class="absolute -top-5 left-1/2 transform -translate-x-1/2 font-semibold text-ox-black text-xs"
                            x-text="maxValue >= max ? 'Max' : maxValue"></span>
                        <div class="w-4 h-4 bg-ox-green-400 rounded-full"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    input[type=range]::-webkit-slider-thumb {
        pointer-events: all;
        width: 16px;
        height: 16px;
        -webkit-appearance: none;
        cursor: pointer;
    }

    input[type=range]::-moz-range-thumb {
        pointer-events: all;
        width: 16px;
        height: 16px;
        cursor: pointer;
    }

    input[type=range]::-ms-thumb {
        pointer-events: all;
        width: 16px;
        height: 16px;
        cursor: pointer;
    }
</style>

@script
    <script>
        Alpine.data('range', (min, max, stepSize) => ({
            minValue: min,
            maxValue: max,
            min: min,
            max: max,
            stepSize: stepSize,
            minThumb: 0,
            maxThumb: 0,

            minInputTrigger(value) {
                if (value === '') {
                    this.minValue = this.min;
                } else {
                    const numValue = parseInt(value);
                    if (!isNaN(numValue)) {
                        this.minValue = numValue;
                    }
                }
                this.mintrigger();
            },

            maxInputTrigger(value) {
                if (value === '') {
                    this.maxValue = this.max;
                } else {
                    const numValue = parseInt(value);
                    if (!isNaN(numValue)) {
                        this.maxValue = numValue;
                    }
                }
                this.maxtrigger();
            },

            mintrigger(dispatch = true) {
                this.minValue = Math.max(this.min, Math.min(this.minValue, this.maxValue));
                this.minValue = Math.round(this.minValue / this.stepSize) * this.stepSize;
                this.minThumb = ((this.minValue - this.min) / (this.max - this.min)) * 100;
                if (dispatch) {
                    this.$dispatch('min-change', this.minValue);
                }
            },

            maxtrigger(dispatch = true) {
                this.maxValue = Math.min(this.max, Math.max(this.maxValue, this.minValue));
                this.maxValue = Math.round(this.maxValue / this.stepSize) * this.stepSize;
                this.maxThumb = 100 - (((this.maxValue - this.min) / (this.max - this.min)) * 100);
                console.log("change max");
                if (dispatch) {
                    this.$dispatch('max-change', this.maxValue);
                }
            }
        }));
    </script>
@endscript
