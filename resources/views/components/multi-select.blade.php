@props([
    'options' => [],
    'name' => '',
    'placeholder' => 'Select options',
    'title' => 'Select options:',
    'initialValue' => [],
    'onChange' => null
])

@script
<script>
    Alpine.data('multiSelect', (name, options, initialValue, onChange) => ({
        open: false,
        search: '',
        options: options,
        selectedOptions: initialValue,
        selectedLabels: initialValue.length ? options
            .filter(opt => initialValue.includes(opt.value))
            .map(opt => opt.label) : [],

        get filteredOptions() {
            const options = [...this.options]; // Create a copy of options array
            
            // Always sort selected items first
            return options.sort((a, b) => {
                const aSelected = this.selectedOptions.includes(a.value);
                const bSelected = this.selectedOptions.includes(b.value);
                if (aSelected && !bSelected) return -1;
                if (!aSelected && bSelected) return 1;
                return 0;
            });
        },

        shouldShowOption(option) {
            if (this.search === '') return true;
            return option.label.toLowerCase().includes(this.search.toLowerCase());
        },

        toggleOption(option) {
            const index = this.selectedOptions.indexOf(option.value);
            if (index === -1) {
                this.selectedOptions.push(option.value);
                this.selectedLabels.push(option.label);
            } else {
                this.selectedOptions.splice(index, 1);
                this.selectedLabels.splice(index, 1);
            }
            
            // Call the onChange handler if provided
            if (typeof onChange === 'function') {
                onChange(this.selectedOptions);
            }
        },

        isSelected(value) {
            return this.selectedOptions.includes(value);
        }
    }))
</script>
@endscript

<div x-data="multiSelect('{{ $name }}', {{ json_encode($options) }}, {{ json_encode($initialValue) }}, {{ $onChange }})" 
    {{ $attributes->merge(['class' => '']) }}>
    <div class="relative">
        @if($title)
            <label for="multi-select" class="block text-sm font-medium text-ox-black mb-1">{{ $title }}</label>
        @endif
        <div class="mt-1 relative">
            <button type="button" @click="open = !open"
                class="relative w-full bg-white border border-neutral-200/70 rounded-md shadow-sm pl-3 pr-10 py-2 text-left cursor-default focus:outline-none focus:ring-1 focus:ring-ox-green-400 focus:border-ox-green-400 text-sm">
                <span class="block truncate text-ox-black"
                    x-text="selectedOptions.length ? selectedLabels.join(', ') : '{{ $placeholder }}'"></span>
                <span class="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                    <svg class="h-5 w-5 text-ox-black" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"
                        fill="currentColor" aria-hidden="true">
                        <path fill-rule="evenodd"
                            d="M10 3a1 1 0 01.707.293l3 3a1 1 0 01-1.414 1.414L10 5.414 7.707 7.707a1 1 0 01-1.414-1.414l3-3A1 1 0 0110 3zm-3.707 9.293a1 1 0 011.414 0L10 14.586l2.293-2.293a1 1 0 011.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z"
                            clip-rule="evenodd" />
                    </svg>
                </span>
            </button>

            <div x-show="open" 
                x-cloak
                @click.away="open = false"
                x-transition:enter="transition ease-out duration-200"
                x-transition:enter-start="transform opacity-0 scale-95"
                x-transition:enter-end="transform opacity-100 scale-100"
                x-transition:leave="transition ease-in duration-75"
                x-transition:leave-start="transform opacity-100 scale-100"
                x-transition:leave-end="transform opacity-0 scale-95"
                class="absolute z-10 w-full bg-white shadow-lg rounded-md overflow-hidden border border-neutral-200/70"
                style="margin-top: 1px;">
                
                <div class="max-h-60 overflow-auto">
                    <div class="sticky top-0 z-20 px-2 py-2 bg-white border-b border-neutral-200/70">
                        <input
                            type="text"
                            class="w-full px-3 py-2 text-sm border border-neutral-200/70 rounded-md focus:outline-none focus:ring-1 focus:ring-ox-green-400 focus:border-ox-green-400 text-ox-black"
                            placeholder="Search options..."
                            x-model="search"
                            @click.stop
                        >
                    </div>
                    <template x-for="option in filteredOptions" :key="option.value">
                        <div @click="toggleOption(option)"
                            x-show="shouldShowOption(option)"
                            class="cursor-pointer select-none relative py-2 pl-3 pr-9 hover:bg-ox-green-100 text-ox-black">
                            <span x-text="option.label" 
                                :class="{ 'font-semibold text-ox-green-600': isSelected(option.value) }"
                                class="block truncate"></span>
                            <span x-show="isSelected(option.value)"
                                class="absolute inset-y-0 right-0 flex items-center pr-4 text-ox-green-600">
                                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"
                                    fill="currentColor">
                                    <path fill-rule="evenodd"
                                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                        clip-rule="evenodd" />
                                </svg>
                            </span>
                        </div>
                    </template>
                </div>
            </div>
        </div>
    </div>
</div>
