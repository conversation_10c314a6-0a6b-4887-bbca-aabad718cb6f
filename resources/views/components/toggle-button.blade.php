@props([
    'icon' => null,
    'title' => '',
])

<button 
    x-data="{ isActive: false }"
    x-on:click="isActive = !isActive"
    {{ $attributes->merge([
        'class' => 'flex items-center gap-2 px-4 py-2 rounded-md transition-colors duration-200'
    ]) }}
    x-bind:class="isActive ? 'bg-ox-green-400 text-ox-green-600' : 'hover:bg-ox-green-200'">
    @if ($icon)
        <img src="{{ $icon }}" alt="{{ $title }}" class="w-4 h-4" />
    @endif
    <span class="text-start">{{ $title }}</span>
</button>
