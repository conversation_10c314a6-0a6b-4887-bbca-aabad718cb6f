@props(['user'])

@php
  /** @var $user \App\Models\User */
@endphp
<div x-data="{ isOpen: false }" class="relative">
    <x-button
        variant="green"
        style="filled"
        @click="isOpen = !isOpen"
        @click.away="isOpen = false"
        class="flex items-center justify-center h-10 w-10 !rounded-full !p-0"
    >
        @svg('general_contact', ["class" => "w-[1.875rem] h-[1.875rem] fill-ox-green-600"])
    </x-button>

    <div
        x-show="isOpen"
        x-transition:enter="transition ease-out duration-200"
        x-transition:enter-start="opacity-0 scale-95"
        x-transition:enter-end="opacity-100 scale-100"
        x-transition:leave="transition ease-in duration-150"
        x-transition:leave-start="opacity-100 scale-100"
        x-transition:leave-end="opacity-0 scale-95"
        class="absolute right-0 mt-2 w-64 bg-white rounded-md shadow-lg py-1 z-50"
        style="display: none;"
    >
        <div class="px-4 py-3 border-b border-gray-100 flex items-start gap-3">
            <img 
                src="{{ $user->avatar }}" 
                alt="{{ $user->name }}"
                class="w-10 h-10 rounded-full"
            />
            <div>
                <p class="text-sm font-medium text-gray-700">{{ $user->name }}</p>
                @if(!empty($user->roles))
                    <x-badge 
                        :color="$user->roles[0]?->getColor()" 
                        class="mt-1"
                    >
                        {{ $user->roles[0]?->getLabel() }}
                    </x-badge>
                @endif
            </div>
        </div>
        <a href="{{ home_url('/my-account') }}" class="block px-6 py-2 text-sm text-gray-700 hover:bg-gray-100">
            <i class="fas fa-user mr-2"></i> My Account
        </a>
        <a href="{{ home_url('/chat') }}" class="block px-6 py-2 text-sm text-gray-700 hover:bg-gray-100">
            <i class="fas fa-comments mr-2"></i> Chat
        </a>
        <a href="{{ wp_logout_url(home_url()) }}" class="block px-6 py-2 text-sm text-gray-700 hover:bg-gray-100">
            <i class="fas fa-sign-out-alt mr-2"></i> Logout
        </a>
    </div>
</div>
