@extends('layouts.app')

@vite('resources/css/product.css')

@section('content')
    @php
        $product = new App\Models\Product($post->ID);
        $is_compensation = $product->is_compensation_project();
        $is_consultation = $product->is_consultation();
    @endphp
    <div class="product-page ">
        {!! do_shortcode('[breadcrumbs_bar]') !!}
        
       <div class="mb-8">
     
        @if (session()->has('wc_cart_success'))
            @php
                $message = session()->get("wc_cart_success");
            @endphp
            <x-cart-message 
                type="success"
                message="{{ $message  }}"
            />
        @endif
        
        @if (session()->has('wc_cart_error'))
            @php
                $message = session()->get("wc_cart_error");
            @endphp
            <x-cart-message 
                type="error"
                message="{{ $message }}"
            />
        @endif
        </div>
        
        @if ($is_compensation)
            @include('partials.project-product')
        @elseif($is_consultation)
            @include('partials.consultation-product')
        @endif
    </div>
@endsection

@push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Get the main image element
            const mainImage = document.querySelector('.main-image img');

            // Initialize Swiper with proper navigation configuration
            const thumbnailSwiper = new Swiper('.thumbnailSwiper', {
                slidesPerView: 4,
                spaceBetween: 16,
                watchSlidesProgress: true,
                navigation: {
                    nextEl: '.swiper-button-next',
                    prevEl: '.swiper-button-prev',
                },
                // Enable loop mode for continuous sliding
                loop: true,
                // Add slideToClickedSlide for better UX
                slideToClickedSlide: true,
                breakpoints: {
                    320: {
                        slidesPerView: 2,
                        spaceBetween: 8
                    },
                    480: {
                        slidesPerView: 3,
                        spaceBetween: 12
                    },
                    768: {
                        slidesPerView: 4,
                        spaceBetween: 16
                    }
                }
            });

            // Update thumbnail active states and main image
            thumbnailSwiper.on('slideChange', function() {
                const activeSlide = thumbnailSwiper.slides[thumbnailSwiper.activeIndex];
                const thumbnailImage = activeSlide.querySelector('.thumbnail-image');

                if (thumbnailImage && mainImage) {
                    mainImage.src = thumbnailImage.src;
                }

                // Update active states
                document.querySelectorAll('.thumbnail-slide').forEach(slide => {
                    slide.classList.remove('active');
                });
                activeSlide.querySelector('.thumbnail-slide').classList.add('active');
            });

            // Handle thumbnail click
            document.querySelectorAll('.swiper-slide').forEach(slide => {
                slide.addEventListener('click', function() {
                    const thumbnailImage = this.querySelector('.thumbnail-image');
                    if (thumbnailImage && mainImage) {
                        mainImage.src = thumbnailImage.src;
                    }

                    // Update active state
                    document.querySelectorAll('.thumbnail-slide').forEach(slide => {
                        slide.classList.remove('active');
                    });
                    this.querySelector('.thumbnail-slide').classList.add('active');
                });
            });

            // Carbon footprint calculation script
            const hiddenValue = document.getElementById('hiddenValue');
            const input = document.getElementById('length_needed');

            if (input && hiddenValue) {
                wp.data.subscribe(function() {
                    const process = wp.data.select('compensationStore').getSelectedProcess();
                    const carbonFootprint = process && process.calculation && process.calculation
                        .carbonFootprint ? process.calculation.carbonFootprint : 50;

                    // Round the carbon footprint to the nearest whole number (tonne)
                    const roundedFootprint = (carbonFootprint < 1) ? 1 : Math.round(carbonFootprint);
                    input.value = roundedFootprint;
                    hiddenValue.innerHTML = roundedFootprint;
                });

                input.addEventListener('input', () => {
                    hiddenValue.innerHTML = input.value;
                });
            }
        });
    </script>
@endpush
