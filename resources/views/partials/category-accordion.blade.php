@php
    global $post;
    $is_current_post_in_category = in_category($category->term_id, $post);
    
    // Get posts for this category
    $posts = get_posts([
        'category' => $category->term_id,
        'orderby' => 'title',
        'order' => 'ASC',
        'posts_per_page' => -1,
        'post_status' => 'publish'
    ]);
@endphp

<div class="secondary-category">
    <input 
        type="checkbox" 
        id="category-{{ $category->slug }}" 
        class="accordion-toggle" 
        {{ $is_current_post_in_category ? 'checked' : '' }}
    >
    <label for="category-{{ $category->slug }}" class="accordion-label">
        {{ esc_html($category->name) }}
    </label>
    
    <div class="accordion-content">
        @if($posts)
            <ul class="posts-list">
                @foreach($posts as $post_item)
                    @php
                        $is_current_post = ($post_item->ID == $post->ID);
                        $highlight_class = $is_current_post ? 'current-post-item' : '';
                    @endphp
                    <li class="post-item {{ $highlight_class }}">
                        <a href="{{ get_permalink($post_item) }}">
                            {{ get_the_title($post_item) }}
                        </a>
                    </li>
                @endforeach
            </ul>
        @else
            <p>{{ $isGeneral ? 'No posts found in General category.' : 'No posts found.' }}</p>
        @endif
    </div>
</div> 