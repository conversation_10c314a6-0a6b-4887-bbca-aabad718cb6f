<div class="consultation-service">
    <style>
        .consultation-service .left-column > section {
            margin-bottom: 0;
        }
        .consultation-service .left-column h2 {
            margin-bottom: 0;
        }
    </style>
    {{-- Two Column Layout Container --}}
    <div class="product-header-grid">
        {{-- Left Column - Images --}}
        <div class="image-column">
            <div class="product-images">
                {{-- Main Image --}}
                <div class="main-image">
                    @if (!empty($product->images))
                        <img src="{{ $product->images[0] }}" alt="{{ $product->name }}" class="featured-image">
                    @endif
                </div>
            </div>
        </div>

        {{-- Right Column - Product Info --}}
        <div class="info-column">
            {{-- Status Badges --}}
            <div class="status-badges {{ $is_consultation ? 'invisible lg:visible lg:opacity-0' : '' }}">
                @if ($product->get_standard())
                    <div class="badge active">
                        <img src="/wp-content/uploads/2024/03/certificate_icon.svg" alt="Standard" width="16"
                            height="16" />
                        <span>{{ __('Standard') }}: {{ $product->get_standard() }}</span>
                    </div>
                @endif
            </div>

            {{-- Title and Description --}}
            <h1 class="product-title">{{ $product->name }}</h1>
            <div class="product-description">
                {!! $product->short_description !!}
            </div>

            {{-- Project Meta --}}
            <div class="project-meta">
                {{-- Info --}}
                <div class="info">
                    <div class="info-item">
                        <strong class="w-32">{{ __('Company', CO2MARKET_TEXT_DOMAIN) }}</strong>
                        <span class="greentext">{{ $product->get_company_name() }}</span>
                    </div>
                    <div class="info-item lower-item">
                        <strong class="w-32">Service category</strong>
                        <span class="greentext">
                            @php
                                $categories = strip_tags(wc_get_product_category_list($product->get_id()));
                                $categories = explode(',', $categories);
                                $filtered_categories = array_filter($categories, function ($cat) {
                                    return trim($cat) !== 'Consulting service';
                                });
                                echo implode(', ', $filtered_categories);
                            @endphp
                        </span>
                    </div>
                    @if ($product->can_ask_for_offer())
                        <a href="#" class="ask-for-offer-btn popmake-1077 mt-6 flex items-center justify-center px-4 py-2 rounded-md text-sm font-normal w-fit border-2 border-ox-green-400 text-ox-green-600 bg-transparent hover:bg-ox-green-200 transition-all">
                            Ask for offer
                        </a>
                    @endif
                </div>
            </div>
        </div>
    </div>

    {{-- Price Bar --}}
    @php
        $has_price = !empty($product->get_price()) && $product->get_price() !== '';
        $purchase_type = $product->get_purchase_type() ?: 'Ask for offer';
    @endphp
    <div class="price-bar {{ !$has_price ? 'price-bar--no-price' : '' }}">
        <div class="left-price-bar">
            <div class="amount-box">
                @if ($is_consultation)
                    @if ($product->get_time_estimate())
                    <p class="consultation-time-estimate">
                        <span class="consultation-time-estimate-time">Time</span>
                        <span class="consultation-time-estimate-estimate">estimate:</span>
                    </p>
                    @endif
                @else
                    <p class="available-amount">{{ __('Available tonnes') }}:</p>
                @endif
                <div class="underline-value">
                    @if ($is_consultation)
                        @if ($product->get_time_estimate())
                        <p class="stock-quantity">{{ $product->get_time_estimate() }}</p>
                        @endif
                    @else
                        @php
                            $stock_quantity = $product->get_stock_quantity();
                            $low_stock_amount = get_option('woocommerce_notify_low_stock_amount');

                            if ($stock_quantity <= 0) {
                                echo '<p class="out-of-stock">' . __('Out of Stock') . '</p>';
                            } elseif ($stock_quantity <= $low_stock_amount) {
                                echo '<p class="low-stock">' . number_format($stock_quantity) . ' t CO₂e</p>';
                            } else {
                                echo '<p class="stock-quantity">' . number_format($stock_quantity) . ' t CO₂e</p>';
                            }
                        @endphp
                    @endif
                </div>
            </div>
            <div class="tonne-price-box">
                <p class="tonne-price">{{ __('Type') }}:</p>
                <div class="underline-value">
                    <p class="pricebar-value-p">
                        @if ($purchase_type === 'Buy now')
                            {{ __('Purchase now') }}
                        @elseif($purchase_type === 'Ask for offer')
                            {{ __('Ask for offer') }}
                        @else
                            {{ __('Inactive') }}
                        @endif
                    </p>
                </div>
            </div>
        </div>

        @if ($purchase_type === 'Buy now' && $has_price)
            <form class="cart" action="{{ $product->get_permalink() }}" method="post" enctype="multipart/form-data">
                <div class="flex items-center justify-end gap-4 p-1">
                    <div class="pricebar-value-p">{!! $product->get_price_html() !!}</div>
                    <button type="submit" name="add-to-cart" value="{{ $product->get_id() }}"
                        class="single_add_to_cart_button">
                        <img src="/wp-content/uploads/2024/02/cart_icon.svg" alt="" width="18"
                            height="18" />
                        <p>{{ __('Add to cart') }}</p>
                    </button>
                </div>
            </form>
        @endif
    </div>

    {{-- Main content grid --}}
    <div class="product-content-grid">
        {{-- Left column --}}
        <div class="left-column">
            {{-- Service Information --}}
            <section class="project-type">
                <h2>Service information</h2>
            </section>

            {{-- Service Details --}}
            <section class="project-details">
                <div class="detail-row">
                    <div class="icon-column">
                        {!! str_replace(
                            '<svg',
                            '<svg data-filename="main_get_footprint.svg"',
                            file_get_contents(resource_path('svg/main_get_footprint.svg')),
                        ) !!}
                    </div>
                    <div class="content-column">
                        <h2 class="small-header">Service category</h2>
                        <p>
                            @php
                                $categories = strip_tags(wc_get_product_category_list($product->get_id()));
                                $categories = explode(',', $categories);
                                $filtered_categories = array_filter($categories, function ($cat) {
                                    return trim($cat) !== 'Consulting service';
                                });
                                echo implode(', ', $filtered_categories);
                            @endphp
                        </p>
                    </div>
                </div>
                @if ($product->get_service_id())
                    <div class="detail-row">
                        <div class="icon-column">
                            {!! file_get_contents(resource_path('svg/general_id.svg')) !!}
                        </div>
                        <div class="content-column">
                            <h2 class="small-header">Service ID</h2>
                            <p>{{ $product->get_service_id() }}</p>
                        </div>
                    </div>
                @endif
                @if ($product->get_consultant_id())
                    <div class="detail-row">
                        <div class="icon-column">
                            {!! file_get_contents(resource_path('svg/general_id.svg')) !!}
                        </div>
                        <div class="content-column">
                            <h2 class="small-header">Consultant ID</h2>
                            <p>{{ $product->get_consultant_id() }}</p>
                        </div>
                    </div>
                @endif
                @if ($product->get_company_name())
                    <div class="detail-row">
                        <div class="icon-column">
                            {!! file_get_contents(resource_path('svg/general_organization.svg')) !!}
                        </div>
                        <div class="content-column">
                            <h2 class="small-header">{{ __('Company', CO2MARKET_TEXT_DOMAIN) }}</h2>
                            <p>{{ $product->get_company_name() }}</p>
                        </div>
                    </div>
                @endif
                @if ($product->get_standard())
                    <div class="detail-row">
                        <div class="icon-column">
                            {!! str_replace(
                                '<svg',
                                '<svg data-filename="general_certification.svg"',
                                file_get_contents(resource_path('svg/general_certification.svg')),
                            ) !!}
                        </div>
                        <div class="content-column">
                            <h2 class="small-header">
                                {{ str_contains($product->get_standard(), ',') ? 'Standards' : 'Standard' }}</h2>
                            <p>{{ $product->get_standard() }}</p>
                        </div>
                    </div>
                @endif
            </section>
        </div>

        {{-- Right column --}}
        <div class="right-column">
            {{-- About this service --}}
            <section class="about-project">
                <h3>About this service</h3>
                <div class="content">
                    {!! $product->get_description() !!}
                </div>
            </section>
        </div>
    </div>
</div>
