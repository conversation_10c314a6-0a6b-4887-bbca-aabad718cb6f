@php
    // Fetch and sort categories
    $categories = get_the_category();
    $priority_slugs = ['get-footprint', 'offset', 'communicate'];

    // Sort categories to prioritize specific slugs
    usort($categories, function ($a, $b) use ($priority_slugs) {
        $a_priority = in_array($a->slug, $priority_slugs) ? 0 : 1;
        $b_priority = in_array($b->slug, $priority_slugs) ? 0 : 1;
        return $a_priority - $b_priority ?: $a->term_id <=> $b->term_id;
    });

    // Get sharing URLs
    $url_to_share = get_permalink();
    $title_to_share = get_the_title();
@endphp

<article {!! post_class('h-entry') !!}>
  <div class="article-background"></div>
  <div class="article-container">
    <header>
      <div class="article-header-grid">
        {{-- Left Column - Featured Image --}}
        <div class="article-image-column">
          @if(has_post_thumbnail())
            <div class="post-thumb-img-content">
              {!! get_the_post_thumbnail() !!}
            </div>
          @endif
        </div>

        {{-- Right Column - Content --}}
        <div class="article-content-column">
          {{-- Categories --}}
          <div class="entry-meta">
            @foreach($categories as $category)
              @if(!in_array($category->slug, ['uncategorized', 'another-slug-to-exclude']))
                <a href="{{ esc_url(get_category_link($category->term_id)) }}" class="ast-terms-link">
                  {{ esc_html($category->name) }}
                </a>
              @endif
            @endforeach
          </div>

          {{-- Title --}}
          <h2 class="p-name">
            {!! $title !!}
          </h2>

          {{-- Social Share Icons --}}
          <div class="entry-meta-icons-grid">
            {{-- Facebook Share Button --}}
            <div class="entry-meta-icon-column">
              <a href="https://www.facebook.com/sharer/sharer.php?u={{ urlencode($url_to_share) }}" target="_blank">
                {!! file_get_contents(resource_path('svg/some/facebook_icon.svg')) !!}
              </a>
            </div>

            {{-- Twitter/X Share Button --}}
            <div class="entry-meta-icon-column">
              <a href="https://twitter.com/intent/tweet?url={{ urlencode($url_to_share) }}&text={{ urlencode($title_to_share) }}" target="_blank">
                {!! file_get_contents(resource_path('svg/some/x_icon.svg')) !!}
              </a>
            </div>

            {{-- Copy Link Button --}}
            <div class="entry-meta-icon-column" onclick="copyToClipboard()" style="cursor:pointer;">
              Share
            </div>
          </div>
        </div>
      </div>
    </header>

    <div class="article-content-layout">
      {{-- Left Column - Search and Categories --}}
      <div class="article-sidebar">
        {{-- Article Search Form --}}
        <div class="sidebar-search">
          {!! do_shortcode('[ivory-search id="19265" title="Article search form"]') !!}
        </div>

        {{-- Category List Section --}}
        <div class="posts-category-container">
          @php
            $all_categories = get_categories(['hide_empty' => false]);

            // Display primary categories
            foreach ($priority_slugs as $slug) {
              $primary_category = get_category_by_slug($slug);
              if ($primary_category) {
                echo "<h2>" . esc_html($primary_category->name) . "</h2>";

                // Display secondary categories
                foreach ($all_categories as $category) {
                  if ($category->parent === $primary_category->term_id) {
                    echo view('partials.category-accordion', [
                      'category' => $category,
                      'isGeneral' => false
                    ])->render();
                  }
                }
              }
            }

            // Display General section
            echo "<h2>General</h2>";
            foreach ($all_categories as $category) {
              if (!in_array($category->slug, $priority_slugs) && $category->parent == 0) {
                echo view('partials.category-accordion', [
                  'category' => $category,
                  'isGeneral' => true
                ])->render();
              }
            }
          @endphp
        </div>
      </div>

      {{-- Right Column - Main Content --}}
      <div class="article-main-content">
        <div class="e-content post-content-wrapper">
          {!! the_content() !!}
        </div>
      </div>
    </div>

    @if ($pagination)
      <footer>
        <nav class="page-nav" aria-label="Page">
          {!! $pagination !!}
        </nav>
      </footer>
    @endif

    @if (!is_singular('post'))
      @php(comments_template())
    @endif
  </div>
</article>

<script>
function copyToClipboard() {
  navigator.clipboard.writeText('{{ get_permalink() }}').catch(function(err) {
    console.error('Could not copy text: ', err);
  });
}
</script>
