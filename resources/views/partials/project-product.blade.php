   <div class="compensation-project">
       {{-- Breadcrumbs --}}

       {{-- Two Column Layout Container --}}
       <div class="product-header-grid">
           {{-- Left Column - Images --}}
           <div class="image-column">
               <div class="product-images">
                   {{-- Main Image --}}
                   <div class="main-image">
                       @if (!empty($product->images))
                           <img src="{{ $product->images[0] }}" alt="{{ $product->name }}" class="featured-image">
                       @endif
                   </div>
               </div>
           </div>

           {{-- Right Column - Product Info --}}
           <div class="info-column">
               {{-- Status badges --}}
               <div class="status-badges {{ $is_consultation ? 'invisible lg:visible lg:opacity-0' : '' }}">
                   @php
                       $is_active = get_field('active');
                   @endphp
                   @if ($is_active)
                       <div class="badge active">
                           @svg('alert_checkmark', 'w-4 h-4')
                           <span class="text-ox-green-600 font-normal">{{ __('Active') }}</span>
                       </div>
                   @else
                       <div class="badge not-active">
                           <img src="/wp-content/uploads/2024/04/not_active_icon.svg" alt="" width="16"
                               height="16" />
                           <span class="text-yellow-600 font-normal">{{ __('Inactive') }}</span>
                       </div>
                   @endif

                   @if ($product->get_certificate())
                       <div class="badge certified">
                           <img src="/wp-content/uploads/2024/03/certificate_icon.svg" alt="" width="16"
                               height="16" />
                           <span class="font-normal">{{ __('Certified by') }} {{ $product->get_certificate() }}</span>
                       </div>
                   @endif
               </div>

               {{-- Short description --}}
               <div class="product-description">
                   {{-- Title --}}
                   <h1 class="product-title">{{ $product->name }}</h1>

                   {!! $product->get_short_description() !!}
               </div>
           </div>
       </div>

       {{-- Secondary Grid Container --}}
       <div class="product-secondary-grid">
           {{-- Left Column --}}
           <div class="secondary-left-column">
               {{-- Thumbnail Carousel --}}
               @if (count($product->images) > 1)
                   <div class="product-thumbnails-carousel">
                       <div class="swiper thumbnailSwiper">
                           <div class="swiper-wrapper">
                               @foreach ($product->images as $index => $image)
                                   <div class="swiper-slide">
                                       <div class="thumbnail-slide {{ $index === 0 ? 'active' : '' }}">
                                           <img src="{{ $image }}"
                                               alt="{{ $product->name }} - Image {{ $index + 1 }}"
                                               data-index="{{ $index }}" class="thumbnail-image">
                                       </div>
                                   </div>
                               @endforeach
                           </div>
                           <div class="swiper-button-next"></div>
                           <div class="swiper-button-prev"></div>
                       </div>
                   </div>
               @endif
           </div>
           {{-- Right Column --}}
           <div class="secondary-right-column">
               {{-- Project score and metadata --}}
               <div class="project-meta">
                   <div class="score-container">
                       <div class="score-circle" style="--score: {{ $product->get_score() ?? 0 }}%">
                           <span class="score">{{ $product->get_score() ? $product->get_score() : '-' }}</span>
                       </div>
                       @if ($product->can_ask_for_offer())
                           <a href="#" class="ask-for-offer-btn popmake-1077 mt-4 text-ox-green-600">
                               Ask for offer
                           </a>
                       @endif
                   </div>
                   <div class="info">
                       <div class="info-item">
                           <strong>{{ __('Organization') }}</strong>
                           <p class="greentext">{{ $product->get_project_provider() }}</p>
                       </div>
                       <div class="info-item lower-item">
                           <strong>{{ __('Project type') }}</strong>
                           <p class="greentext">
                               @foreach ($product->get_project_types() as $type)
                                   {{ $type['name'] }} {{ $type['description'] }}<br>
                               @endforeach
                           </p>
                       </div>
                       <div class="info-icons">
                           <ul class="sdgs-container">
                               @php
                                   $sdgs = $product->get_sdgs();
                                   $identified_sdgs = [];
                                   $other_sdgs = false;

                                   foreach ($sdgs as $sdg) {
                                       if (!empty($sdg['icon']) && strtolower($sdg['name']) !== 'other') {
                                           $identified_sdgs[] = $sdg;
                                       } else {
                                           $other_sdgs = true;
                                       }
                                   }
                               @endphp

                               @foreach ($identified_sdgs as $sdg)
                                   <li class="sdg-image" title="SDG {{ $sdg['id'] }}: {{ $sdg['name'] }}">
                                       <img src="{{ $sdg['icon'] }}"
                                           alt="SDG {{ $sdg['id'] }}: {{ $sdg['name'] }}" width="32"
                                           height="32" loading="lazy" />
                                   </li>
                               @endforeach

                               @if ($other_sdgs)
                                   <li class="sdg-image" title="Other SDGs">
                                       <img src="{{ @Vite::svg('sdg/sdg_placeholder_icon.svg') }}" alt="Other SDGs"
                                           width="32" height="32" loading="lazy" />
                                   </li>
                               @endif
                           </ul>
                       </div>
                   </div>
               </div>
           </div>
       </div>

       {{-- Purchase section --}}
       @php
           $has_price = $product->get_price() && $product->get_price() !== '';
       @endphp
       <div class="price-bar {{ !$has_price ? 'price-bar--no-price' : '' }}">
           <div class="left-price-bar">
               @if ($has_price)
                   <div class="tonne-price-box">
                       <p class="tonne-price">{{ __('Price per metric tonne') }}:</p>
                       <div class="underline-value">
                           <p class="pricebar-value-p">{!! $product->get_price_html() !!}</p>
                       </div>
                   </div>
               @endif
               @if ($product->managing_stock())
                   <div class="amount-box">
                       @if ($is_consultation)
                           <p class="consultation-time-estimate">
                               <span class="consultation-time-estimate-time">Time</span>
                               <span class="consultation-time-estimate-estimate">estimate:</span>
                           </p>
                       @else
                           <p class="available-amount">{{ __('Available tonnes') }}:</p>
                       @endif
                       <div class="underline-value">
                           @if ($is_consultation)
                               <p class="stock-quantity">{{ $product->get_time_estimate() }}</p>
                           @else
                               @php
                                   $stock_quantity = $product->get_stock_quantity();
                                   $low_stock_amount = get_option('woocommerce_notify_low_stock_amount');

                                   if ($stock_quantity <= 0) {
                                       echo '<p class="out-of-stock">' . __('Out of Stock') . '</p>';
                                   } elseif ($stock_quantity <= $low_stock_amount) {
                                       echo '<p class="low-stock">' . number_format($stock_quantity) . ' t CO₂e</p>';
                                   } else {
                                       echo '<p class="stock-quantity">' .
                                           number_format($stock_quantity) .
                                           ' t CO₂e</p>';
                                   }
                               @endphp
                           @endif
                       </div>
                   </div>
               @endif
           </div>

           @if ($has_price)
               <form
                   class="cart"
                   action="{{ $product->get_permalink() }}"
                   method="post"
                   enctype="multipart/form-data"
                   x-data="{ quantity: 50 }" {{-- Initialize quantity --}}
                   @process-selected.window="quantity = $event.detail.quantity" {{-- Listen for event --}}
               >
                   <div id="price_calculator" class="wc-measurement-price-calculator-price-table">
                       <div class="price-table-grid">
                           {{-- Top row - left column --}}
                           <div class="price-table-content">
                               <div class="quantity-input-container">
                                   <input type="number" name="quantity" id="length_needed" class="amount_needed"
                                       min="1" step="1" x-model="quantity"> {{-- Bind value to Alpine state --}}
                                   <div class="units">
                                       {{-- Keep hidden span if needed for other JS, otherwise can remove --}}
                                       <span class="invisible" id="hiddenValue" x-text="quantity"></span>
                                       <span class="units-value">t CO₂e</span>
                                   </div>
                               </div>
                           </div>

                           {{-- Top row - right column --}}
                           <button type="submit" name="add-to-cart" value="{{ $product->get_id() }}"
                               class="single_add_to_cart_button">
                               <img src="/wp-content/uploads/2024/02/cart_icon.svg" alt="" width="18"
                                   height="18" />
                               <p>{{ __('Add to cart') }}</p>
                           </button>

                           {{-- Bottom row - left column --}}
                           <div id="purchase-type-container" class="disclaimer">
                               <div class="purchase-type">
                                   <p id="purchase-type-text">This is a {{ $product->get_purchase_type() }}. Read more
                                   </p>
                                   <div class="question-circle"><span>i</span></div>
                                   <span class="disc-tooltip">
                                       Lorem ipsum dolor sit amet, consectetur adipiscing elit.
                                   </span>
                               </div>
                           </div>

                           {{-- Bottom row - right column --}}
                           <div class="subscription-checkbox">
                               <label for="subscription_checkbox">
                                   <input type="checkbox" name="subscription_checkbox" id="subscription_checkbox" />
                                   <p>{{ __('Yearly Subscription') }}</p>
                               </label>
                           </div>
                       </div>
                   </div>
               </form>
           @endif
       </div>

       {{-- Main content grid --}}
       <div class="product-content-grid">
           {{-- Left column --}}
           <div class="left-column">
               {{-- Project type --}}
               <section class="project-type">
                   <h2>Project type</h2>
                   @if ($product->get_project_types())
                       <div class="type-card">
                           @foreach ($product->get_project_types() as $type)
                               <div class="type-flex">
                                   <img src="{{ $type['icon'] }}" alt="{{ $type['name'] }}" width="33"
                                       height="33" class="rounded-md" />
                                   <div class="type-content">
                                       <h3>{{ $type['name'] }}</h3>
                                       <p>{{ $type['level2'] }}</p>
                                   </div>
                               </div>
                           @endforeach
                       </div>
                   @endif
               </section>

               {{-- Co-benefits --}}
               <section class="co-benefits">
                   <h2>Co-benefits</h2>
                   @foreach ($product->get_co_benefits() as $key => $benefit)
                       <div class="benefit-card">
                           {!! file_get_contents(resource_path("svg/benefits/{$key}.svg")) !!}
                           <div class="benefit-content">
                               <h3>{{ $benefit['label'] }}</h3>
                               <p>{{ $benefit['description'] }}</p>
                           </div>
                       </div>
                   @endforeach

                   {{-- SDG List --}}
                   <div class="sdg-list mt-6">
                       @php
                           $sdgs = $product->get_sdgs();
                           $identified_sdgs = [];
                           $other_sdgs = false;

                           foreach ($sdgs as $sdg) {
                               if (!empty($sdg['icon']) && strtolower($sdg['name']) !== 'other') {
                                   $identified_sdgs[] = $sdg;
                               } else {
                                   $other_sdgs = true;
                               }
                           }
                       @endphp

                       @foreach ($identified_sdgs as $sdg)
                           <div class="sdg-flex">
                               <img src="{{ $sdg['icon'] }}" alt="SDG {{ $sdg['id'] }}: {{ $sdg['name'] }}"
                                   width="33" height="33" />
                               <div class="sdg-content">
                                   <h3>{{ $sdg['name'] }}</h3>
                               </div>
                           </div>
                       @endforeach

                       @if ($other_sdgs)
                           <div class="sdg-flex">
                               <img src="{{ @Vite::svg('sdg/sdg_placeholder_icon.svg') }}" alt="Other SDGs"
                                   width="33" height="33" />
                               <div class="sdg-content">
                                   <h3>Other</h3>
                               </div>
                           </div>
                       @endif
                   </div>
               </section>

               {{-- Project details --}}
               <section class="project-details">
                   @if ($product->get_project_id())
                       <div class="detail-row">
                           <div class="icon-column">
                               {!! str_replace(
                                   '<svg',
                                   '<svg data-filename="general_id.svg"',
                                   file_get_contents(resource_path('svg/general_id.svg')),
                               ) !!}
                           </div>
                           <div class="content-column">
                               <h2 class="small-header">Project code</h2>
                               <p>{{ $product->get_project_id() }}</p>
                           </div>
                       </div>
                   @endif
                   @if ($product->get_organization())
                       <div class="detail-row">
                           <div class="icon-column">
                               {!! file_get_contents(resource_path('svg/general_organization.svg')) !!}
                           </div>
                           <div class="content-column">
                               <h2 class="small-header">Organization</h2>
                               <p>{{ $product->get_organization() }}</p>
                           </div>
                       </div>
                   @endif
                   @if ($product->get_certificate())
                       <div class="detail-row">
                           <div class="icon-column">
                               {!! str_replace(
                                   '<svg',
                                   '<svg data-filename="general_certification.svg"',
                                   file_get_contents(resource_path('svg/general_certification.svg')),
                               ) !!}
                           </div>
                           <div class="content-column">
                               <h2 class="small-header">Certification</h2>
                               <p>{{ $product->get_certificate() }}</p>
                           </div>
                       </div>
                   @endif
                   @if ($product->get_country())
                       <div class="detail-row">
                           <div class="icon-column">
                               {!! str_replace(
                                   '<svg',
                                   '<svg data-filename="general_location.svg"',
                                   file_get_contents(resource_path('svg/general_location.svg')),
                               ) !!}
                           </div>
                           <div class="content-column">
                               <h2 class="small-header">{{ __('Location', CO2MARKET_TEXT_DOMAIN) }}</h2>
                               <p>{{ $product->get_country() }}</p>
                           </div>
                       </div>
                   @endif
               </section>
           </div>

           {{-- Right column --}}
           <div class="right-column">
               {{-- About this project --}}
               <section class="about-project">
                   <h3>About this project</h3>
                   <div class="content">
                       {!! $product->get_description() !!}
                   </div>
               </section>

               {{-- Impact Description --}}
               @php
                   $impact_description = $product->get_impact_description();
               @endphp
               @if ($impact_description && trim($impact_description) !== '')
                   <section class="impact-description">
                       <h3>Impact Description</h3>
                       <div class="content">
                           {!! $impact_description !!}
                       </div>
                   </section>
               @endif

               {{-- Environmental benefits --}}
               @if ($product->get_env_description())
                   <section class="environmental-benefits">
                       <h2>Environmental benefits</h2>
                       <div class="content">
                           {!! $product->get_env_description() !!}
                       </div>
                       @php
                           $benefits_list = $product->get_environmental_benefits_list();
                       @endphp
                       @if (!empty($benefits_list))
                           <ul class="benefits-list">
                               @foreach ($benefits_list as $benefit)
                                   <li>{!! $benefit['benefit'] !!}</li>
                               @endforeach
                           </ul>
                       @endif
                   </section>
               @endif

               {{-- Social benefits --}}
               @if ($product->get_social_description())
                   <section class="social-benefits">
                       <h2>Social benefits</h2>
                       <div class="content">
                           {!! $product->get_social_description() !!}
                       </div>
                   </section>
               @endif

               {{-- Map --}}
               @if ($product->get_country_map_url())
                   <section class="map-section">
                       <div class="gap-2">
                           <a class="text-lg text-ox-green-800 hover:text-ox-green-600 transition-colors"
                               href="https://www.google.com/maps/embed/v1/place?key=AIzaSyBf-E9K7EWlXZzJhFIuOA9I_YDg1WGXhXpD7bFII&q={{ $product->get_country_map_url() }}"
                               target="_blank">
                               {{ __('View on map', CO2MARKET_TEXT_DOMAIN) }}
                           </a>
                       </div>
                   </section>
               @endif
           </div>
       </div>
   </div>
