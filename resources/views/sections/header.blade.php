<header class="bg-white">
    <div class="mx-auto">
        <div class="flex items-center justify-between py-5 px-6">
            <!-- Logo and Search -->
            <div class="flex flex-col xl:flex-row items-start xl:items-center gap-6 xl:gap-[72px] w-full xl:w-auto">
                <a href="{{ home_url('/') }}" class="flex items-center">
                    <img src="{{ Vite::image('logo.webp') }}" alt="{{ get_bloginfo('name', 'display') }}" class="h-11 sm:h-16">
                </a>
                <div class="hidden sm:block w-full xl:w-auto [&_.is-search-form]:w-full [&_.is-search-form]:flex [&_.is-search-form]:bg-white [&_.is-search-form]:rounded-[5px] [&_.is-form-style.is-form-style-3_label]:!border-solid [&_.is-form-style.is-form-style-3_label]:!border-2 [&_.is-form-style.is-form-style-3_label]:!border-ox-green-200 [&_.is-form-style.is-form-style-3_label]:!rounded-[5px] [&_.is-form-style.is-form-style-3_label]:!ring-0 [&_.is-form-style.is-form-style-3_label]:!ring-offset-0 [&_.is-form-style.is-form-style-3_label:has(input:focus)]:!border-ox-green-400 [&_#is-search-input-5909]:!text-ox-green-200 [&_#is-search-input-5909]:!text-base [&_#is-search-input-5909]:placeholder:!text-ox-green-200 [&_#is-search-input-5909]:placeholder:!opacity-100 [&_#is-search-input-5909]:focus:!text-ox-green-600 [&_#is-search-input-5909]:focus:placeholder:!text-ox-green-600 [&_#is-search-input-5909]:active:!text-ox-green-600 [&_#is-search-input-5909.is-search-input]:!text-ox-green-600 [&_.is-form-style.is-form-style-3_input.is-search-input]:!border-0 [&_.is-form-style.is-form-style-3_input.is-search-input]:!bg-white [&_.is-form-style.is-form-style-3_input.is-search-input]:min-w-[400px] [&_.is-form-style.is-form-style-3_input.is-search-input]:w-full [&_.is-form-style.is-form-style-3_input.is-search-input]:!h-10 [&_.is-form-style.is-form-style-3_input.is-search-input]:!px-6 [&_.is-form-style.is-form-style-3_input.is-search-input]:!ring-0 [&_.is-form-style.is-form-style-3_input.is-search-input]:!ring-offset-0 [&_.is-form-style.is-form-style-3_input.is-search-input]:focus:!outline-none [&_.is-form-style.is-form-style-3_input.is-search-input]:focus:!ring-0 [&_.is-form-style.is-form-style-3_input.is-search-input]:focus:!ring-offset-0 [&_.is-ajax-search-no-result]:!text-left [&_#is-ajax-search-result-5909]:!border-ox-green-100 [&_.is-ajax-search-post_a]:!text-ox-green-600 [&_.is-ajax-search-post_a]:!font-bold [&_.is-ajax-woocommerce-actions]:!hidden [&_.is-form-id-5909_.is-search-submit]:!rounded-[5px] [&_.is-form-id-5909_.is-search-icon]:!rounded-[5px] [&_.is-form-style_button.is-search-submit]:!w-[2.8rem] [&_.is-form-style_button.is-search-submit]:!h-[2.8rem] [&_.is-form-style_button.is-search-submit]:!flex [&_.is-form-style_button.is-search-submit]:!items-center [&_.is-form-style_button.is-search-submit]:!justify-center [&_.is-form-style_button.is-search-submit]:!ml-[5px] [&_.is-form-id-5909_.is-search-submit]:!transition-all [&_.is-form-id-5909_.is-search-submit]:!duration-200 [&_.is-form-id-5909_.is-search-icon]:!transition-all [&_.is-form-id-5909_.is-search-icon]:!duration-200 [&_.is-form-id-5909_.is-search-submit:hover]:!bg-ox-green-200 [&_.is-form-id-5909_.is-search-submit:focus]:!bg-ox-green-200 [&_.is-form-id-5909_.is-search-submit:hover]:!border-ox-green-200 [&_.is-form-id-5909_.is-search-submit:focus]:!border-ox-green-200 [&_.is-form-id-5909_.is-search-icon:hover]:!bg-ox-green-200 [&_.is-form-id-5909_.is-search-icon:focus]:!bg-ox-green-200 [&_.is-form-id-5909_.is-search-icon:hover]:!border-ox-green-200 [&_.is-form-id-5909_.is-search-icon:focus]:!border-ox-green-200 [&_.is-search-icon_svg]:!scale-125">
                    {!! do_shortcode('[ivory-search id="5909" title="Header search"]') !!}
                </div>
            </div>

            <div class="flex self-start xl:self-center pt-1 sm:pt-4 md:pt-4 lg:pt-4 xl:pt-0">
                <div class="flex items-start xl:items-center self-start xl:self-center gap-1 sm:gap-2 xl:gap-4">
                    <div class="hidden sm:flex items-start xl:items-center self-start xl:self-center gap-2">
                        <x-currency-picker />
                        <x-language-switcher />
                    </div>
                    @if (is_user_logged_in())
                        <a href="{{ get_permalink(5964) }}" class="block">
                            <x-button variant="green" style="secondary" rounded class="!rounded-full h-10 flex items-center justify-center whitespace-nowrap !bg-white !text-ox-green-600 hover:!bg-ox-green-200 !border-2 !border-ox-green-400">
                                <span class="hidden sm:inline">Get Started</span>
                                <span class="sm:hidden">Get Started</span>
                            </x-button>
                        </a>
                    @else
                        <a href="{{ get_permalink(658) }}" class="block">
                            <x-button variant="green" style="secondary" rounded class="!rounded-full h-10 flex items-center justify-center whitespace-nowrap !bg-white !text-ox-green-600 hover:!bg-ox-green-200 !border-2 !border-ox-green-400">
                                <span class="hidden sm:inline">Register</span>
                                <span class="sm:hidden">Register</span>
                            </x-button>
                        </a>
                    @endif
                    @if (is_user_logged_in())
                        <x-user-menu/>
                    @else
                        <a href="{{ get_login_url() }}">
                            <x-button variant="green" style="filled" class="!rounded-full h-10 flex items-center justify-center whitespace-nowrap">
                                <div class="flex items-center">
                                    @svg('general_contact', ["class" => "w-[1.875rem] h-[1.875rem] fill-ox-green-600"])
                                    <span class="hidden sm:inline ml-1">Log In</span>
                                </div>
                            </x-button>
                        </a>
                    @endif
                </div>
            </div>
        </div>
        <div class="flex flex-col xl:flex-row justify-between bg-ox-green-100 mx-6 py-4 px-5">
            <div class="flex justify-between xl:justify-start w-full xl:w-auto mb-4 xl:mb-0">
                <x-process-progress />
            </div>
            <div class="flex flex-col sm:flex-row items-start sm:items-center gap-4 sm:gap-8 xl:gap-16 w-full xl:w-auto">
                <div class="w-full sm:w-auto sm:flex-1 xl:flex-auto">
                    <x-process-selector add-new-link="/calculate" />
                </div>
                <div class="w-full sm:flex-1 xl:flex-auto flex justify-start sm:justify-end">
                    <x-cart-button />
                </div>
            </div>
        </div>
    </div>
</header>
