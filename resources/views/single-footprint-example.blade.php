@extends('layouts.app')

@section('content')
@php
    $footprint_example =  new \App\Models\FootprintExample($post);
    $currency = get_woocommerce_currency();
    [$scope_one_percentage, $scope_two_percentage, $scope_three_percentage] = $footprint_example?->getScopePercentages();
    $available_projects = $footprint_example?->getAvailableCompensationProjects();
    $projects_json = json_encode($available_projects);
@endphp

<div class="footprint-example-page">
    {{-- Full width header section --}}
    <div class="w-full bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div class="flex items-center gap-8">
                <a href="/search" class="flex items-center gap-2 px-4 py-2 rounded-full border-2 border-ox-green-400 text-ox-green-600 hover:bg-ox-green-100">
                    <span class="text-sm">←</span>
                    Back to search
                </a>
                <div class="flex items-center gap-4 text-sm">
                    <a href="/" class="text-ox-green-400 hover:text-ox-green-600">Home</a>
                    <span class="text-ox-green-400">•</span>
                    <a href="/get-footprint" class="text-ox-green-400 hover:text-ox-green-600">Get footprint</a>
                    <span class="text-ox-green-400">•</span>
                    <a href="/business-examples" class="text-ox-green-400 hover:text-ox-green-600">Business examples</a>
                    <span class="text-ox-green-400">•</span>
                    <span class="text-ox-green-600">{{ $footprint_example->level5 }}</span>
                </div>
            </div>
        </div>
    </div>

    {{-- Main content --}}
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {{-- Top section --}}
        <div class="flex flex-col lg:flex-row gap-12 mt-8">
            {{-- Image - adjusted from 7/12 to 1/2 --}}
            <div class="lg:w-1/2">
                <div class="rounded-3xl overflow-hidden aspect-[16/10]">
                    @if($footprint_example->getImageUrl())
                        <img src="{{ $footprint_example->getImageUrl() }}"
                             alt="{{ $footprint_example->level5 }}"
                             class="w-full h-full object-cover">
                    @endif
                </div>
            </div>

            {{-- Categories and info - adjusted from 5/12 to 1/2 --}}
            <div class="lg:w-1/2 flex flex-col gap-8">
                <div class="space-y-2">
                    {{-- Main category with background --}}
                    <div class="text-ox-green-600 font-medium bg-ox-green-200 w-fit px-3 py-1 rounded-md text-sm">
                        {{ $footprint_example->level1 }}
                    </div>

                    {{-- First subcategory with half-square --}}
                    <div class="flex items-end gap-2 ml-4">
                        <div class="w-[25px] min-w-[25px] h-[25px] border-l-2 border-b-2 border-[#c8dcc8] rounded-bl"></div>
                        <p class="text-[#c8dcc8] text-sm -mb-[2px]">
                            {{ $footprint_example->level2 }}
                        </p>
                    </div>

                    {{-- Second subcategory with half-square --}}
                    <div class="flex items-end gap-2 ml-8">
                        <div class="w-[25px] min-w-[25px] h-[25px] border-l-2 border-b-2 border-[#c8dcc8] rounded-bl"></div>
                        <p class="text-[#c8dcc8] text-sm -mb-[2px]">
                            {{ $footprint_example->level4 }}
                        </p>
                    </div>
                </div>

                <h1 class="text-2xl font-bold text-ox-black">
                    {{ $footprint_example->level5 }}
</h1>

                {{-- Replace the simple scope display with circle chart --}}
                <div class="mt-4">
                    <div class="flex items-start gap-12">
                        {{-- Circle chart - matched to legend height --}}
                        <div class="relative w-24 h-24">
                            <svg class="w-full h-full -rotate-90" viewBox="0 0 100 100">
                                {{-- Background circle --}}
                                <circle
                                    cx="50" cy="50" r="45"
                                    fill="none"
                                    class="stroke-ox-green-200"
                                    stroke-width="10"
                                />

                                {{-- Scope 3 arc --}}
                                <circle
                                    cx="50" cy="50" r="45"
                                    fill="none"
                                    class="stroke-ox-green-200"
                                    stroke-width="10"
                                    stroke-dasharray="{{ $scope_three_percentage * 2.51327 }} 1000"
                                />

                                {{-- Scope 2 arc --}}
                                <circle
                                    cx="50" cy="50" r="45"
                                    fill="none"
                                    class="stroke-ox-green-400"
                                    stroke-width="10"
                                    stroke-dasharray="{{ $scope_two_percentage * 2.51327 }} 1000"
                                />

                                {{-- Scope 1 arc --}}
                                <circle
                                    cx="50" cy="50" r="45"
                                    fill="none"
                                    class="stroke-ox-green-600"
                                    stroke-width="10"
                                    stroke-dasharray="{{ $scope_one_percentage * 2.51327 }} 1000"
                                />

                                {{-- White center --}}
                                <circle
                                    cx="50" cy="50" r="40"
                                    fill="white"
                                />
                            </svg>
                        </div>

                        {{-- Scope legend with updated spacing and border radius --}}
                        <div class="space-y-2">
                            <div class="flex items-center gap-2">
                                <div class="w-6 h-6 bg-ox-green-600 rounded-[5px]"></div>
                                <span class="text-sm">
                                    <span class="font-bold">Scope 1</span>
                                    <span class="ml-3">{{ $scope_one_percentage }}%</span>
                                </span>
                            </div>
                            <div class="flex items-center gap-2">
                                <div class="w-6 h-6 bg-ox-green-400 rounded-[5px]"></div>
                                <span class="text-sm">
                                    <span class="font-bold">Scope 2</span>
                                    <span class="ml-3">{{ $scope_two_percentage }}%</span>
                                </span>
                            </div>
                            <div class="flex items-center gap-2">
                                <div class="w-6 h-6 bg-ox-green-200 rounded-[5px]"></div>
                                <span class="text-sm">
                                    <span class="font-bold">Scope 3</span>
                                    <span class="ml-3">{{ $scope_three_percentage }}%</span>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        {{-- Bottom section with calculation boxes --}}
        <div class="mt-[72px] flex flex-col md:flex-row md:justify-start gap-12 md:gap-6 px-4 md:px-0">
            {{-- Get footprint box --}}
            <div class="w-full md:w-[320px] bg-ox-green-100 pt-20 p-6 rounded-lg relative mt-8 md:mt-0">
                <div class="absolute -top-6 left-1/2 -translate-x-1/2">
                    <div class="flex items-center justify-center bg-ox-green-400 w-12 h-12 rounded-[5px]">
                        <img src="/wp-content/uploads/2024/04/calculate_icon.svg" class="w-6 h-6 text-white"/>
                    </div>
                    <div class="flex items-center justify-center mt-4">
                        <span class="bg-ox-green-600 text-ox-green-400 w-6 h-6 rounded-full flex items-center justify-center text-sm font-medium">1</span>
                    </div>
                </div>
                <div class="flex flex-col items-center text-center">
                    <h4 class="text-sm text-ox-green-600 font-medium mb-8 max-w-[140px]">Carbon footprint estimate</h4>

                    {{-- Scope buttons --}}
                    <div class="flex gap-2 mb-4 w-full">
                        <button
                            class="flex-1 px-4 py-1.5 text-sm text-ox-green-600 border-2 border-ox-green-400 rounded-md
                                   transition-colors duration-200 scope-btn focus:outline-none
                                   hover:bg-ox-green-400 hover:text-ox-green-600
                                   data-[active=true]:bg-ox-green-400 data-[active=true]:text-ox-green-600"
                            data-scope="scope-1-2"
                            data-active="true">
                            Scope 1-2
                        </button>
                        <button
                            class="flex-1 px-4 py-1.5 text-sm text-ox-green-600 border-2 border-ox-green-400 rounded-md
                                   transition-colors duration-200 scope-btn
                                   hover:bg-ox-green-400 hover:text-ox-green-600
                                   data-[active=true]:bg-ox-green-400 data-[active=true]:text-ox-green-600"
                            data-scope="scope-1-3"
                            data-active="false">
                            Scope 1-3
                        </button>
                    </div>

                    {{-- Info tooltip --}}
                    <div class="relative mb-4">
                        <div class="text-sm text-ox-green-600 hover:text-ox-green-400 flex items-center gap-1 group cursor-pointer">
                            Info
                            <div class="question-circle w-4 h-4 rounded-full border border-ox-green-600 flex items-center justify-center ml-1">
                                <span class="text-ox-green-600 text-xs">i</span>
                            </div>
                            <div class="disc-tooltip invisible group-hover:visible absolute left-0 bottom-full mb-2 w-64">
                                {{-- Added a container with padding to create hover bridge --}}
                                <div class="relative">
                                    {{-- Actual tooltip content --}}
                                    <div class="bg-white p-4 rounded-lg shadow-lg border border-gray-100">
                                        <div class="text-sm text-gray-600">
                                            <a href="/post/get-footprint/ghg-scopes/" class="text-ox-green-600 hover:text-ox-green-400 block mb-2">
                                                What are the scopes 1, 2 and 3?
                                            </a>
                                            <a href="/post/get-footprint/what-scope-shall-i-select/" class="text-ox-green-600 hover:text-ox-green-400 block">
                                                What scope should we select?
                                            </a>
                                        </div>
                                    </div>
                                    {{-- Invisible bridge to maintain hover --}}
                                    <div class="absolute h-2 w-full bottom-0 translate-y-full"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {{-- CO2e amount --}}
                    <div class="text-4xl font-secondary text-ox-green-600 mb-12">
                        <span id="scope-1-2" class="scope-value font-bold">
                            @if($footprint_example->getScopeTwoTotal())
                                {{ number_format($footprint_example->getScopeTwoTotal(), 0) }}
                            @else
                                0
                            @endif
                        </span>
                        <span id="scope-1-3" class="scope-value font-bold hidden">
                            @if($footprint_example->getScopeThreeTotal())
                                {{ number_format($footprint_example->getScopeThreeTotal(), 0) }}
                            @else
                                0
                            @endif
                        </span>
                        <span class="font-normal">CO<sub>2</sub>e</span>
                    </div>

                    {{-- Calculate button - Step 1 --}}
                    <button class="w-full px-4 py-1.5 text-sm text-ox-green-600 border-2 border-ox-green-400 rounded-md
                                   hover:bg-ox-green-400 focus:bg-ox-green-400 focus:outline-none
                                   transition-colors duration-200 mb-4">
                        Calculate your actual footprint
                    </button>

                    {{-- Disclaimer tooltip --}}
                    <div class="relative">
                        <div class="text-xs text-ox-green-600 hover:text-ox-green-400 flex items-center gap-1 group cursor-pointer">
                            Disclaimer
                            <div class="question-circle w-4 h-4 rounded-full border border-ox-green-600 flex items-center justify-center ml-1">
                                <span class="text-ox-green-600 text-xs">?</span>
                            </div>
                            <div class="disc-tooltip invisible group-hover:visible absolute left-1/2 -translate-x-1/2 bottom-full mb-2 w-64">
                                <div class="relative">
                                    <div class="bg-white p-4 rounded-lg shadow-lg border border-gray-100">
                                        <div class="text-sm text-gray-600">
                                            This is a typical estimate based on industry averages. Actual values may vary.
                                        </div>
                                    </div>
                                    <div class="absolute h-2 w-full bottom-0 translate-y-full"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {{-- First Arrow --}}
            <div class="hidden md:flex items-center relative" style="top: -200px">
                <img src="/wp-content/uploads/2024/10/general_arrow_three.svg"
                     class="w-20 h-20 opacity-90"
                     alt="Next step" />
            </div>

            {{-- Offset box --}}
            <div class="w-full md:w-[320px] bg-ox-green-100 pt-20 p-6 rounded-lg relative mt-8 md:mt-0">
                {{-- Icon and number --}}
                <div class="absolute -top-6 left-1/2 -translate-x-1/2">
                    <div class="flex items-center justify-center bg-ox-green-400 w-12 h-12 rounded-[5px]">
                        <img src="/wp-content/uploads/2024/04/offset_icon.svg" class="w-6 h-6 text-white"/>
                    </div>
                    <div class="flex items-center justify-center mt-4">
                        <span class="bg-ox-green-600 text-ox-green-400 w-6 h-6 rounded-full flex items-center justify-center text-sm font-medium">2</span>
                    </div>
                </div>
                <div class="flex flex-col items-center text-center">
                    <h4 class="text-sm text-ox-green-600 font-medium mb-8 max-w-[140px]">Net-zero cost estimate</h4>

                    {{-- Custom dropdown --}}
                    <div class="w-full mb-4 relative">
                        {{-- Hidden native select for maintaining form functionality --}}
                        <select id="project-select" class="hidden">
                            <option value="10">Select a project</option>
                            @foreach($footprint_example->getAvailableCompensationProjects() as $project)
                                <option value="{{ $project['price'] }}">{{ $project['title'] }}</option>
                            @endforeach
                        </select>

                        {{-- Custom dropdown button --}}
                        <button
                            id="custom-select"
                            type="button"
                            class="w-full px-4 py-1.5 text-sm text-ox-green-600 border-2 border-ox-green-400 rounded-md
                                   hover:bg-ox-green-100 focus:outline-none bg-white
                                   flex items-center justify-between">
                            <span id="selected-text">Select a project</span>
                            @svg('general_arrow_head', ['class' => 'w-4 h-4 text-ox-green-600'])
                        </button>

                        {{-- Dropdown options --}}
                        <div id="dropdown-options"
                             class="hidden absolute left-0 right-0 top-full mt-1 bg-white border-2 border-ox-green-400
                                    rounded-md shadow-lg z-10">
                            <div class="py-1">
                                <button type="button"
                                        class="w-full px-4 py-2 text-left text-sm text-ox-green-600 hover:bg-ox-green-100"
                                        data-value="10">
                                    Select a project
                                </button>
                                @foreach($footprint_example->getAvailableCompensationProjects() as $project)
                                    <button type="button"
                                            class="w-full px-4 py-2 text-left text-sm text-ox-green-600 hover:bg-ox-green-100"
                                            data-value="{{ $project['price'] }}">
                                        {{ $project['title'] }}
                                    </button>
                                @endforeach
                            </div>
                        </div>
                    </div>

                    {{-- Info tooltip --}}
                    <div class="relative mb-4">
                        <div class="text-sm text-ox-green-600 hover:text-ox-green-400 flex items-center gap-1 group cursor-pointer">
                            Info
                            <div class="question-circle w-4 h-4 rounded-full border border-ox-green-600 flex items-center justify-center ml-1">
                                <span class="text-ox-green-600 text-xs">i</span>
                            </div>
                            <div class="disc-tooltip invisible group-hover:visible absolute left-0 bottom-full mb-2 w-64">
                                {{-- Added a container with padding to create hover bridge --}}
                                <div class="relative">
                                    {{-- Actual tooltip content --}}
                                    <div class="bg-white p-4 rounded-lg shadow-lg border border-gray-100">
                                        <div class="text-sm text-gray-600">
                                            <a href="/post/get-footprint/what-is-net-zero/" class="text-ox-green-600 hover:text-ox-green-400 block mb-2">
                                                What is net-zero?
                                            </a>
                                            <a href="/post/get-footprint/how-to-achieve-net-zero/" class="text-ox-green-600 hover:text-ox-green-400 block">
                                                How to achieve net-zero?
                                            </a>
                                        </div>
                                    </div>
                                    {{-- Invisible bridge to maintain hover --}}
                                    <div class="absolute h-2 w-full bottom-0 translate-y-full"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {{-- EUR amount --}}
                    <div class="text-4xl font-secondary text-ox-green-600 mb-12">
                        <span id="eur-amount" class="font-bold">{{ number_format($footprint_example->getScopeTwoTotal() * 10, 0) }}</span>
                        <span class="font-normal">€</span>
                    </div>

                    {{-- Find project button - Step 2 --}}
                    <button class="w-full px-4 py-1.5 text-sm text-ox-green-600 border-2 border-ox-green-400 rounded-md
                                   hover:bg-ox-green-400 focus:bg-ox-green-400 focus:outline-none
                                   transition-colors duration-200 mb-4">
                        Find a suitable offset project
                    </button>

                    {{-- Disclaimer tooltip --}}
                    <div class="relative">
                        <div class="text-xs text-ox-green-600 hover:text-ox-green-400 flex items-center gap-1 group cursor-pointer">
                            Disclaimer
                            <div class="question-circle w-4 h-4 rounded-full border border-ox-green-600 flex items-center justify-center ml-1">
                                <span class="text-ox-green-600 text-xs">?</span>
                            </div>
                            <div class="disc-tooltip invisible group-hover:visible absolute left-1/2 -translate-x-1/2 bottom-full mb-2 w-64">
                                <div class="relative">
                                    <div class="bg-white p-4 rounded-lg shadow-lg border border-gray-100">
                                        <div class="text-sm text-gray-600 space-y-2">
                                            <p>These estimates are only indicative.</p>
                                            <p>Actual costs may vary considerably from these figures.</p>
                                            <p>The actual cost will be influenced by the size of your actual
                                            carbon footprint and the cost of the offset project you choose.
                                                <a href="#" class="text-ox-green-600 hover:text-ox-green-400">Compare consultants</a></p>
                                            <p>who can calculate your actual carbon footprint and the
                                                <a href="/post/communicate/climate-claims/" class="text-ox-green-600 hover:text-ox-green-400"> offset projects </a>you can
												choose from to offset your carbon footprint.</p>
                                        </div>
                                    </div>
                                    <div class="absolute h-2 w-full bottom-0 translate-y-full"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {{-- Second Arrow --}}
            <div class="hidden md:flex items-center relative" style="top: -200px">
                <img src="/wp-content/uploads/2024/10/general_arrow_three.svg"
                     class="w-20 h-20 opacity-90"
                     alt="Next step" />
            </div>

            {{-- Communicate box --}}
            <div class="w-full md:w-[320px] bg-ox-green-100 pt-20 p-6 rounded-lg relative mt-8 md:mt-0 communicate-box">
                {{-- Icon and number --}}
                <div class="absolute -top-6 left-1/2 -translate-x-1/2">
                    <div class="flex items-center justify-center bg-ox-green-400 w-12 h-12 rounded-[5px]">
                        <img src="/wp-content/uploads/2024/04/communicate_icon.svg" class="w-6 h-6 text-white"/>
                    </div>
                    {{-- Step number --}}
                    <div class="flex items-center justify-center mt-3 mb-4">
                        <span class="bg-ox-green-600 text-ox-green-400 w-6 h-6 rounded-full flex items-center justify-center text-sm font-medium">3</span>
                    </div>
                </div>
                <div class="flex flex-col items-center text-center h-full">
                    <h4 class="text-sm text-ox-green-600 font-medium mb-2 max-w-[140px]">Communications material examples</h4>

                    {{-- Communications material examples section --}}
                    <div class="communicate-stamp-swiper mb-0">
                        <div class="swiper example-page-swiper">
                            <div class="swiper-wrapper">
                                <div class="swiper-slide">
                                    @svg('stamp-carousel-templates/template1', ['class' => 'w-[140px] h-[140px] rounded-lg'])
                                </div>
                                <div class="swiper-slide">
                                    @svg('stamp-carousel-templates/template2', ['class' => 'w-[140px] h-[140px] rounded-lg'])
                                </div>
                                <div class="swiper-slide">
                                    @svg('stamp-carousel-templates/template3', ['class' => 'w-[140px] h-[140px] rounded-lg'])
                                </div>
                            </div>
                        </div>
                    </div>

                    {{-- Bottom content wrapper --}}
                    <div class="mt-auto flex flex-col items-center w-full">
                        {{-- See all possibilities button - Step 3 --}}
                        <button class="w-full px-4 py-1.5 text-sm text-ox-green-600 border-2 border-ox-green-400 rounded-md
                                       hover:bg-ox-green-400 focus:bg-ox-green-400 focus:outline-none
                                       transition-colors duration-200 mb-4">
                            See all communication possibilities
                        </button>

                        {{-- Disclaimer tooltip --}}
                        <div class="relative">
                            <div class="text-xs text-ox-green-600 hover:text-ox-green-400 flex items-center gap-1 group cursor-pointer">
                                Disclaimer
                                <div class="question-circle w-4 h-4 rounded-full border border-ox-green-600 flex items-center justify-center ml-1">
                                    <span class="text-ox-green-600 text-xs">?</span>
                                </div>
                                <div class="disc-tooltip invisible group-hover:visible absolute left-1/2 -translate-x-1/2 bottom-full mb-2 w-64">
                                    <div class="relative">
                                        <div class="bg-white p-4 rounded-lg shadow-lg border border-gray-100">
                                            <div class="text-sm text-gray-600 space-y-2">
                                                <p>The photos are for illustrative purposes only.</p>
                                                <p>The actual image options include your company name and other information of your choice.</p>
                                                <p>The climate evidence available will depend on the extent and quality of the actions you take.
                                                    <a href="#" class="text-ox-green-600 hover:text-ox-green-400">Read more</a>.</p>
                                                <p>You should also consider what, if any, regulations exist in each market for the
                                                    <a href="/post/communicate/climate-claims/" class="text-ox-green-600 hover:text-ox-green-400">use of climate claims</a>.</p>
                                            </div>
                                        </div>
                                        <div class="absolute h-2 w-full bottom-0 translate-y-full"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        {{-- Description section --}}
        <div class="mt-16 mb-24 prose max-w-none">
            <div class="description-content">
                <style scoped>
                    .description-content > p {
                        margin-bottom: 48px !important;
                    }
                    .description-content > p:last-child {
                        margin-bottom: 0 !important;
                    }
                </style>
                {!! $footprint_example->description !!}
            </div>
        </div>
    </div>
</div>

@push('styles')
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@8/swiper-bundle.min.css">
<style>
/* Container styles to prevent cropping */
.communicate-stamp-swiper {
    padding: 0 40px;
    overflow: visible;
    margin-bottom: 0;  /* Default for desktop */
}

@media (max-width: 768px) {  /* Tablet and mobile */
    .communicate-stamp-swiper {
        margin-bottom: 3rem;  /* mb-12 equivalent */
    }
}

/* Rest of the styles remain unchanged */
.example-page-swiper {
    overflow: visible;
}

/* Slide styles */
.communicate-stamp-swiper .swiper-slide {
    width: 140px;
    height: 140px;
    opacity: 50%;
    transition: opacity 0.3s ease;
    margin: 0 20px;  /* Add horizontal spacing between slides */
}

.communicate-stamp-swiper .swiper-slide-active {
    opacity: 100%;
}

.example-page-swiper .swiper-slide {
    border-radius: 20px;
    margin: 1rem 0 3rem;
}

.example-page-swiper .swiper-slide img {
    height: 100%;
    object-fit: cover;
    border-radius: 5px;
}

@media (max-width: 544px) {
    .communicate-stamp-swiper {
        padding: 0 30px;  /* Slightly less padding on mobile */
    }

    .communicate-stamp-swiper .swiper-slide {
        width: 120px;
        height: 120px;
        margin: 0 15px;  /* Slightly less spacing on mobile */
    }
}

/* Add higher z-index for Step 3 disclaimer tooltip */
.communicate-box .disc-tooltip {
    z-index: 20;  /* Higher than Swiper's z-index */
}
</style>
@endpush

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/swiper@8/swiper-bundle.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const scopeButtons = document.querySelectorAll('.scope-btn');
    const scopeValues = document.querySelectorAll('.scope-value');
    const projectSelect = document.getElementById('project-select');
    const customSelect = document.getElementById('custom-select');
    const dropdownOptions = document.getElementById('dropdown-options');
    const selectedText = document.getElementById('selected-text');
    const eurAmount = document.getElementById('eur-amount');

    function updatePrice() {
        const scope12 = document.getElementById('scope-1-2');
        const tonnes = !scope12.classList.contains('hidden') ?
            {{ $footprint_example->getScopeTwoTotal() }} :
            {{ $footprint_example->getScopeThreeTotal() }};

        const price = parseFloat(projectSelect.value) || 10;
        const total = price * tonnes;

        eurAmount.textContent = new Intl.NumberFormat('en-US').format(total);
    }

    // Toggle dropdown
    customSelect.addEventListener('click', () => {
        dropdownOptions.classList.toggle('hidden');
    });

    // Close dropdown when clicking outside
    document.addEventListener('click', (e) => {
        if (!customSelect.contains(e.target) && !dropdownOptions.contains(e.target)) {
            dropdownOptions.classList.add('hidden');
        }
    });

    // Handle option selection
    dropdownOptions.querySelectorAll('button').forEach(option => {
        option.addEventListener('click', () => {
            const value = option.dataset.value;
            const text = option.textContent.trim();

            selectedText.textContent = text;
            projectSelect.value = value;
            dropdownOptions.classList.add('hidden');

            updatePrice();
        });
    });

    // Handle scope changes
    scopeButtons.forEach(button => {
        button.addEventListener('click', function() {
            scopeButtons.forEach(btn => btn.dataset.active = "false");
            this.dataset.active = "true";

            scopeValues.forEach(value => {
                value.classList.add('hidden');
                if (value.id === this.dataset.scope) {
                    value.classList.remove('hidden');
                }
            });

            updatePrice();
        });
    });

    // Initialize Swiper with specific dimensions
    const swiper = new Swiper(".example-page-swiper", {
        effect: "coverflow",
        grabCursor: true,
        centeredSlides: true,
        slidesPerView: "auto",
        initialSlide: 1,
        spaceBetween: -80,  // Increased negative value to bring slides even closer
        coverflowEffect: {
            rotate: 0,
            stretch: 0,
            depth: 20,      // Reduced depth for less pronounced 3D effect
            modifier: 3,     // Increased for more overlap
            scale: 0.85,    // Slightly larger scale for non-active slides
            slideShadows: false,
        }
    });
});
</script>
@endpush

@php
@endphp

@endsection