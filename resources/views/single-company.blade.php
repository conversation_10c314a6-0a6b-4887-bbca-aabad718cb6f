@extends('layouts.app')

@vite('resources/css/companyPage.css')

@section('content')
    @php
    while(have_posts()): the_post();
        $featured_prods = get_field('featured_products');
        $products = get_field('products');
        $about_us = get_field('about_us');
        $background_image = get_field('background_image');
        $company_image = get_field('image');
        $country = get_field('country');
        $address = get_field('address');
        $contact_info = get_field('contact_info');
    @endphp

    <div id="company-page" class="company-info">
        {!! do_shortcode('[breadcrumbs_bar]') !!}
        
        <div class="banner">
            @if($background_image && $background_image['url'])
                <img src="{{ $background_image['url'] }}" alt="{{ get_the_title() }} Banner" style="align-self: center;">
            @endif
        </div>

        <div class="main-info-container">
            <!-- Left Column (1/3) -->
            <div class="sidebar">
                @if($company_image)
                    <div class="image">
                        <img src="{{ $company_image['url'] }}" alt="{{ get_the_title() }} Image">
                    </div>
                @endif

                {{-- Contact box with contact icon --}}
                @if(isset($contact_info))
                    <div class="contact side-box">
                        {!! file_get_contents(resource_path('svg/general_contact.svg')) !!}
                        <div class="inner-item contact-item">
                            <h2 class="small-header">Contact</h2>
                            @if($contact_info)
                                <p>{{ $contact_info }}</p>
                            @endif
                        </div>
                    </div>
                @endif
            </div>

            <!-- Right Column (2/3) -->
            <div class="main-content-container">
                <div class="common-content">
                    <div class="title-container">
                        <h3 class="company-header">{{ get_the_title() }}</h3>
                        <div class="inner-rating">
                            {!! file_get_contents(resource_path('svg/general_star.svg')) !!}
                            @if($average)
                                <h2 class="company-rating">{{ number_format($average, 1) }}</h2>
                            @else
                                <p class="no-rating">Not enough ratings yet</p>
                            @endif
                        </div>
                    </div>

                    @php
                        $products = get_field('products') ?: [];
                        $all_categories = [];
                        
                        if (!empty($products)) {
                            foreach ($products as $product) {
                                $terms = get_the_terms($product->ID, 'product_cat');
                                if ($terms && !is_wp_error($terms)) {
                                    foreach ($terms as $term) {
                                        if ($term->name != "Uncategorized" && $term->name != "Consulting service") {
                                            $all_categories[$term->name] = true;
                                        }
                                    }
                                }
                            }
                        }
                    @endphp

                    @if(!empty($all_categories))
                        <ul class="product-types">
                            @foreach($all_categories as $cat_name => $presence)
                                <li class="type">{{ $cat_name }}</li>
                            @endforeach
                        </ul>
                    @endif

                    @if($description = get_field('description'))
                        {!! wp_kses_post($description) !!}
                    @endif
                </div>
            </div>
        </div>

        <div class="secondary-info-container">
            <!-- Left Column (1/3) -->
            <div class="sidebar">
                <h3 class="sidebar-header">COMPANY INFO</h3>
                
                {{-- Address box with location icon --}}
                @if(isset($country) || isset($address))
                    <div class="address side-box">
                        <div class="side-box-content">
                            <div class="two-column-layout">
                                <div class="icon-column">
                                    {!! file_get_contents(resource_path('svg/general_location.svg')) !!}
                                </div>
                                <div class="content-column">
                                    <h2 class="small-header">Address</h2>
                                    <div class="address-details">
                                        @if(isset($country) && $country)
                                            <p>{{ $country }}</p>
                                        @endif
                                        @if(isset($address) && $address)
                                            <p>{{ $address }}</p>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif
            </div>

            <!-- Right Column (2/3) -->
            <div class="main-content-container">
                <div class="content-container">
                    {{-- Tab Navigation --}}
                    <div class="button-container">
                        @if($featured_prods)
                            <button 
                                class="content-button {{ request()->tab === 'featured' || !request()->tab ? 'active' : '' }}"
                                onclick="handleTabClick(this, 'featured')"
                            >
                                Featured
                            </button>
                        @endif
                        
                        @if($products)
                            <button 
                                class="content-button {{ request()->tab === 'services' ? 'active' : '' }}"
                                onclick="handleTabClick(this, 'services')"
                            >
                                Services
                            </button>
                        @endif
                        
                        @if($about_us)
                            <button 
                                class="content-button {{ request()->tab === 'about' ? 'active' : '' }}"
                                onclick="handleTabClick(this, 'about')"
                            >
                                About us
                            </button>
                        @endif
                    </div>

                    {{-- Tab Content --}}
                    <div id="featured-products" class="content-box" style="{{ request()->tab === 'featured' || !request()->tab ? '' : 'display: none' }}">
                        <h3>Featured services</h3>
                        <div class="products-grid">
                            @foreach($featured_prods as $product)
                                <x-company-product-card 
                                    :product="$product"
                                    :featured="true"
                                />
                            @endforeach
                        </div>
                    </div>

                    <div id="services" class="content-box" style="{{ request()->tab === 'services' ? '' : 'display: none' }}">
                        @php
                            $categorized_products = collect($products)->groupBy(function($product) {
                                $terms = wp_get_post_terms($product->ID, 'product_cat');
                                return $terms[0]->name ?? 'Other';
                            })->sortBy(function($products, $category) {
                                // Custom sorting order for categories
                                $order = [
                                    'Calculate' => 1,
                                    'Audit' => 2,
                                    'Calculation' => 3,
                                    'New projects' => 4,
                                    'Other' => 5
                                ];
                                return $order[$category] ?? 6;
                            });
                        @endphp

                        @foreach($categorized_products as $category => $category_products)
                            @if($category !== 'Uncategorized')
                                <h3>{{ $category === 'Consulting service' ? 'Other' : $category }} services</h3>
                                <div class="products-grid">
                                    @foreach($category_products as $product)
                                        <x-company-product-card 
                                            :product="$product"
                                        />
                                    @endforeach
                                </div>
                            @endif
                        @endforeach
                    </div>

                    <div id="about-us" class="content-box" style="{{ request()->tab === 'about' ? '' : 'display: none' }}">
                        <h2>About us</h2>
                        {!! wp_kses_post($about_us) !!}
                    </div>

                    <script>
                        // Initialize active tab on page load
                        document.addEventListener('DOMContentLoaded', function() {
                            const urlParams = new URLSearchParams(window.location.search);
                            const activeTab = urlParams.get('tab') || 'featured';
                            const activeButton = document.querySelector(`button[onclick*="${activeTab}"]`);
                            if (activeButton) {
                                handleTabClick(activeButton, activeTab);
                            }
                        });

                        function handleTabClick(button, tabId) {
                            // Remove active class from all buttons
                            document.querySelectorAll('.content-button').forEach(btn => {
                                btn.classList.remove('active');
                            });
                            
                            // Add active class to clicked button
                            button.classList.add('active');
                            
                            // Hide all content boxes
                            document.querySelectorAll('.content-box').forEach(box => {
                                box.style.display = 'none';
                            });
                            
                            // Show selected content
                            let contentId;
                            switch(tabId) {
                                case 'featured':
                                    contentId = 'featured-products';
                                    break;
                                case 'about':
                                    contentId = 'about-us';
                                    break;
                                default:
                                    contentId = tabId;
                            }
                            
                            const contentBox = document.getElementById(contentId);
                            if (contentBox) {
                                contentBox.style.display = 'block';
                            }
                            
                            // Update URL without page reload
                            const url = new URL(window.location);
                            url.searchParams.set('tab', tabId);
                            window.history.pushState({}, '', url);
                        }
                    </script>
                </div>
            </div>
        </div>
    </div>

    @php
    endwhile;
    @endphp
@endsection
