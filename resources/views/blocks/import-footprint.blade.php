<script>
  document.addEventListener('alpine:init', () => {
    Alpine.data('importFootprint', () => ({
      dropzoneActive: false,
      files: [],
      uploading: false,
      uploadProgress: 0,
      uploadComplete: false,
      uploadError: null,
      maxFiles: 5,
      maxFileSize: 20971520, // 20MB in bytes
      isUserLoggedIn: {{ $is_user_logged_in ? 'true' : 'false' }},

      init() {
        this.$watch('files', value => {
          if (value.length > this.maxFiles) {
            this.files = this.files.slice(0, this.maxFiles);
            alert(`{{ __('You can only upload a maximum of', CO2MARKET_TEXT_DOMAIN) }} ${this.maxFiles} {{ __('files', CO2MARKET_TEXT_DOMAIN) }}.`);
          }
        });
      },

      addFiles(e) {
        if (!this.isUserLoggedIn) return;

        const newFiles = [...e.target.files].filter(file => {
          // Check file size
          if (file.size > this.maxFileSize) {
            alert(`{{ __('File', CO2MARKET_TEXT_DOMAIN) }} ${file.name} {{ __('is too large. Maximum file size is 20MB.', CO2MARKET_TEXT_DOMAIN) }}`);
            return false;
          }
          return true;
        });

        // Check total files count
        if (this.files.length + newFiles.length > this.maxFiles) {
          alert(`{{ __('You can only upload a maximum of', CO2MARKET_TEXT_DOMAIN) }} ${this.maxFiles} {{ __('files', CO2MARKET_TEXT_DOMAIN) }}.`);
          this.files = [...this.files, ...newFiles.slice(0, this.maxFiles - this.files.length)];
        } else {
          this.files = [...this.files, ...newFiles];
        }
      },

      removeFile(index) {
        this.files = this.files.filter((_, i) => i !== index);
      },

      uploadFiles() {
        if (this.files.length === 0) {
          alert('{{ __('Please select at least one file to upload.', CO2MARKET_TEXT_DOMAIN) }}');
          return;
        }

        this.uploading = true;
        this.uploadProgress = 0;
        this.uploadError = null;

        const formData = new FormData();
        this.files.forEach(file => {
          formData.append('files[]', file);
        });

        // Add CSRF token
        formData.append('_token', '{{ $csrf_token }}');

        const xhr = new XMLHttpRequest();
        xhr.open('POST', '{{ $upload_url }}');
        xhr.setRequestHeader('X-WP-Nonce', '{{ $csrf_token }}');

        xhr.upload.addEventListener('progress', (e) => {
          if (e.lengthComputable) {
            this.uploadProgress = Math.round((e.loaded * 100) / e.total);
          }
        });

        xhr.onload = () => {
          this.uploading = false;

          if (xhr.status === 200) {
            const response = JSON.parse(xhr.responseText);
            if (response.success) {
              this.uploadComplete = true;
              this.files = [];
            } else {
              this.uploadError = response.message || '{{ __('Upload failed', CO2MARKET_TEXT_DOMAIN) }}';
            }
          } else if (xhr.status === 401) {
            this.uploadError = '{{ __('You must be logged in to upload files', CO2MARKET_TEXT_DOMAIN) }}';
          } else {
            this.uploadError = '{{ __('Upload failed. Please try again.', CO2MARKET_TEXT_DOMAIN) }}';
          }
        };

        xhr.onerror = () => {
          this.uploading = false;
          this.uploadError = '{{ __('Network error. Please try again.', CO2MARKET_TEXT_DOMAIN) }}';
        };

        xhr.send(formData);
      }
    }))
  })
</script>

<div {{ $attributes }} class="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
  <div>
    <div class="p-6">
      <div x-data="importFootprint"
      class="relative">
        <!-- Dropzone -->
        <div
          @dragover.prevent="dropzoneActive = true"
          @dragleave.prevent="dropzoneActive = false"
          @drop.prevent="dropzoneActive = false; if(isUserLoggedIn) { addFiles($event) }"
          :class="{
            'border-dashed border-2 border-gray-300 bg-gray-50': true,
            'border-ox-green-400 bg-ox-green-100': dropzoneActive,
            'opacity-50 cursor-not-allowed': !isUserLoggedIn || uploading,
            'border-ox-green-500': uploadComplete
          }"
          class="min-h-[200px] rounded-lg flex flex-col items-center justify-center p-6 transition-all duration-150 ease-in-out">

          <!-- Not logged in message -->
          <template x-if="!isUserLoggedIn">
            <div class="text-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
              <h3 class="text-xl font-medium text-gray-900 mb-2">{{ __('Login Required', CO2MARKET_TEXT_DOMAIN) }}</h3>
              <p class="text-gray-500 mb-4">{{ __('Please login to import footprints', CO2MARKET_TEXT_DOMAIN) }}</p>
              <a href="{{ get_login_url() }}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-ox-black bg-ox-green-400 hover:bg-ox-green-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-ox-green-400">
                {{ __('Login Now', CO2MARKET_TEXT_DOMAIN) }}
              </a>
            </div>
          </template>

          <!-- Upload interface for logged in users -->
          <template x-if="isUserLoggedIn && !uploading && !uploadComplete">
            <div class="text-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
              </svg>
              <h3 class="text-xl font-medium text-gray-900 mb-2">{{ __('Drag and drop your files here', CO2MARKET_TEXT_DOMAIN) }}</h3>
              <p class="text-gray-500 mb-4">{{ __('or click to browse (max 5 files, 20MB each)', CO2MARKET_TEXT_DOMAIN) }}</p>
              <input
                type="file"
                multiple
                class="hidden"
                id="fileInput"
                @change="addFiles($event)"
                accept=".pdf,.doc,.docx,.xls,.xlsx,.csv,.txt,.zip,.rar,.7z,.jpg,.jpeg,.png"
              >
              <label for="fileInput" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-ox-green-600 bg-ox-green-400 hover:bg-ox-green-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-ox-green-400 cursor-pointer">
                {{ __('Browse Files', CO2MARKET_TEXT_DOMAIN) }}
              </label>
            </div>
          </template>
          <!-- Uploading progress -->
          <template x-if="uploading">
            <div class="text-center w-full max-w-md">
              <svg class="animate-spin h-8 w-8 text-ox-green-500 mx-auto mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <h3 class="text-xl font-medium text-gray-900 mb-2">{{ __('Uploading...', CO2MARKET_TEXT_DOMAIN) }}</h3>
              <div class="w-full bg-gray-200 rounded-full h-2.5 mb-4">
                <div class="bg-ox-green-500 h-2.5 rounded-full" :style="`width: ${uploadProgress}%`"></div>
              </div>
              <p class="text-gray-500" x-text="`${uploadProgress}% {{ __('complete', CO2MARKET_TEXT_DOMAIN) }}`"></p>
            </div>
          </template>

          <!-- Upload complete -->
          <template x-if="uploadComplete">
            <div class="text-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-ox-green-500 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              <h3 class="text-xl font-medium text-gray-900 mb-2">{{ __('Upload Complete!', CO2MARKET_TEXT_DOMAIN) }}</h3>
              <p class="text-gray-500 mb-4">{{ __('Your files have been successfully uploaded. Our professionals will check your footprint and inform you about the results.', CO2MARKET_TEXT_DOMAIN) }}</p>
              <button
                @click="uploadComplete = false"
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-ox-green-600 bg-ox-green-400 hover:bg-ox-green-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-ox-green-400">
                {{ __('Upload More Files', CO2MARKET_TEXT_DOMAIN) }}
              </button>
            </div>
          </template>

          <!-- Error message -->
          <template x-if="uploadError">
            <div class="mt-4 p-4 bg-red-50 rounded-md border border-red-200">
              <div class="flex">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <p class="ml-3 text-sm text-red-700" x-text="uploadError"></p>
              </div>
            </div>
          </template>
        </div>

        <!-- File list -->
        <div class="mt-6" x-show="0 < files.length">
          <h4 class="text-sm font-medium text-gray-900 mb-2">{{ __('Selected Files', CO2MARKET_TEXT_DOMAIN) }}</h4>
          <ul class="divide-y divide-gray-200 border border-gray-200 rounded-md overflow-hidden">
            <template x-for="(file, index) in files" :key="index">
              <li class="flex items-center justify-between py-3 px-4 bg-white gap-8 hover:bg-gray-50">
                <div class="flex items-center w-[80%]">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  <div class="max-w-[90%]">
                    <p class="text-sm font-medium text-gray-900 truncate" x-text="file.name"></p>
                    <p class="text-xs text-gray-500" x-text="`${(file.size / 1024 / 1024).toFixed(2)} MB`"></p>
                  </div>
                </div>
                <button
                  @click="removeFile(index)"
                  type="button"
                  class="text-red-500 hover:text-red-700 focus:outline-none">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                </button>
              </li>
            </template>
          </ul>

          <div class="mt-4 flex justify-end">
            <button
              @click="uploadFiles()"
              :disabled="uploading || files.length === 0"
              :class="{
                'inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2': true,
                'bg-ox-green-400 hover:bg-ox-green-200 focus:ring-ox-green-400 text-ox-green-600': !uploading &&  0 < files.length ,
                'bg-gray-300 cursor-not-allowed': uploading || files.length === 0
              }">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0l-4 4m4-4v12" />
              </svg>
              {{ __('Upload Files', CO2MARKET_TEXT_DOMAIN) }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
