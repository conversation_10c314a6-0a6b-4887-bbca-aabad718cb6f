@vite('resources/css/blocks/footprint-calculate.css')

@php
// Get table data from ninjaTables API with error handling
try {
    $tableData = function_exists('ninjaTablesGetTablesDataByID') ? ninjaTablesGetTablesDataByID(1304) : [];

    // If tableData is not an array, set it to an empty array
    if (!is_array($tableData)) {
        $tableData = [];
    }
} catch (Exception $e) {
    // Log the error
    error_log('Error fetching Ninja Tables data: ' . $e->getMessage());
    $tableData = [];
}

// Check if user is logged in
$is_user_logged_in = is_user_logged_in();
@endphp


<script>
document.addEventListener('alpine:init', () => {
    Alpine.data('footprintCalculator', () => ({
        step: 1,
        category: null,
        subCategory: "", // Use empty string instead of null for proper select binding
        value: "", // Use empty string instead of null for proper select binding
        inputValue: null,
        footprintName: '',
        calculatedValue: 0,
        isSaving: false,
        isSaved: false,
        isUserLoggedIn: {{ $is_user_logged_in ? 'true' : 'false' }},
        tableData: @json($tableData),

        get categories() {
            return [...new Set(this.tableData.map(item => item.level1))];
        },

        get subCategories() {
            if (!this.category) return [];
            return [...new Set(
                this.tableData
                    .filter(item => item.level1 === this.category)
                    .map(item => item.level2)
            )];
        },

        get level3Options() {
            if (!this.category || !this.subCategory || this.subCategory === "") return [];

            // Filter the table data to get level3 options based on selected category and subcategory
            const options = this.tableData.filter(item =>
                item.level1 === this.category &&
                item.level2 === this.subCategory
            );

            return options;
        },

        get currentDataPoint() {
            if (!this.value || this.value === "") return null;
            return this.tableData.find(item => item.id === this.value);
        },

        get calculatedValueRounded() {
            if (!this.currentDataPoint || !this.inputValue) return 0;
            const amount = parseFloat(this.currentDataPoint.carbonfootprint.replace(',', '.'));
            return Math.round(amount * this.inputValue);
        },

        changeCategory(c) {
            this.category = c;
            this.step = 2;
            this.subCategory = "";
            this.value = null;
            this.inputValue = null;
            this.footprintName = '';
        },

        changeSubCategory(e) {
            this.subCategory = e.target.value;
            this.value = "";
            this.inputValue = null;
            this.footprintName = '';
        },

        changeValue(e) {
            this.value = e.target.value;
            this.inputValue = null;
            this.footprintName = '';
        },

        goToStep(stepNumber) {
            this.step = stepNumber;
        },

        async saveFootprint() {
            if (this.isSaving || this.isSaved || !this.isUserLoggedIn) return;

            this.isSaving = true;

            try {
                const response = await fetch('{{ route("footprint-calculate.store") }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-WP-Nonce': '{{ wp_create_nonce('wp_rest') }}',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    body: JSON.stringify({
                        name: this.footprintName,
                        calculation: {
                            type: 'generic',
                            emissions: [{
                                name: this.currentDataPoint.level3,
                                total: this.calculatedValueRounded
                            }]
                        }
                    })
                });

                if (response.ok) {
                    this.isSaved = true;
                    toast("Saved footprint", {
                        type: 'success',
                        position: "bottom-center"
                    });
                } else {
                    throw new Error('Failed to save');
                }
            } catch (error) {
                // Show error toast
               toast("Failed to save", {
                        type: 'danger',
                        position: "bottom-center"
                    });
            } finally {
                this.isSaving = false;
            }
        }
    }))
});
</script>

<div x-data="footprintCalculator" class="main-container">
    <div class="inner-container">
        <!-- Breadcrumbs -->
        <nav x-show="1 < step" class="calculator-breadcrumbs">
            <ul>
                <li>
                    <div class="breadcrumb-item" @click="goToStep(1)" role="button" tabindex="0">
                        <button class="breadcrumb-button" x-data>
                            <template x-if="category === 'Construction'">@include('blocks.icons.calc-construction')</template>
                            <template x-if="category === 'Energy'">@include('blocks.icons.calc-energy')</template>
                            <template x-if="category === 'Foods'">@include('blocks.icons.calc-foods')</template>
                            <template x-if="category === 'Operations'">@include('blocks.icons.calc-operations')</template>
                            <template x-if="category === 'Procurement'">@include('blocks.icons.calc-procurement')</template>
                            <template x-if="category === 'Raw materials'">@include('blocks.icons.calc-raw-materials')</template>
                            <template x-if="category === 'Refrigerant'">@include('blocks.icons.calc-refrigerant')</template>
                            <template x-if="category === 'Services'">@include('blocks.icons.calc-services')</template>
                            <template x-if="category === 'Transport'">@include('blocks.icons.calc-transport')</template>
                            <template x-if="category === 'Travel'">@include('blocks.icons.calc-travel')</template>
                            <template x-if="category === 'Waste disposal'">@include('blocks.icons.calc-waste-disposal')</template>
                            <template x-if="category === 'Water and sewage'">@include('blocks.icons.calc-water-and-sewage')</template>
                        </button>
                        <div class="breadcrumb-label" x-text="category"></div>
                    </div>
                </li>
                <template x-if="subCategory">
                    <li>
                        <div class="breadcrumb-item" @click="goToStep(2)" role="button" tabindex="0">
                            <span x-text="subCategory"></span>
                        </div>
                    </li>
                </template>
                <template x-if="value">
                    <li>
                        <div class="breadcrumb-item" @click="goToStep(3)" role="button" tabindex="0">
                            <span x-text="currentDataPoint?.level3"></span>
                        </div>
                    </li>
                </template>
            </ul>
        </nav>

        <!-- Step Headings -->
        <h3 class="category-heading uppercase">
            <template x-if="step === 1">
                <span>{{ __('Select a category', CO2MARKET_TEXT_DOMAIN) }}</span>
            </template>
            <template x-if="step === 2">
                <span>{{ __('Select a sub-category', CO2MARKET_TEXT_DOMAIN) }}</span>
            </template>
            <template x-if="step === 3">
                <span>{{ __('Calculate your footprint', CO2MARKET_TEXT_DOMAIN) }}</span>
            </template>
            <template x-if="step === 4">
                <span>{{ __('Carbon footprint', CO2MARKET_TEXT_DOMAIN) }}
            </template>
        </h3>

        <!-- Step Content -->
        <div class="step-container">
            <!-- Step 1: Category Selection -->
            <template x-if="step === 1">
                <div class="category-grid">
                    <template x-for="c in categories" :key="c">
                        <div class="category-item" @click="changeCategory(c)" role="button" tabindex="0">
                            <button class="category-button">
                                <template x-if="c === 'Construction'">@include('blocks.icons.calc-construction')</template>
                                <template x-if="c === 'Energy'">@include('blocks.icons.calc-energy')</template>
                                <template x-if="c === 'Foods'">@include('blocks.icons.calc-foods')</template>
                                <template x-if="c === 'Operations'">@include('blocks.icons.calc-operations')</template>
                                <template x-if="c === 'Procurement'">@include('blocks.icons.calc-procurement')</template>
                                <template x-if="c === 'Raw materials'">@include('blocks.icons.calc-raw-materials')</template>
                                <template x-if="c === 'Refrigerant'">@include('blocks.icons.calc-refrigerant')</template>
                                <template x-if="c === 'Services'">@include('blocks.icons.calc-services')</template>
                                <template x-if="c === 'Transport'">@include('blocks.icons.calc-transport')</template>
                                <template x-if="c === 'Travel'">@include('blocks.icons.calc-travel')</template>
                                <template x-if="c === 'Waste disposal'">@include('blocks.icons.calc-waste-disposal')</template>
                                <template x-if="c === 'Water and sewage'">@include('blocks.icons.calc-water-and-sewage')</template>
                            </button>
                            <span x-text="c"></span>
                        </div>
                    </template>
                </div>
            </template>

            <!-- Step 2: Subcategory Selection -->
            <template x-if="step === 2">
                <div class="subcategory-container">
                    <div class="subcategory-options">
                        <label for="subcategory-select-1" class="subcategory-label">{{ __('Select subcategory', CO2MARKET_TEXT_DOMAIN) }}</label>
                        <select
                            id="subcategory-select-1"
                            x-model="subCategory"
                            @change="changeSubCategory($event)"
                            class="subcategory-select textField"
                        >
                            <option value="" selected>{{ __('Select your option', CO2MARKET_TEXT_DOMAIN) }}</option>
                            <template x-for="s in subCategories" :key="s">
                                <option :value="s" x-text="s"></option>
                            </template>
                        </select>
                    </div>
                    <div class="subcategory-options">
                        <label for="subcategory-select-2" class="subcategory-label">{{ __('Select option', CO2MARKET_TEXT_DOMAIN) }}</label>

                        <select
                            id="subcategory-select-2"
                            x-model="value"
                            @change="changeValue($event)"
                            class="subcategory-select textField"
                            :disabled="!subCategory || subCategory === ''"
                        >
                            <option value="" selected>{{ __('Select your option', CO2MARKET_TEXT_DOMAIN) }}</option>
                            <template x-for="d in level3Options" :key="d.id">
                                <option :value="d.id" x-text="d.level3"></option>
                            </template>
                        </select>
                    </div>
                </div>
            </template>

            <!-- Step 3: Input Value -->
            <template x-if="step === 3">
                <div class="input-container">
                    <div class="subcategory-container">
                        <div class="subcategory-options">
                            <label for="footprint-input" class="subcategory-label">
                                Insert <span x-text="currentDataPoint?.level3"></span> in <span x-text="currentDataPoint?.per"></span>
                            </label>
                            <input
                                id="footprint-input"
                                type="number"
                                x-model="inputValue"
                                class="subcategory-select"
                            />
                        </div>
                        <div class="subcategory-options">
                            <label for="footprint-name-input" class="subcategory-label">{{ __('Name your footprint', CO2MARKET_TEXT_DOMAIN) }}</label>
                            <input
                                id="footprint-name-input"
                                type="text"
                                x-model="footprintName"
                                class="subcategory-select"
                            />
                        </div>
                    </div>
                </div>
            </template>

            <!-- Step 4: Results -->
            <template x-if="step === 4">
                <div class="flex flex-col justify-start">
                    <div class="calculations">
                        <div class="info-and-value-container">
                            <div class="additional-info">
                                <div class="subcategory-options">
                                    <div class="info-value">
                                        <span x-text="parseFloat(currentDataPoint?.carbonfootprint.replace(',', '.')) || '-'"></span>
                                        t CO₂e/<span x-text="currentDataPoint?.per || 'not found'"></span>
                                        <span x-show="currentDataPoint?.datasource">*</span>
                                    </div>
                                </div>
                                <div class="subcategory-options">
                                    <div class="info-value">
                                        <b>x</b> <span x-text="inputValue !== undefined ? inputValue : 'not found'"></span>
                                        <span x-text="currentDataPoint?.per || 'not found'"></span> =
                                    </div>
                                </div>
                            </div>
                            <div class="calculated-value-container">
                                <div class="calculated-value">
                                    <div class="calculated-value-number">
                                        <b x-text="calculatedValueRounded"></b>
                                    </div>
                                    <div class="calculated-value-text">t CO₂e</div>
                                </div>
                            </div>
                        </div>

                        <template x-if="currentDataPoint?.datasource">
                            <div class="datasource-container">
                                <div class="info-value text-sm text-gray-400">*<span x-text="currentDataPoint.datasource"></span></div>
                            </div>
                        </template>


                    </div>
                    <div class="certify-offset-buttons">
                            <a href="/calculate/search-consultants">
                                <button class="certify-button">{{ __('Certify carbon footprint', CO2MARKET_TEXT_DOMAIN) }}</button>
                            </a>
                            <a href="/offset">
                                <button class="offset-button">{{ __('Offset carbon footprint', CO2MARKET_TEXT_DOMAIN) }}</button>
                            </a>
                        </div>
                    <div class="mt-4 flex flex-col justify-start">
                      <div class="flex">
                        <button
                            @click="saveFootprint()"
                            :disabled="isSaving || isSaved || !isUserLoggedIn"
                            class="offset-button"
                            :class="{ 'opacity-50': isSaved || !isUserLoggedIn }"
                            x-text="isSaved ? 'Saved' : 'Save'"
                        ></button>
                      </div>
                        <div x-show="!isUserLoggedIn" class="mt-2 text-ox-green-600 text-sm">
                            <a href="{{ get_login_url() }}" class="underline hover:text-ox-green-800">{{ __('Log in', CO2MARKET_TEXT_DOMAIN) }}</a> {{ __('to save this footprint', CO2MARKET_TEXT_DOMAIN) }}
                        </div>
                    </div>
                </div>
            </template>
        </div>
    </div>

    <!-- Navigation Buttons -->
    <div x-show="1 < step && step < 4" class="navigation-buttons">
        <div
            class="previous-button"
            @click="goToStep(step - 1)"
            role="button"
            tabindex="0"
        >
            @include('blocks.icons.go-arrow-left')
            {{ __('Previous', CO2MARKET_TEXT_DOMAIN) }}
        </div>

        <template x-if="step < 4 && value">
            <button
                @click="goToStep(step + 1)"
                class="next-button"
            >
                <div x-show="step === 3">{{ __('Calculate', CO2MARKET_TEXT_DOMAIN) }}</div>
                <div x-show="step !== 3">{{ __('Next', CO2MARKET_TEXT_DOMAIN) }}</div>
            </button>
        </template>
    </div>

    <template x-if="step === 4">
        <div class="navigation-buttons">
            <div
                class="previous-button"
                @click="goToStep(step - 1)"
                role="button"
                tabindex="0"
            >
                @include('blocks.icons.go-arrow-left')
                {{ __('Previous', CO2MARKET_TEXT_DOMAIN) }}
            </div>
            <div
                class="new-footprint-button"
                @click="goToStep(1)"
                role="button"
                tabindex="0"
            >
                {{ __('Calculate a new footprint', CO2MARKET_TEXT_DOMAIN) }}
            </div>
        </div>
    </template>
</div>