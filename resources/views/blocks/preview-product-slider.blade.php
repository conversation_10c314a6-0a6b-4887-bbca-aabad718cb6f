<div class="{{ $block->classes }} w-full py-8" style="{{ $block->inlineStyle }}">
  <div class="flex gap-6 px-4">
    @for ($i = 0; $i < 4; $i++)
      <div class="w-1/4">
        <div class="bg-ox-green-100 rounded-2xl overflow-hidden h-full">
          {{-- Project Image Section --}}
          <div class="relative h-48 bg-ox-green-200">
            <div class="absolute top-4 left-2 right-2">
              <div class="flex gap-2 w-full justify-between">
                <div class="bg-white/80 backdrop-blur-sm rounded-lg px-3 py-1.5 flex gap-2 items-center">
                  <p class="text-sm text-ox-green-600">Score</p>
                  <p class="text-sm font-bold text-ox-green-600">90</p>
                </div>
              </div>
            </div>
          </div>

          {{-- Project Details --}}
          <div class="p-4">
            <div class="flex items-center text-gray-600 mb-2">
              @svg("general_location", "!w-6 !h-auto")
              <span class="text-sm">Sample Location</span>
            </div>

            <b class="text-sm text-left font-bold text-ox-black mb-4 overflow-hidden line-clamp-2"
               style="display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical;">
              Sample Project Title {{ $i + 1 }}
            </b>

            <div class="flex space-x-4">
              <div class="bg-ox-green-200 rounded-lg px-3 py-1.5 flex flex-row gap-2 items-center">
                <p class="text-sm text-ox-green-600">USD</p>
                <p class="text-sm font-bold text-ox-green-600">4.00</p>
              </div>
              <div class="bg-ox-green-200 rounded-lg px-3 py-1.5 flex gap-2 items-center">
                <p class="text-sm text-ox-green-600">Tonnes</p>
                <p class="text-sm font-bold text-ox-green-600">10,000</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    @endfor
  </div>
</div>
