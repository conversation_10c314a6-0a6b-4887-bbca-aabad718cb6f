@vite('resources/css/swiper.css')
@php
/** @var \App\Models\FootprintExample[] $examples */
$unique_id = uniqid('footprint-example-slider-');
$currency = get_woocommerce_currency();
@endphp
<div class="{{ $block->classes }}" style="{{ $block->inlineStyle }}">
  <div class="relative">
    <div class="swiper {{ $slider_id }}">
      <div class="swiper-wrapper h-auto">
        @foreach($examples as $example)
        <div class="swiper-slide">
          <div class="flex flex-col md:flex-row gap-4">
            <!-- Image Section -->
            <a href="{{ get_permalink($example->post->ID) }}" class="w-full md:w-2/3 block group">
              <div class="relative rounded-xl overflow-hidden max-h-[400px] md:h-full transition-all duration-300 hover:shadow-lg group-hover:-translate-y-1">
                <img src="{{ $example->getImageUrl() }}" 
                     alt="{{ $example->post->post_title }}" 
                     class="w-auto h-full object-cover min-w-full"
                     style="object-position: center;">
                <!-- Added gradient overlay -->
                <div class="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"></div>
                <div class="absolute bottom-4 left-4 right-4">  
                  <div class="flex justify-between items-end gap-4">
                    <div class="text-white font-bold text-md uppercase text-left max-w-1/2">
                      {{ $example->post->post_title }}
                    </div>
                    <div class="badge bg-ox-green-400 whitespace-nowrap flex items-center gap-2 h-fit transition-colors duration-300 group-hover:bg-ox-green-200">
                   {{ __("Read more", CO2MARKET_TEXT_DOMAIN) }} 
                      <i class="fa-solid fa-arrow-right transition-transform duration-300 group-hover:translate-x-1"></i>
                    </div>
                  </div>
                </div>
              </div>
            </a>

            <!-- Info Boxes Section -->
            <div class="w-full md:w-1/3 flex flex-col gap-4">
              <!-- Carbon Footprint Box -->
              <div class="bg-ox-green-400 rounded-xl p-6 flex-1">
                <div class="flex items-center gap-4 mb-4">
                  @svg("main_get_footprint", "fill-ox-green-600 !w-12 !h-12")
                  <div class="text-ox-green-600 uppercase text-left text-sm font-bold">{{ __("typical carbon footprint", CO2MARKET_TEXT_DOMAIN) }}</div>
                </div>
                <div class="text-2xl font-bold text-ox-green-600 text-left">
                  {{ $example->total_carbon_footprint }}
                </div>
                <p class="text-sm text-ox-green-600 text-left">{{ __("metric tonnes of CO2e", CO2MARKET_TEXT_DOMAIN) }}</p>
              </div>

              <!-- Offset Cost Box -->
              <div class="bg-ox-green-400 rounded-xl p-6 flex-1">
                <div class="flex items-center gap-4 mb-4">
                  @svg("main_offset", "fill-ox-green-600 !w-12 !h-12")
                  <div class="text-ox-green-600 uppercase text-left text-sm font-bold">{{ __("typical offset cost", CO2MARKET_TEXT_DOMAIN) }}</div>
                </div>
                <div class="text-2xl font-bold text-ox-green-600 text-left">
                  {{ $currency }}
                  {{ $example->typicalOffsetCost() ?? 'N/A' }}
                </div>
                <p class="text-sm text-ox-green-600 text-left">{{ __("metric tonnes of CO2e", CO2MARKET_TEXT_DOMAIN) }}</p>
              </div>
            </div>
          </div>
        </div>
        @endforeach
      </div>
      <div class="swiper-pagination {{ $slider_id }}"></div>
    </div>
    <div class="swiper-button-next {{ $slider_id }} bg-ox-green-400 after:!content-[''] flex items-center justify-center">
      <i class="fa-solid fa-chevron-right text-ox-green-600 text-sm"></i>
    </div>
    <div class="swiper-button-prev {{ $slider_id }} bg-ox-green-400 after:!content-[''] flex items-center justify-center">
      <i class="fa-solid fa-chevron-left text-ox-green-600 text-sm"></i>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const swiper = new Swiper(".{{ $slider_id }}", {
      slidesPerView: 1,
      loop: true,
      spaceBetween: 0,
      autoHeight: true,
      pagination: {
        el: ".swiper-pagination.{{ $slider_id }}",
      },
      navigation: {
        nextEl: '.swiper-button-next.{{ $slider_id }}',
        prevEl: '.swiper-button-prev.{{ $slider_id }}',
      },
    });
  });
</script>
