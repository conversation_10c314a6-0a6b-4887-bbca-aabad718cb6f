@vite('resources/css/swiper.css')

<div class="{{ $block->classes }}  w-full py-8" style="{{ $block->inlineStyle }}">
  <div class="relative">
    <div class="swiper {{ $slider_id }}">
      @php
    /** @var \App\Models\Product[] $products */
      @endphp
      <div class="swiper-wrapper h-auto">
        @foreach($products as $product)
      <div class="swiper-slide">
        @if($product->is_compensation_project())
        <x-project-card :title="$product->get_name()" :price="$product->get_price()"
        :image="wp_get_attachment_url($product->get_image_id())" :product="$product" />
        @else
        <x-consult-card :title="$product->get_name()" :price="$product->get_price()"
        :image="wp_get_attachment_url($product->get_image_id())" :product="$product" />
        @endif
      </div>

    @endforeach
      </div>
      <div class="swiper-pagination {{ $slider_id }}"></div>
    </div>
    <div class="swiper-button-next {{ $slider_id }} bg-ox-green-400 after:!content-[''] flex items-center justify-center">
      <i class="fa-solid fa-chevron-right text-ox-green-600 text-sm"></i>
    </div>
    <div class="swiper-button-prev {{ $slider_id }} bg-ox-green-400 after:!content-[''] flex items-center justify-center">
      <i class="fa-solid fa-chevron-left text-ox-green-600 text-sm"></i>
    </div>
  </div>
</div>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    const swiper = new Swiper(".{{ $slider_id }}", {
      slidesPerView: 4,
      loop: true,
      spaceBetween: 0,
      autoHeight: true,
      pagination: {
        el: ".swiper-pagination.{{ $slider_id }}",
      },
      navigation: {
        nextEl: '.swiper-button-next.{{ $slider_id }}',
        prevEl: '.swiper-button-prev.{{ $slider_id }}',
      },
      breakpoints: {
        1300: {
          slidesPerView: 4,
        },
        768: {
          slidesPerView: 3,
        },
        640: {
          slidesPerView: 2,
        },
      },
    });
  });
</script>
