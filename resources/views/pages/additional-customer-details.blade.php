@extends('layouts.app')

@section('content')
    <div class="my-32 flex items-center justify-center px-4 sm:px-6 lg:px-8">
        <div class="register-page-container mx-auto bg-ox-green-100 p-8 rounded-lg shadow-lg">
            <div class="flex justify-center">
                <a href="{{ home_url('/') }}" class="flex items-center">
                    <img src="{{ Vite::image('logo.webp') }}" alt="{{ get_bloginfo('name', 'display') }}" class="h-11 sm:h-16">
                </a>
            </div>
            <div class="flex flex-col items-center">
                <h2 class="register-heading mt-2 text-center text-3xl font-extrabold">
                    {{ __('Additional Information Required', CO2MARKET_TEXT_DOMAIN) }}
                </h2>
            </div>
            
            @if(session('errors') && session('errors')->has('rate_limit'))
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mt-4" role="alert">
                    <span class="block sm:inline">{{ session('errors')->first('rate_limit') }}</span>
                </div>
            @endif
            
            @if(session('errors') && session('errors')->has('already_submitted'))
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mt-4" role="alert">
                    <span class="block sm:inline">{{ session('errors')->first('already_submitted') }}</span>
                </div>
            @endif
            
            <form id="additional-customer-details-form" method="post" action="{{ home_url('/customer-details-submit') }}" class="register-as-form mt-8 space-y-6">
                @csrf
                <div class="space-y-4">
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-6">
                        <div>
                            <label for="first_name" class="block text-sm">{{ __('First Name', CO2MARKET_TEXT_DOMAIN) }} *</label>
                            <input id="first_name" name="first_name" type="text" required maxlength="64"
                                placeholder="{{ __('Type your first name', CO2MARKET_TEXT_DOMAIN) }}">
                            @if(session('errors') && session('errors')->has('first_name'))
                                <div class="bg-red-100 border border-red-400 text-red-700 px-3 py-2 rounded mt-2">
                                    <p class="text-sm">{{ session('errors')->first('first_name') }}</p>
                                </div>
                            @endif
                        </div>
                        <div>
                            <label for="last_name" class="block text-sm">{{ __('Last Name', CO2MARKET_TEXT_DOMAIN) }} *</label>
                            <input id="last_name" name="last_name" type="text" required maxlength="64"
                                placeholder="{{ __('Type your last name', CO2MARKET_TEXT_DOMAIN) }}">
                            @if(session('errors') && session('errors')->has('last_name'))
                                <div class="bg-red-100 border border-red-400 text-red-700 px-3 py-2 rounded mt-2">
                                    <p class="text-sm">{{ session('errors')->first('last_name') }}</p>
                                </div>
                            @endif
                        </div>
                    </div>

                    <div class="flex items-center">
                        <input id="terms" name="accept_terms" type="checkbox" required
                            class="h-6 w-6 text-ox-green-400 border-2 border-ox-green-400 rounded-md focus:ring-ox-green-400 focus:outline-none">
                        <label for="terms" class="ml-4 block text-sm text-ox-black">
                            {{ __('I accept the', CO2MARKET_TEXT_DOMAIN) }} <span class="ml-1"><a href="{{ home_url('/terms-and-conditions') }}" class="text-ox-green-600 hover:text-ox-green-500">{{ __('terms and conditions', CO2MARKET_TEXT_DOMAIN) }}</a>*</span>
                        </label>
                    </div>
                    @if(session('errors') && session('errors')->has('accept_terms'))
                        <div class="bg-red-100 border border-red-400 text-red-700 px-3 py-2 rounded mt-2">
                            <p class="text-sm">{{ session('errors')->first('accept_terms') }}</p>
                        </div>
                    @endif
                </div>

                <div>
                    <button type="submit" name="submit_customer_details"
                        class="ur-submit-button button button-green button-filled group relative w-full flex justify-center py-2 px-4">
                        {{ __('Submit', CO2MARKET_TEXT_DOMAIN) }}
                    </button>
                </div>
                
                <div class="text-center text-sm text-gray-500 mt-4 mb-2">
                    {{ __('Fields marked with * are required', CO2MARKET_TEXT_DOMAIN) }}
                </div>
                
                <div class="text-center text-sm text-gray-500 mt-2">
                    {{ __('By submitting this form, you agree to our', CO2MARKET_TEXT_DOMAIN) }} 
                    <a href="{{ home_url('/terms-and-conditions') }}" class="text-ox-green-600 hover:underline">
                        {{ __('Terms and Conditions', CO2MARKET_TEXT_DOMAIN) }}
                    </a>
                </div>
            </form>
        </div>
    </div>
@endsection 