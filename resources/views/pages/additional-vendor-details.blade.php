@extends('layouts.app')

@section('content')
    <div class="my-32 flex items-center justify-center px-4 sm:px-6 lg:px-8">
        <div class="register-page-container mx-auto bg-ox-green-100 p-8 rounded-lg shadow-lg">
            <div class="flex justify-center">
                <a href="{{ home_url('/') }}" class="flex items-center">
                    <img src="{{ Vite::image('logo.webp') }}" alt="{{ get_bloginfo('name', 'display') }}" class="h-11 sm:h-16">
                </a>
            </div>
            <div class="flex flex-col items-center">
                <h2 class="register-heading mt-2 text-center text-3xl font-extrabold">
                    {{ __('Additional Information Required', CO2MARKET_TEXT_DOMAIN) }}
                </h2>
            </div>
            
            @if(session('errors') && session('errors')->has('rate_limit'))
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mt-4" role="alert">
                    <span class="block sm:inline">{{ session('errors')->first('rate_limit') }}</span>
                </div>
            @endif
            
            @if(session('errors') && session('errors')->has('already_submitted'))
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mt-4" role="alert">
                    <span class="block sm:inline">{{ session('errors')->first('already_submitted') }}</span>
                </div>
            @endif
            
            <form id="additional-vendor-details-form" method="post" action="{{ home_url('/vendor-details-submit') }}" class="register-as-form mt-8 space-y-6" x-data="vendorDetailsForm">
                @csrf
                <div class="space-y-4">
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-6">
                        <div>
                            <label for="first_name" class="block text-sm">{{ __('First Name', CO2MARKET_TEXT_DOMAIN) }} *</label>
                            <input id="first_name" name="first_name" type="text" required maxlength="64"
                                placeholder="{{ __('Type your first name', CO2MARKET_TEXT_DOMAIN) }}">
                            @if(session('errors') && session('errors')->has('first_name'))
                                <div class="bg-red-100 border border-red-400 text-red-700 px-3 py-2 rounded mt-2">
                                    <p class="text-sm">{{ session('errors')->first('first_name') }}</p>
                                </div>
                            @endif
                        </div>
                        <div>
                            <label for="last_name" class="block text-sm">{{ __('Last Name', CO2MARKET_TEXT_DOMAIN) }} *</label>
                            <input id="last_name" name="last_name" type="text" required maxlength="64"
                                placeholder="{{ __('Type your last name', CO2MARKET_TEXT_DOMAIN) }}">
                            @if(session('errors') && session('errors')->has('last_name'))
                                <div class="bg-red-100 border border-red-400 text-red-700 px-3 py-2 rounded mt-2">
                                    <p class="text-sm">{{ session('errors')->first('last_name') }}</p>
                                </div>
                            @endif
                        </div>
                    </div>

                    <div>
                        <label for="organization_name" class="block text-sm">{{ __('Organization Name', CO2MARKET_TEXT_DOMAIN) }} *</label>
                        <input id="organization_name" name="organization_name" type="text" required maxlength="64"
                            placeholder="{{ __('Type your organization name', CO2MARKET_TEXT_DOMAIN) }}" 
                            class="w-full">
                        @if(session('errors') && session('errors')->has('organization_name'))
                            <div class="bg-red-100 border border-red-400 text-red-700 px-3 py-2 rounded mt-2">
                                <p class="text-sm">{{ session('errors')->first('organization_name') }}</p>
                            </div>
                        @endif
                    </div>

                    <div>
                        <label for="business_id" class="block text-sm">{{ __('Business ID', CO2MARKET_TEXT_DOMAIN) }} *</label>
                        <input id="business_id" name="business_id" type="text" required maxlength="26"
                            placeholder="{{ __('Type your business ID', CO2MARKET_TEXT_DOMAIN) }}" 
                            class="w-full">
                        @if(session('errors') && session('errors')->has('business_id'))
                            <div class="bg-red-100 border border-red-400 text-red-700 px-3 py-2 rounded mt-2">
                                <p class="text-sm">{{ session('errors')->first('business_id') }}</p>
                            </div>
                        @endif
                    </div>
                    
                    <div>
                        <label for="vendor_country" class="block text-sm">{{ __('Country', CO2MARKET_TEXT_DOMAIN) }} *</label>
                        <input id="vendor_country" name="vendor_country" type="text" required
                            placeholder="{{ __('Select your country', CO2MARKET_TEXT_DOMAIN) }}"
                            class="w-full">
                        @if(session('errors') && session('errors')->has('vendor_country'))
                            <div class="bg-red-100 border border-red-400 text-red-700 px-3 py-2 rounded mt-2">
                                <p class="text-sm">{{ session('errors')->first('vendor_country') }}</p>
                            </div>
                        @endif
                    </div>

                    <div>
                        <label for="vendor_signature" class="block text-sm">{{ __('Signature', CO2MARKET_TEXT_DOMAIN) }} *</label>
                        <canvas id="vendor_signature_pad" class="border border-gray-300 w-full rounded-md" x-ref="canvas"></canvas>
                        <input type="hidden" name="vendor_signature" id="vendor_signature" x-ref="signatureInput">
                        <div class="mt-2 flex justify-between items-center">
                            <button type="button" id="clear_signature" class="button button-green button-outline text-sm text-ox-green-600 bg-white" @click="clearSignature">
                                {{ __('Clear Signature', CO2MARKET_TEXT_DOMAIN) }}
                            </button>
                        </div>
                        <div x-show="signatureError" x-cloak class="bg-red-100 border border-red-400 text-red-700 px-3 py-2 rounded mt-2">
                            <p id="signature-error" class="text-sm">
                                {{ __('Please draw your signature in the signature pad.', CO2MARKET_TEXT_DOMAIN) }}
                            </p>
                        </div>
                        @if(session('errors') && session('errors')->has('vendor_signature'))
                            <div class="bg-red-100 border border-red-400 text-red-700 px-3 py-2 rounded mt-2">
                                <p class="text-sm">{{ session('errors')->first('vendor_signature') }}</p>
                            </div>
                        @endif
                    </div>
                </div>

                <div>
                    <button type="submit" name="submit_vendor_details"
                        class="ur-submit-button button button-green button-filled group relative w-full flex justify-center py-2 px-4">
                        {{ __('Submit', CO2MARKET_TEXT_DOMAIN) }}
                    </button>
                </div>
                
                <div class="text-center text-sm text-gray-500 mt-4 mb-2">
                    {{ __('Fields marked with * are required', CO2MARKET_TEXT_DOMAIN) }}
                </div>
                
                <div class="text-center text-sm text-gray-500 mt-2">
                    {{ __('By submitting this form, you agree to our', CO2MARKET_TEXT_DOMAIN) }} 
                    <span class="ml-1"><a href="{{ home_url('/terms-and-conditions') }}" class="text-ox-green-600 hover:underline">
                        {{ __('Terms and Conditions', CO2MARKET_TEXT_DOMAIN) }}
                    </a></span>
                </div>
            </form>
        </div>
    </div>

    @push('styles')
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/country-select-js/2.1.0/css/countrySelect.min.css">
    @endpush

    @push('scripts')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/country-select-js/2.1.0/js/countrySelect.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/signature_pad@2.3.2/dist/signature_pad.min.js"></script>
    <script>
        document.addEventListener('alpine:init', () => {
            Alpine.data('vendorDetailsForm', () => ({
                signaturePad: null,
                signatureError: false,

                init() {
                    // Initialize signature pad
                    this.$nextTick(() => {
                        this.signaturePad = new SignaturePad(this.$refs.canvas);
                        this.resizeCanvas();
                        window.addEventListener('resize', () => this.resizeCanvas());
                    });

                    // Initialize country selector with jQuery
                    this.$nextTick(() => {
                        if (typeof jQuery !== 'undefined' && typeof jQuery.fn.countrySelect !== 'undefined') {
                            jQuery('#vendor_country').countrySelect({
                                defaultCountry: "fi",
                                preferredCountries: ['fi', 'us', 'gb', 'ca', 'de', 'fr', 'au', 'in', 'jp']
                            });
                        }
                    });

                    // Form submission handling
                    this.$el.addEventListener('submit', (e) => {
                        if (this.signaturePad && (this.signaturePad.isEmpty() || this.isEmptySignature(this.signaturePad.toDataURL()))) {
                            e.preventDefault();
                            this.signatureError = true;
                            // Scroll to signature error
                            this.$refs.canvas.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        } else {
                            this.signatureError = false;
                            this.$refs.signatureInput.value = this.signaturePad.toDataURL();
                        }
                    });
                },

                clearSignature() {
                    if (this.signaturePad) {
                        this.signaturePad.clear();
                        this.signatureError = false;
                    }
                },

                resizeCanvas() {
                    if (!this.signaturePad) return;
                    
                    const canvas = this.$refs.canvas;
                    const ratio = Math.max(window.devicePixelRatio || 1, 1);
                    const width = canvas.offsetWidth;
                    const height = width * 1/3;
                    
                    canvas.width = width * ratio;
                    canvas.height = height * ratio;
                    canvas.getContext("2d").scale(ratio, ratio);
                    
                    // Need to clear and redraw after resize
                    this.signaturePad.clear();
                },

                isEmptySignature(signatureData) {
                    // Check if the signature is the default empty signature (just contains the transparent background)
                    const emptySignaturePrefix = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAZAAAADICAYAAADGFbfiAAAAAXNSR0IArs4c6QAABxZJRE';
                    return signatureData.startsWith(emptySignaturePrefix);
                }
            }));
        });
    </script>
    @endpush
@endsection 