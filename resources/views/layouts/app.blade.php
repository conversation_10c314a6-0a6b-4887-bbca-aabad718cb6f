<!doctype html>
<html @php(language_attributes()) class="h-full">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}" data-update-uri="/livewire/update" />

    {{-- Swiper.js --}}
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11.0.0/swiper-bundle.min.css" />
    <script src="https://cdn.jsdelivr.net/npm/swiper@11.0.0/swiper-bundle.min.js"></script>

    @viteReactRefresh
    @vite(['resources/js/components.tsx', 'resources/js/app.tsx'])
    @php(do_action('get_header'))
    @php(wp_head())
    @livewireStyles
    @stack('styles')
</head>

<body @php(body_class('h-full'))>
    @php(wp_body_open())
    @livewireScripts
    @stack('scripts')


    @include('sections.header')
    <div id="app" class="min-h-full flex flex-col bg-white px-6 py-6">

        <a class="sr-only focus:not-sr-only" href="#main">
            {{ __('Skip to content', 'sage') }}
        </a>


        <main id="main" class="main flex-1">
            @yield('content')
        </main>

        @hasSection('sidebar')
            <aside class="sidebar">
                @yield('sidebar')
            </aside>
        @endif


    </div>
    @include('sections.footer')
    @php(do_action('get_footer'))
    @php(wp_footer())
    <x-toast />

</body>

</html>