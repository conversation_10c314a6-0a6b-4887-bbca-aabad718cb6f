@extends('layouts.app')

@push('scripts')
<script>
function removeCartItem(key) {
    const formData = new FormData();
    formData.append('cart_item_key', key);
    formData.append('_wpnonce', '{{ wp_create_nonce("woocommerce-cart") }}');
    formData.append('_wp_http_referer', '{{ wc_get_cart_url() }}');
    
    fetch('/?wc-ajax=remove_from_cart', {
        method: 'POST',
        credentials: 'same-origin',
        body: formData
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        return response.text();
    })
    .then(() => {
        window.location.reload();
    })
    .catch(error => {
        console.error('Error:', error);
        window.location.reload();
    });
}
</script>
@endpush

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    @if (WC()->cart->is_empty())
        <div class="text-center py-8">
            <p class="text-xl mb-4">{{ __('Your cart is currently empty.') }}</p>
            <a href="{{ get_permalink(wc_get_page_id('shop')) }}" 
               class="inline-flex items-center px-4 py-2 rounded-md text-sm font-medium bg-ox-green-400 text-ox-green-600 hover:bg-ox-green-200 transition-colors duration-200">
                {{ __('Return to shop') }}
            </a>
        </div>
    @else
        <h1 class="text-xl sm:text-[1.75rem] font-bold text-ox-green-600 leading-[1.2em] uppercase tracking-[0.02em] mb-12">
            {{ __('Shopping Cart') }}
        </h1>
        
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Cart Items -->
            <div class="lg:col-span-2">
                <div class="space-y-6">
                    @foreach (WC()->cart->get_cart() as $cart_item_key => $cart_item)
                        @php
                            $product = wc_get_product($cart_item['data']->get_id());
                            $thumbnail = $product->get_image(['class' => 'w-full h-full object-cover rounded-lg bg-ox-green-100']);
                            $is_consultation = has_term('consult-service', 'product_cat', $product->get_id());
                            $is_compensation = has_term('compensation-project', 'product_cat', $product->get_id());
                            $footprint_name = get_field('footprint_name', $product->get_id());
                            $time_estimate = get_field('time_estimate', $product->get_id());
                            $footprint = get_field('footprint', $product->get_id());
                        @endphp
                        
                        <div class="bg-ox-green-100 rounded-lg p-6 relative">
                            <!-- Remove button - Absolute positioned -->
                            <form method="post" class="absolute top-2 right-2" onsubmit="event.preventDefault(); removeCartItem('{{ $cart_item_key }}');">
                                @csrf
                                <button type="submit"
                                        class="text-ox-gray-500 hover:text-ox-green-600 transition-colors duration-200 text-[0.7rem] flex items-center gap-2">
                                    <span class="hidden sm:inline">{{ __('Remove item') }}</span>
                                    @svg('remove_item_x', ['class' => 'w-12 h-12 sm:w-8 sm:h-8'])
                                </button>
                            </form>

                            <div class="flex flex-col sm:flex-row sm:items-start">
                                <!-- Product Image -->
                                <div class="flex-shrink-0 w-32 h-24 bg-ox-green-200 rounded-lg overflow-hidden">
                                    @if($thumbnail)
                                        {!! $thumbnail !!}
                                    @endif
                                </div>

                                <!-- Product Info -->
                                <div class="mt-6 sm:mt-0 sm:ml-6 flex-grow relative">
                                    <!-- Category Tag -->
                                    <div class="mb-4 sm:mb-6">
                                        <span class="inline-flex px-3 py-1.5 text-sm font-medium text-ox-green-600 bg-ox-green-200 rounded-md">
                                            {{ $is_consultation ? 'Calculate' : ($is_compensation ? 'Compensation project' : 'Other') }}
                                        </span>
                                    </div>
                                    
                                    <!-- Product Title -->
                                    <h3 class="text-base font-bold text-ox-black mb-6 tracking-[0.02em] normal-case">
                                        <a href="{{ $product->get_permalink() }}" class="hover:text-ox-green-600 transition-colors duration-200">
                                            {{ $product->get_name() }}
                                        </a>
                                    </h3>
                                    
                                    <!-- Product Details -->
                                    {{-- Initialize Alpine data context to access the store --}}
                                    <div class="space-y-2" x-data> 
                                        @if($is_consultation)
                                            {{-- Keep existing logic for consultation services --}}
                                            <div class="flex flex-col sm:flex-row sm:flex-wrap">
                                                <span class="text-sm text-ox-black font-bold w-full sm:w-32">{{ __('Footprint name') }}</span>
                                                <span class="text-sm text-ox-black mt-1 sm:mt-0 sm:ml-12">{{ $footprint_name ?: '-' }}</span>
                                            </div>
                                            <div class="flex flex-col sm:flex-row sm:flex-wrap">
                                                <span class="text-sm text-ox-black font-bold w-full sm:w-32">{{ __('Time estimate') }}</span>
                                                <span class="text-sm text-ox-black mt-1 sm:mt-0 sm:ml-12">{{ $time_estimate ?: '-' }}</span>
                                            </div>
                                        @else
                                            {{-- Use Alpine store for non-consultation items --}}
                                            <div class="flex flex-col sm:flex-row sm:flex-wrap">
                                                <span class="text-sm text-ox-black font-bold w-full sm:w-32">{{ __('Footprint name') }}</span>
                                                <span class="text-sm text-ox-black mt-1 sm:mt-0 sm:ml-12" x-text="$store.selectedProcess.name ? $store.selectedProcess.name : '-'"></span>
                                            </div>
                                            <div class="flex flex-col sm:flex-row sm:flex-wrap">
                                                <span class="text-sm text-ox-black font-bold w-full sm:w-32">{{ __('Footprint') }}</span>
                                                <span class="text-sm text-ox-black mt-1 sm:mt-0 sm:ml-12">
                                                    <span x-text="$store.selectedProcess.total_emissions ? $store.selectedProcess.total_emissions : '-'"></span> CO₂e
                                                </span>
                                            </div>
                                            @if($is_compensation)
                                                <div class="flex flex-col sm:flex-row sm:flex-wrap">
                                                    <span class="text-sm text-ox-black font-bold w-full sm:w-32">{{ __('Offset amount') }}</span>
                                                    <span class="text-sm text-ox-black mt-1 sm:mt-0 sm:ml-12">{{ $cart_item['quantity'] }} CO₂e</span>
                                                </div>
                                            @endif
                                        @endif
                                    </div>

                                    <!-- Price -->
                                    <div class="mt-6 sm:mt-4 sm:absolute sm:bottom-0 sm:right-0">
                                        <div class="inline-block text-lg font-bold text-ox-green-600 bg-ox-green-200 px-4 py-2 rounded-md whitespace-nowrap">
                                            {!! wc_price($cart_item['line_total']) !!}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
            
            <!-- Cart Totals -->
            <div class="lg:col-span-1">
                <div class="bg-ox-green-100 p-12 rounded-lg">
                    <h2 class="text-xl font-bold text-ox-black mb-6">{{ __('Cart Totals') }}</h2>
                    
                    <div class="space-y-4">
                        <div class="flex justify-between items-center mb-6">
                            <span class="text-ox-black">{{ __('Subtotal') }}</span>
                            <span class="text-ox-black font-bold">{!! WC()->cart->get_cart_subtotal() !!}</span>
                        </div>
                        
                        @if (WC()->cart->get_shipping_total() > 0)
                            <div class="flex justify-between items-center">
                                <span class="text-ox-black">{{ __('Shipping') }}</span>
                                <span class="text-ox-black">{!! WC()->cart->get_cart_shipping_total() !!}</span>
                            </div>
                        @endif
                        
                        <div class="flex justify-between items-center pt-4 pb-4 border-t border-b border-ox-green-300">
                            <span class="text-ox-black font-bold">{{ __('Total') }}</span>
                            <span class="text-ox-black font-bold">{!! WC()->cart->get_cart_total() !!}</span>
                        </div>
                        
                        <div class="flex sm:justify-end justify-start">
                            <a href="{{ wc_get_checkout_url() }}" 
                               class="mt-4 inline-flex justify-center items-center px-6 py-3 rounded-md text-sm font-medium bg-ox-green-400 text-ox-green-600 hover:bg-ox-green-200 transition-colors duration-200">
                                {{ __('Proceed to checkout') }}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>
@endsection
