<?xml version="1.0"?>
<psalm errorLevel="7" resolveFromConfigFile="true" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="https://getpsalm.org/schema/config" xsi:schemaLocation="https://getpsalm.org/schema/config vendor/vimeo/psalm/config.xsd" findUnusedBaselineEntry="true" findUnusedCode="true">
  <projectFiles>
    <directory name="app" />
    <file name="functions.php" />
    <ignoreFiles>
      <directory name="vendor" />
    </ignoreFiles>
  </projectFiles>
  <plugins>
    <pluginClass class="PsalmWordPress\Plugin" />
  </plugins>
  <stubs>
    <file name="vendor/php-stubs/acf-pro-stubs/acf-pro-stubs.php" />
    <file name="vendor/php-stubs/woocommerce-stubs/woocommerce-stubs.php" />
    <file name="app/Stubs/psalm-stubs.php" />
  </stubs>
</psalm>