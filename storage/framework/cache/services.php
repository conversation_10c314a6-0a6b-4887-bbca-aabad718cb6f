<?php return array (
  'providers' => 
  array (
    0 => 'Illuminate\\Auth\\AuthServiceProvider',
    1 => 'Illuminate\\Broadcasting\\BroadcastServiceProvider',
    2 => 'Illuminate\\Bus\\BusServiceProvider',
    3 => 'Illuminate\\Cache\\CacheServiceProvider',
    4 => 'Illuminate\\Concurrency\\ConcurrencyServiceProvider',
    5 => 'Illuminate\\Cookie\\CookieServiceProvider',
    6 => 'Illuminate\\Database\\DatabaseServiceProvider',
    7 => 'Illuminate\\Encryption\\EncryptionServiceProvider',
    8 => 'Illuminate\\Filesystem\\FilesystemServiceProvider',
    9 => 'Illuminate\\Foundation\\Providers\\FoundationServiceProvider',
    10 => 'Illuminate\\Hashing\\HashServiceProvider',
    11 => 'Illuminate\\Mail\\MailServiceProvider',
    12 => 'Illuminate\\Notifications\\NotificationServiceProvider',
    13 => 'Illuminate\\Pagination\\PaginationServiceProvider',
    14 => 'Illuminate\\Auth\\Passwords\\PasswordResetServiceProvider',
    15 => 'Illuminate\\Pipeline\\PipelineServiceProvider',
    16 => 'Illuminate\\Queue\\QueueServiceProvider',
    17 => 'Illuminate\\Redis\\RedisServiceProvider',
    18 => 'Illuminate\\Session\\SessionServiceProvider',
    19 => 'Illuminate\\Translation\\TranslationServiceProvider',
    20 => 'Illuminate\\Validation\\ValidationServiceProvider',
    21 => 'Roots\\Acorn\\Assets\\AssetsServiceProvider',
    22 => 'Roots\\Acorn\\Filesystem\\FilesystemServiceProvider',
    23 => 'Roots\\Acorn\\Providers\\AcornServiceProvider',
    24 => 'Roots\\Acorn\\Providers\\QueueServiceProvider',
    25 => 'Roots\\Acorn\\View\\ViewServiceProvider',
    26 => 'Illuminate\\Foundation\\Providers\\ComposerServiceProvider',
    27 => 'Illuminate\\Database\\MigrationServiceProvider',
    28 => 'Barryvdh\\LaravelIdeHelper\\IdeHelperServiceProvider',
    29 => 'BladeUI\\Icons\\BladeIconsServiceProvider',
    30 => 'Dedoc\\Scramble\\ScrambleServiceProvider',
    31 => 'Genero\\Sage\\WooCommerce\\WooCommerceServiceProvider',
    32 => 'Inertia\\ServiceProvider',
    33 => 'Laravel\\Pail\\PailServiceProvider',
    34 => 'Livewire\\LivewireServiceProvider',
    35 => 'Log1x\\AcfComposer\\Providers\\AcfComposerServiceProvider',
    36 => 'Carbon\\Laravel\\ServiceProvider',
    37 => 'Termwind\\Laravel\\TermwindServiceProvider',
    38 => 'Spatie\\Tail\\TailServiceProvider',
    39 => 'App\\Providers\\ThemeServiceProvider',
    40 => 'App\\Providers\\RouteServiceProvider',
    41 => 'App\\Providers\\PriceUpdaterServiceProvider',
    42 => 'Genero\\Sage\\WooCommerce\\WooCommerceServiceProvider',
    43 => 'Inertia\\ServiceProvider',
  ),
  'eager' => 
  array (
    0 => 'Illuminate\\Auth\\AuthServiceProvider',
    1 => 'Illuminate\\Cookie\\CookieServiceProvider',
    2 => 'Illuminate\\Database\\DatabaseServiceProvider',
    3 => 'Illuminate\\Encryption\\EncryptionServiceProvider',
    4 => 'Illuminate\\Filesystem\\FilesystemServiceProvider',
    5 => 'Illuminate\\Foundation\\Providers\\FoundationServiceProvider',
    6 => 'Illuminate\\Notifications\\NotificationServiceProvider',
    7 => 'Illuminate\\Pagination\\PaginationServiceProvider',
    8 => 'Illuminate\\Session\\SessionServiceProvider',
    9 => 'Roots\\Acorn\\Assets\\AssetsServiceProvider',
    10 => 'Roots\\Acorn\\Filesystem\\FilesystemServiceProvider',
    11 => 'Roots\\Acorn\\Providers\\AcornServiceProvider',
    12 => 'Roots\\Acorn\\Providers\\QueueServiceProvider',
    13 => 'Roots\\Acorn\\View\\ViewServiceProvider',
    14 => 'BladeUI\\Icons\\BladeIconsServiceProvider',
    15 => 'Dedoc\\Scramble\\ScrambleServiceProvider',
    16 => 'Genero\\Sage\\WooCommerce\\WooCommerceServiceProvider',
    17 => 'Inertia\\ServiceProvider',
    18 => 'Laravel\\Pail\\PailServiceProvider',
    19 => 'Livewire\\LivewireServiceProvider',
    20 => 'Log1x\\AcfComposer\\Providers\\AcfComposerServiceProvider',
    21 => 'Carbon\\Laravel\\ServiceProvider',
    22 => 'Termwind\\Laravel\\TermwindServiceProvider',
    23 => 'Spatie\\Tail\\TailServiceProvider',
    24 => 'App\\Providers\\ThemeServiceProvider',
    25 => 'App\\Providers\\RouteServiceProvider',
    26 => 'App\\Providers\\PriceUpdaterServiceProvider',
    27 => 'Genero\\Sage\\WooCommerce\\WooCommerceServiceProvider',
    28 => 'Inertia\\ServiceProvider',
  ),
  'deferred' => 
  array (
    'Illuminate\\Broadcasting\\BroadcastManager' => 'Illuminate\\Broadcasting\\BroadcastServiceProvider',
    'Illuminate\\Contracts\\Broadcasting\\Factory' => 'Illuminate\\Broadcasting\\BroadcastServiceProvider',
    'Illuminate\\Contracts\\Broadcasting\\Broadcaster' => 'Illuminate\\Broadcasting\\BroadcastServiceProvider',
    'Illuminate\\Bus\\Dispatcher' => 'Illuminate\\Bus\\BusServiceProvider',
    'Illuminate\\Contracts\\Bus\\Dispatcher' => 'Illuminate\\Bus\\BusServiceProvider',
    'Illuminate\\Contracts\\Bus\\QueueingDispatcher' => 'Illuminate\\Bus\\BusServiceProvider',
    'Illuminate\\Bus\\BatchRepository' => 'Illuminate\\Bus\\BusServiceProvider',
    'Illuminate\\Bus\\DatabaseBatchRepository' => 'Illuminate\\Bus\\BusServiceProvider',
    'cache' => 'Illuminate\\Cache\\CacheServiceProvider',
    'cache.store' => 'Illuminate\\Cache\\CacheServiceProvider',
    'cache.psr6' => 'Illuminate\\Cache\\CacheServiceProvider',
    'memcached.connector' => 'Illuminate\\Cache\\CacheServiceProvider',
    'Illuminate\\Cache\\RateLimiter' => 'Illuminate\\Cache\\CacheServiceProvider',
    'Illuminate\\Concurrency\\ConcurrencyManager' => 'Illuminate\\Concurrency\\ConcurrencyServiceProvider',
    'hash' => 'Illuminate\\Hashing\\HashServiceProvider',
    'hash.driver' => 'Illuminate\\Hashing\\HashServiceProvider',
    'mail.manager' => 'Illuminate\\Mail\\MailServiceProvider',
    'mailer' => 'Illuminate\\Mail\\MailServiceProvider',
    'Illuminate\\Mail\\Markdown' => 'Illuminate\\Mail\\MailServiceProvider',
    'auth.password' => 'Illuminate\\Auth\\Passwords\\PasswordResetServiceProvider',
    'auth.password.broker' => 'Illuminate\\Auth\\Passwords\\PasswordResetServiceProvider',
    'Illuminate\\Contracts\\Pipeline\\Hub' => 'Illuminate\\Pipeline\\PipelineServiceProvider',
    'pipeline' => 'Illuminate\\Pipeline\\PipelineServiceProvider',
    'queue' => 'Illuminate\\Queue\\QueueServiceProvider',
    'queue.connection' => 'Illuminate\\Queue\\QueueServiceProvider',
    'queue.failer' => 'Illuminate\\Queue\\QueueServiceProvider',
    'queue.listener' => 'Illuminate\\Queue\\QueueServiceProvider',
    'queue.worker' => 'Illuminate\\Queue\\QueueServiceProvider',
    'redis' => 'Illuminate\\Redis\\RedisServiceProvider',
    'redis.connection' => 'Illuminate\\Redis\\RedisServiceProvider',
    'translator' => 'Illuminate\\Translation\\TranslationServiceProvider',
    'translation.loader' => 'Illuminate\\Translation\\TranslationServiceProvider',
    'validator' => 'Illuminate\\Validation\\ValidationServiceProvider',
    'validation.presence' => 'Illuminate\\Validation\\ValidationServiceProvider',
    'Illuminate\\Contracts\\Validation\\UncompromisedVerifier' => 'Illuminate\\Validation\\ValidationServiceProvider',
    'composer' => 'Illuminate\\Foundation\\Providers\\ComposerServiceProvider',
    'migrator' => 'Illuminate\\Database\\MigrationServiceProvider',
    'migration.repository' => 'Illuminate\\Database\\MigrationServiceProvider',
    'migration.creator' => 'Illuminate\\Database\\MigrationServiceProvider',
    'Illuminate\\Database\\Migrations\\Migrator' => 'Illuminate\\Database\\MigrationServiceProvider',
    'Illuminate\\Database\\Console\\Migrations\\MigrateCommand' => 'Illuminate\\Database\\MigrationServiceProvider',
    'Illuminate\\Database\\Console\\Migrations\\FreshCommand' => 'Illuminate\\Database\\MigrationServiceProvider',
    'Illuminate\\Database\\Console\\Migrations\\InstallCommand' => 'Illuminate\\Database\\MigrationServiceProvider',
    'Illuminate\\Database\\Console\\Migrations\\RefreshCommand' => 'Illuminate\\Database\\MigrationServiceProvider',
    'Illuminate\\Database\\Console\\Migrations\\ResetCommand' => 'Illuminate\\Database\\MigrationServiceProvider',
    'Illuminate\\Database\\Console\\Migrations\\RollbackCommand' => 'Illuminate\\Database\\MigrationServiceProvider',
    'Illuminate\\Database\\Console\\Migrations\\StatusCommand' => 'Illuminate\\Database\\MigrationServiceProvider',
    'Illuminate\\Database\\Console\\Migrations\\MigrateMakeCommand' => 'Illuminate\\Database\\MigrationServiceProvider',
    'Barryvdh\\LaravelIdeHelper\\Console\\GeneratorCommand' => 'Barryvdh\\LaravelIdeHelper\\IdeHelperServiceProvider',
    'Barryvdh\\LaravelIdeHelper\\Console\\ModelsCommand' => 'Barryvdh\\LaravelIdeHelper\\IdeHelperServiceProvider',
    'Barryvdh\\LaravelIdeHelper\\Console\\MetaCommand' => 'Barryvdh\\LaravelIdeHelper\\IdeHelperServiceProvider',
    'Barryvdh\\LaravelIdeHelper\\Console\\EloquentCommand' => 'Barryvdh\\LaravelIdeHelper\\IdeHelperServiceProvider',
  ),
  'when' => 
  array (
    'Illuminate\\Broadcasting\\BroadcastServiceProvider' => 
    array (
    ),
    'Illuminate\\Bus\\BusServiceProvider' => 
    array (
    ),
    'Illuminate\\Cache\\CacheServiceProvider' => 
    array (
    ),
    'Illuminate\\Concurrency\\ConcurrencyServiceProvider' => 
    array (
    ),
    'Illuminate\\Hashing\\HashServiceProvider' => 
    array (
    ),
    'Illuminate\\Mail\\MailServiceProvider' => 
    array (
    ),
    'Illuminate\\Auth\\Passwords\\PasswordResetServiceProvider' => 
    array (
    ),
    'Illuminate\\Pipeline\\PipelineServiceProvider' => 
    array (
    ),
    'Illuminate\\Queue\\QueueServiceProvider' => 
    array (
    ),
    'Illuminate\\Redis\\RedisServiceProvider' => 
    array (
    ),
    'Illuminate\\Translation\\TranslationServiceProvider' => 
    array (
    ),
    'Illuminate\\Validation\\ValidationServiceProvider' => 
    array (
    ),
    'Illuminate\\Foundation\\Providers\\ComposerServiceProvider' => 
    array (
    ),
    'Illuminate\\Database\\MigrationServiceProvider' => 
    array (
    ),
    'Barryvdh\\LaravelIdeHelper\\IdeHelperServiceProvider' => 
    array (
    ),
  ),
);