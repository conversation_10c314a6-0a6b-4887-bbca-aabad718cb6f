<?php
  /** @var $processes array<\App\Models\OffsetProcess> */
  /** @var $selectedProcess \App\Models\OffsetProcess|null */

  // Make sure processes is defined and not empty
  $processes = $processes ?? [];
  // Define selectedProcess as a parameter with null default
  $selectedProcess = $selectedProcess ?? null;
  $selectedProcessId = $_COOKIE['selected_process_id'] ?? null;

  if (!$selectedProcess && $selectedProcessId && !empty($processes)) {
      $selectedProcess = collect($processes)->first(fn($p) => $p->id == $selectedProcessId);
  }

  // Ensure we have a default process for initial state
  $selectedProcess = $selectedProcess ?? (!empty($processes) ? collect($processes)->first() : null);
  $currentStep = $selectedProcess ? $selectedProcess->step->getStepNumber() : 0;
?>


<style>
.progress-step-3 {
    border-left: none;
}

.progress-step {
    position: relative;
    display: flex;
    align-items: stretch;
    height: 45px;
    min-width: 150px;
    cursor: pointer;
}

.progress-content {
    display: flex;
    flex: 1;
    border: 2.5px solid #E1F0DC;
    padding-inline: 1rem;
    background-color: #FAFAF5;
    justify-content: center;
    align-items: center;
    z-index: 1;
}

.progress-inner {
    display: flex;
    align-items: center;
}

.progress-cap {
    height: calc(100%);
    fill: none;
    stroke: #E1F0DC;
    stroke-width: 5px;
}

.progress-step:hover .progress-content {
    background-color: #E1F0DC;
}

.progress-step:hover .progress-cap path {
    fill: #E1F0DC;
}

@media (max-width: 639px) {
    .progress-step-2, .progress-step-3 {
        margin-left: -10px;
    }
}
</style>

<script>
  document.addEventListener('alpine:init', () => {
    Alpine.store('process', {
      currentStep: <?php echo e($currentStep); ?>,
      setStep(step) {
        this.currentStep = step;
      }
    });
  });
</script>
<div class="flex flex-col sm:flex-row min-h-[40px] gap-4 sm:gap-1 w-full xl:w-auto items-start sm:items-center justify-between xl:justify-start" x-data>
    <?php if (isset($component)) { $__componentOriginala15687208d8fdfcbf3edee1799a451cd = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginala15687208d8fdfcbf3edee1799a451cd = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.progress-bar.step1','data' => ['class' => 'flex-shrink-0 w-auto','icon' => 'main_get_footprint','link' => '/calculate']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('progress-bar.step1'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'flex-shrink-0 w-auto','icon' => 'main_get_footprint','link' => '/calculate']); ?>
        <div class="ml-[5px] flex items-center">
            <span class="font-bold" style="padding-right: 4px;">1. </span>
            <span>Get footprint</span>
        </div>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginala15687208d8fdfcbf3edee1799a451cd)): ?>
<?php $attributes = $__attributesOriginala15687208d8fdfcbf3edee1799a451cd; ?>
<?php unset($__attributesOriginala15687208d8fdfcbf3edee1799a451cd); ?>
<?php endif; ?>
<?php if (isset($__componentOriginala15687208d8fdfcbf3edee1799a451cd)): ?>
<?php $component = $__componentOriginala15687208d8fdfcbf3edee1799a451cd; ?>
<?php unset($__componentOriginala15687208d8fdfcbf3edee1799a451cd); ?>
<?php endif; ?>

    <?php if (isset($component)) { $__componentOriginalc44e1244db752c79233b884c3b8d2042 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc44e1244db752c79233b884c3b8d2042 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.progress-bar.step2','data' => ['class' => 'flex-shrink-0 w-auto','icon' => 'main_offset','link' => '/offset']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('progress-bar.step2'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'flex-shrink-0 w-auto','icon' => 'main_offset','link' => '/offset']); ?>
       <div class="ml-[5px] flex items-center">
        <span class="font-bold" style="padding-right: 4px;">2. </span>
        <span>Offset</span>
       </div>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc44e1244db752c79233b884c3b8d2042)): ?>
<?php $attributes = $__attributesOriginalc44e1244db752c79233b884c3b8d2042; ?>
<?php unset($__attributesOriginalc44e1244db752c79233b884c3b8d2042); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc44e1244db752c79233b884c3b8d2042)): ?>
<?php $component = $__componentOriginalc44e1244db752c79233b884c3b8d2042; ?>
<?php unset($__componentOriginalc44e1244db752c79233b884c3b8d2042); ?>
<?php endif; ?>

    <?php if (isset($component)) { $__componentOriginal1200d51fb2ac7b3daf5cb45d15dd7238 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal1200d51fb2ac7b3daf5cb45d15dd7238 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.progress-bar.step3','data' => ['class' => 'flex-shrink-0 w-auto','icon' => 'main_communicate','link' => '/communicate']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('progress-bar.step3'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'flex-shrink-0 w-auto','icon' => 'main_communicate','link' => '/communicate']); ?>
        <div class="ml-[5px] flex items-center">
            <span class="font-bold" style="padding-right: 4px;">3. </span>
            <span>Communicate</span>
        </div>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal1200d51fb2ac7b3daf5cb45d15dd7238)): ?>
<?php $attributes = $__attributesOriginal1200d51fb2ac7b3daf5cb45d15dd7238; ?>
<?php unset($__attributesOriginal1200d51fb2ac7b3daf5cb45d15dd7238); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal1200d51fb2ac7b3daf5cb45d15dd7238)): ?>
<?php $component = $__componentOriginal1200d51fb2ac7b3daf5cb45d15dd7238; ?>
<?php unset($__componentOriginal1200d51fb2ac7b3daf5cb45d15dd7238); ?>
<?php endif; ?>
</div>
<?php /**PATH /Users/<USER>/Herd/co2market/wp-content/themes/co2market/resources/views/components/process-progress.blade.php ENDPATH**/ ?>