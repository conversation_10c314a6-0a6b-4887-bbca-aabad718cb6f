<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'addNewLink' => '/calculate',
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'addNewLink' => '/calculate',
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php
  /** @var $processes array<\App\Models\OffsetProcess> */

  $selectedProcessId = App\Models\User::getSelectedProcessId();

  if ($selectedProcessId) {
      $selectedProcess = collect($processes)->first(fn($p) => $p->id == $selectedProcessId);
  } else {
     $selectedProcess = $selectedProcess ?? collect($processes)->first();
  }

  // Ensure we have a default process for initial state
  $defaultProcess = $selectedProcess ?? collect($processes)->first();
  $initialProcessId = $defaultProcess ? $defaultProcess->id : null;
  $collection = collect($processes);
  $jsonProcesses = $collection->map(function ($process) {
    return $process->toArray();
  })->toJson();
?>

<div x-data="{
    open: false,
    selectedProcessId: '<?php echo e($initialProcessId); ?>',
    processes: <?php echo e($jsonProcesses); ?>,
    selectedEmissions: <?php echo e($selectedProcess?->calculateTotalEmissions() ?? 1); ?>, // Default to 1 if no process or emissions
    init() {
        // Initialize the store with the default/initial process
        const initialProcess = this.getSelectedProcess();
        Alpine.store('selectedProcess', {
            name: initialProcess?.name ?? 'Default Footprint',
            total_emissions: initialProcess?.total_emissions ?? 1
        });
        // Call updateQuantity directly to ensure store is updated and event dispatched if needed
        this.updateQuantity();
    },
    setSelectedProcess(processId) {
        this.selectedProcessId = processId;
        document.cookie = `selected_process_id=${processId}; path=/`; //persist the selection
        this.updateQuantity();
    },
    getSelectedProcess() {
        return this.processes.find(p => p.id == this.selectedProcessId);
    },
    updateQuantity() {
        const process = this.getSelectedProcess();
        this.selectedEmissions = process?.total_emissions ?? 1; // Use 1 if no process or emissions
        // Update the Alpine store with the selected process details
        Alpine.store('selectedProcess', {
            name: process?.name ?? 'Default Footprint',
            total_emissions: this.selectedEmissions // Already calculated with default
        });

        // Dispatch event with the new quantity (keep for compatibility if needed by other components)
        this.$dispatch('process-selected', { quantity: this.selectedEmissions });

        // Update step in another store (keeping existing logic)
        const stepNumber = parseInt(process?.step?.step_number) || 1;
        Alpine.store('process').setStep(stepNumber); // Assuming 'process' store is for step tracking
    }
}" x-cloak>
    <?php if(!empty($processes)): ?>
        <div class="relative">
            <div class="flex gap-1 items-center">
                <div class="flex gap-1 items-center justify-center bg-ox-green-400 px-4 py-1 rounded-md">
                    <?php echo e(svg("main_carbon_footprint", "fill-ox-green-600")); ?>
                    <span class="text-ox-green-600 whitespace-nowrap">
                        <b>
                            <span x-text="selectedEmissions"></span>
                            t CO<sub>2</sub>e
                        </b>
                    </span>
                </div>
                <button @click="open = !open" class="w-[280px] h-[42px] border-2 border-ox-green-200 py-1 px-3 rounded-md">
                    <div class="flex justify-between items-center gap-2">
                        <template x-if="!selectedProcessId">
                            <span class="flex items-center"><?php echo e($defaultProcess?->name); ?></span>
                        </template>
                        <template x-if="selectedProcessId">
                            <span class="flex items-center truncate" x-text="getSelectedProcess().name"></span>
                        </template>
                        <div class="transform transition-transform" :class="{ 'rotate-180': open }">
                            <?php echo e(svg('general_arrow_head', 'w-[18px] h-auto')); ?>
                        </div>
                    </div>
                </button>
            </div>

            <div x-show="open" @click.away="open = false"
                class="absolute bg-white border-2 border-ox-green-200 w-[500px] max-w-[90vw] z-[9999] mt-2 rounded-md hidden"
                :class="{ 'hidden': !open }">
                <div class="flex flex-col h-full max-h-[min(400px,80vh)] gap-2 p-4 overflow-y-auto">
                    <div class="flex flex-col gap-2">
                        <?php $__currentLoopData = $processes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $process): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php if (isset($component)) { $__componentOriginalbbc950dd84124ee8ede308a65a43dd2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalbbc950dd84124ee8ede308a65a43dd2c = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.process-selector.calculation-item','data' => ['process' => $process,'isSelected' => $process->id == $selectedProcess?->id]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('process-selector.calculation-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['process' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($process),'is-selected' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($process->id == $selectedProcess?->id)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalbbc950dd84124ee8ede308a65a43dd2c)): ?>
<?php $attributes = $__attributesOriginalbbc950dd84124ee8ede308a65a43dd2c; ?>
<?php unset($__attributesOriginalbbc950dd84124ee8ede308a65a43dd2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalbbc950dd84124ee8ede308a65a43dd2c)): ?>
<?php $component = $__componentOriginalbbc950dd84124ee8ede308a65a43dd2c; ?>
<?php unset($__componentOriginalbbc950dd84124ee8ede308a65a43dd2c); ?>
<?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                    <a href="<?php echo e($addNewLink); ?>" class="flex gap-2 items-center justify-center">
                        <?php echo e(svg('general_plus', "w-6 h-6")); ?>
                        <?php echo e(__('Add new carbon footprint')); ?>

                    </a>
                </div>
            </div>
        </div>
    <?php else: ?>
        <a href="<?php echo e($addNewLink); ?>" class="flex gap-1 items-center">
            <?php if (isset($component)) { $__componentOriginald0f1fd2689e4bb7060122a5b91fe8561 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.button','data' => ['variant' => 'green','style' => 'filled','class' => '!rounded-md w-8 h-8 !p-0']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['variant' => 'green','style' => 'filled','class' => '!rounded-md w-8 h-8 !p-0']); ?>
                <div class="flex items-center">
                    <?php echo e(svg('general_plus', 'w-6 h-6')); ?>
                </div>
             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561)): ?>
<?php $attributes = $__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561; ?>
<?php unset($__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald0f1fd2689e4bb7060122a5b91fe8561)): ?>
<?php $component = $__componentOriginald0f1fd2689e4bb7060122a5b91fe8561; ?>
<?php unset($__componentOriginald0f1fd2689e4bb7060122a5b91fe8561); ?>
<?php endif; ?>
            <div class="bg-white p-2 px-4 rounded-md justify-between whitespace-nowrap">
                <?php echo e(__('Add new carbon footprint')); ?>

            </div>
        </a>
    <?php endif; ?>
</div>
<?php /**PATH /Users/<USER>/Herd/co2market/wp-content/themes/co2market/resources/views/components/process-selector.blade.php ENDPATH**/ ?>