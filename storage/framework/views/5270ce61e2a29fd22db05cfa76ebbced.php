<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['tooltipText']));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['tooltipText']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<div x-data="{ showTooltip: false }" class="relative inline-block">
    <!-- Slot Content -->
    <div @mouseover="showTooltip = true" @mouseleave="showTooltip = false">
        <?php echo e($slot); ?>

    </div>

    <!-- Tooltip Portal -->
    <template x-portal>
        <div x-show="showTooltip" x-transition.opacity.duration.200ms
            class="absolute z-50 p-2 text-sm text-white bg-gray-800 rounded shadow-lg"
            style="top: 100%; left: 50%; transform: translateX(-50%);">
            <?php echo e($tooltipText); ?>

        </div>
    </template>
</div><?php /**PATH /Users/<USER>/Herd/co2market/wp-content/themes/co2market/resources/views/components/tooltip.blade.php ENDPATH**/ ?>