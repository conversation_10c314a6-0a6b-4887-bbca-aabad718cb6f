<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
"product" => null,
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
"product" => null,
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php
/** @var \App\Models\Product $product */
?>


<!-- profile-card.blade.php -->
<a href="<?php echo e($product->get_permalink()); ?>" class="block w-full">
    <div class="bg-ox-green-100 rounded-2xl h-full hover:-translate-y-1 transition-all duration-300 p-6 flex flex-col">
        <!-- Logo/Name Circle -->
        <div class="flex flex-col items-center mb-6">
            <div class="bg-white rounded-full w-24 h-24 flex items-center justify-center mb-4 overflow-hidden">

                <img src="<?php echo e($attributes->get('image', null) ?: @Vite::image('project-placeholder.jpg')); ?>"
                    alt="<?php echo e($title); ?>"
                    class="w-full h-full object-cover">

            </div>
        </div>

        <!-- Info Row -->
        <div class="flex items-center justify-start space-x-4 mb-2">
            <div class="flex items-center text-gray-600">
                <?php echo e(svg("general_location", "!w-6 !h-auto")); ?>
                <span class="text-sm"><?php echo e($product->get_country()); ?></span>
            </div>
            <div class="h-full w-1">|</div>
            <div class="flex items-center space-x-4">
                <div class="flex items-center">
                    <?php echo e(svg("general_star", "!w-5 !h-auto")); ?>
                    <span class="ml-1 text-sm text-ox-black"><?php echo e($product->get_average_rating()); ?></span>
                </div>
                <div class="flex items-center">
                    <?php echo e(svg("general_comment", "!w-5 !h-auto")); ?>
                    <span class="ml-1 text-sm text-ox-black"><?php echo e($product->get_review_count()); ?></span>
                </div>
            </div>
        </div>

        <!-- Company Name -->
        <bold class="text-[14px] font-bold text-gray-900 text-left w-full mb-4"><?php echo e($product->get_name()); ?></bold>

        <!-- Spacer to push badges to bottom -->
        <div class="flex-grow"></div>

        <!-- Action divs -->
        <div class="flex flex-wrap gap-2">
            <?php
                $categories = $product->get_terms_objects();
                
                // Check if categories exist and are valid
                if (is_array($categories) && !empty($categories)) {
                    // Filter out the 'consult-service' category and 'uncategorized'
                    $filtered_categories = array_filter($categories, function($term) {
                        return $term && is_object($term) && isset($term->slug) && 
                               $term->slug !== 'consult-service' && 
                               $term->slug !== 'uncategorized';
                    });
                } else {
                    $filtered_categories = [];
                }
            ?>
            
            <?php if(count($filtered_categories) > 0): ?>
                <?php $__currentLoopData = $filtered_categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="badge green"><?php echo e($category->name); ?></div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php else: ?>
                <div class="badge green"><?php echo e(__('Consulting', CO2MARKET_TEXT_DOMAIN)); ?></div>
            <?php endif; ?>
        </div>
    </div>
</a>
<?php /**PATH /Users/<USER>/Herd/co2market/wp-content/themes/co2market/resources/views/components/consult-card.blade.php ENDPATH**/ ?>