<?php
    $languages = apply_filters('wpml_active_languages', null, ['skip_missing' => 0]);
    $current_language = apply_filters('wpml_current_language', null);
?>

<div class="relative inline-block text-left">
    <div>
        <button type="button"
            class="inline-flex justify-center items-center w-full px-3 py-2 text-base font-bold text-ox-black hover:text-ox-black focus:outline-none"
            id="language-menu-button" aria-expanded="true" aria-haspopup="true">
            <?php echo e(strtoupper($languages[$current_language]['language_code'])); ?>

            <svg class="-mr-1 ml-2 h-6 w-6 text-ox-black" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd"
                    d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                    clip-rule="evenodd" />
            </svg>
        </button>
    </div>

    <div class="hidden origin-top-right absolute overflow-hidden right-0 mt-2 w-16 z-50 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none"
        role="menu" aria-orientation="vertical" aria-labelledby="language-menu-button" tabindex="-1"
        id="language-menu">
        <?php $__currentLoopData = $languages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $code => $language): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php if($code !== $current_language): ?>
                <a href="<?php echo e($language['url']); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    role="menuitem">
                    <?php echo e(strtoupper($language['language_code'])); ?>

                </a>
            <?php endif; ?>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>
</div>

<script>
    document.getElementById('language-menu-button').addEventListener('click', function() {
        document.getElementById('language-menu').classList.toggle('hidden');
    });

    document.addEventListener('click', function(event) {
        if (!event.target.closest('#language-menu-button')) {
            document.getElementById('language-menu').classList.add('hidden');
        }
    });
</script>
<?php /**PATH /Users/<USER>/Herd/co2market/wp-content/themes/co2market/resources/views/components/language-switcher.blade.php ENDPATH**/ ?>