<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['icon' => null, 'link']));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['icon' => null, 'link']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<a href="<?php echo e($link); ?>" class="flex progress-step relative">
    <div class="progress-content progress-step-1  rounded-l-lg !border-r-0">
        <div class="progress-inner flex items-center">
            <?php if($icon): ?>
                <div class="mr-2 flex items-center justify-center" style="width: 32px; height: 32px;">
                    <?php echo e(svg($icon, 'w-8 h-8')); ?>
                </div>
            <?php endif; ?>
            <?php echo e($slot); ?>

        </div>
    </div>
    <div class="absolute -right-[6px] top-0 h-full z-10">
        <svg class="progress-cap right" width="10" height="100%" viewBox="0 0 10 100" overflow="visible"
            xmlns="http://www.w3.org/2000/svg">
            <path
                d="M0,2.5c4.91,0,9.31,3.01,11.08,7.59l13.83,35.61c1.07,2.77,1.07,5.84,0,8.61l-13.83,35.61c-1.78,4.57-6.18,7.59-11.08,7.59"
                vectorEffect="non-scaling-stroke" />
        </svg>
    </div>
</a><?php /**PATH /Users/<USER>/Herd/co2market/wp-content/themes/co2market/resources/views/components/progress-bar/step1.blade.php ENDPATH**/ ?>