<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['icon' => null, 'link']));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['icon' => null, 'link']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<a href="<?php echo e($link); ?>" class="flex progress-step progress-step-3">
    <svg class="progress-cap left translate-x-[1px] z-10" width="17" height="100%" viewBox="0 0 1 105" overflow="visible" xmlns="http://www.w3.org/2000/svg">
        <path vectorEffect="non-scaling-stroke" fill="none" d="M18.92,102.5h-3.89c-8.8,0-14.85-8.84-11.67-17.04l11.04-28.43c1.13-2.91,1.13-6.14,0-9.06L3.36,19.54C.18,11.34,6.23,2.5,15.03,2.5h3.89" />
    </svg>
    <div class="progress-content rounded-r-lg !border-l-0">
        <div class="progress-inner flex items-center">
            <?php if($icon): ?>
                <div class="mr-2 flex items-center justify-center" style="width: 32px; height: 32px;">
                    <?php echo e(svg($icon, 'w-8 h-8')); ?>
                </div>
            <?php endif; ?>
            <?php echo e($slot); ?>

        </div>
    </div>
</a>
<?php /**PATH /Users/<USER>/Herd/co2market/wp-content/themes/co2market/resources/views/components/progress-bar/step3.blade.php ENDPATH**/ ?>