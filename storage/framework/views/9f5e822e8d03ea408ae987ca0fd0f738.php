<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'rating' => 3.5,
    'comments' => 150,
    'location' => 'Korea',
    'title' => 'Taebaek Wind Park (Hasami Samcheok)',
    'projectId' => 'CDM Project UN 9165',
    'price' => "4.00",
    'tonnes' => 10209,
    "product" => null
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'rating' => 3.5,
    'comments' => 150,
    'location' => 'Korea',
    'title' => 'Taebaek Wind Park (Hasami Samcheok)',
    'projectId' => 'CDM Project UN 9165',
    'price' => "4.00",
    'tonnes' => 10209,
    "product" => null
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php
  /** @var \App\Models\Product $product */
  $sdgs = $product->get_sdgs();
  $main_project_type = $product->get_main_project_type();
  $score = $product->get_score();
  
  // Calculate width class based on score
  $widthClass = '';
  if ($score !== null) {
    $scoreValue = min($score, 100);
    if ($scoreValue <= 5) {
      $widthClass = 'w-[5%]';
    } elseif ($scoreValue <= 10) {
      $widthClass = 'w-[10%]';
    } elseif ($scoreValue <= 20) {
      $widthClass = 'w-[20%]';
    } elseif ($scoreValue <= 30) {
      $widthClass = 'w-[30%]';
    } elseif ($scoreValue <= 40) {
      $widthClass = 'w-[40%]';
    } elseif ($scoreValue <= 50) {
      $widthClass = 'w-[50%]';
    } elseif ($scoreValue <= 60) {
      $widthClass = 'w-[60%]';
    } elseif ($scoreValue <= 70) {
      $widthClass = 'w-[70%]';
    } elseif ($scoreValue <= 80) {
      $widthClass = 'w-[80%]';
    } elseif ($scoreValue <= 90) {
      $widthClass = 'w-[90%]';
    } else {
      $widthClass = 'w-full';
    }
  }
?>
<?php if($product): ?>
  <a href="<?php echo e($product->get_permalink()); ?>" class="block w-full">
    <div class="bg-ox-green-100 rounded-2xl overflow-hidden h-full transition-all duration-300  hover:-translate-y-1">
      
      <div class="relative h-48">
        <img src="<?php echo e($attributes->get('image', null) ?: @Vite::image('project-placeholder.jpg')); ?>"
             alt="<?php echo e($title); ?>"
             class="w-full h-full object-cover">
        <div class="absolute top-4 left-4 right-4">
          <div class="flex gap-2 w-full justify-between">
            <?php if($main_project_type): ?>
              <div class="flex space-x-2">
                <div class="w-10 h-10 rounded-md overflow-hidden bg-ox-green-200 flex items-center justify-center">
                  <img src="<?php echo e($main_project_type['icon'] ?: @Vite::svg("projects/project_6_icon.svg")); ?>" alt="<?php echo e($main_project_type['name']); ?>"
                       class="w-full h-full text-ox-green-600">
                </div>
              </div>
            <?php endif; ?>

          <div class="flex gap-2">
            <?php $__currentLoopData = $sdgs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sdg): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
              <?php if (isset($component)) { $__componentOriginal7875b222dc4d64f17fd6d2e345da8799 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal7875b222dc4d64f17fd6d2e345da8799 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.tooltip','data' => ['tooltipText' => 'Ok']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('tooltip'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['tooltipText' => 'Ok']); ?>
                <div class="w-6 h-6 rounded-full bg-ox-green-200 flex items-center justify-center">
                  <img src="<?php echo e($sdg['icon']); ?>" alt="<?php echo e($sdg['name']); ?>" class="w-4 h-4 text-ox-green-600">
                </div>
               <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal7875b222dc4d64f17fd6d2e345da8799)): ?>
<?php $attributes = $__attributesOriginal7875b222dc4d64f17fd6d2e345da8799; ?>
<?php unset($__attributesOriginal7875b222dc4d64f17fd6d2e345da8799); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7875b222dc4d64f17fd6d2e345da8799)): ?>
<?php $component = $__componentOriginal7875b222dc4d64f17fd6d2e345da8799; ?>
<?php unset($__componentOriginal7875b222dc4d64f17fd6d2e345da8799); ?>
<?php endif; ?>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
          </div>
        </div>
        </div>
      </div>

      
      <div class="px-4 py-2 border-gray-200 flex items-center justify-between gap-2 h-10">
        
        <?php if($score !== null): ?>
        <div class="flex items-center space-x-2 w-3/5">
          <div class="w-full bg-ox-green-600 h-2 rounded-none relative">
            <div class="bg-ox-green-400 h-2 rounded-none absolute top-0 left-0 <?php echo e($widthClass); ?>"></div>
          </div>
          <span class="text-ox-black font-bold text-[15px] whitespace-nowrap"><?php echo e($score); ?></span>
        </div>
        <div class="h-full w-1">|</div>
        <?php endif; ?>
        
        <div class="flex items-center space-x-4 flex-shrink-0">
          <div class="flex items-center">
            <?php echo e(svg("general_star", "!w-5 !h-auto")); ?>
            <span class="ml-1 text-sm text-ox-black"><?php echo e($rating); ?></span>
          </div>
          <div class="flex items-center">
            <?php echo e(svg("general_comment", "!w-5 !h-auto")); ?>
            <span class="ml-1 text-sm text-ox-black"><?php echo e($comments); ?></span>
          </div>
        </div>
      </div>

      
      <div class="p-4">
        <div class="flex items-center text-gray-600 mb-2">
          <?php echo e(svg("general_location", "!w-6 !h-auto")); ?>
          <span class="text-sm"><?php echo e($location ?: __('Global', CO2MARKET_TEXT_DOMAIN)); ?></span>
        </div>

        <b class="text-sm text-left font-bold text-ox-black mb-4 overflow-hidden line-clamp-2"
           style="display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical;">
          <?php echo e($title); ?>

        </b>

        <div class="flex flex-wrap gap-x-2 gap-y-2">
          <div class="badge green">
            <?php
              $current_currency = get_woocommerce_currency() ?: 'EUR';
            ?>
            <p class="text-sm text-ox-green-600"><?php echo e($current_currency); ?></p>
            <p class="text-sm font-bold text-ox-green-600"><?php echo e(number_format(ceil($price * 100) / 100, 2)); ?></p>
          </div>
          <div class="badge green">
            <p class="text-sm text-ox-green-600">Tonnes</p>
            <p class="text-sm font-bold text-ox-green-600"><?php echo e(number_format($tonnes, 0)); ?></p>
          </div>
          <?php
            // Get the current currency
            $current_currency = get_woocommerce_currency();
            $priceValue = floatval($price);
          ?>
          <div class="badge score" x-data>
            <?php echo e(svg("main_carbon_footprint", "!w-[22px] !h-[22px] mr-1")); ?>
            <div>
              <p class="text-sm text-ox-green-600"><?php echo e($current_currency); ?> <span class="font-bold" x-text="(Math.ceil((($store.selectedProcess?.total_emissions || 1) * <?php echo e($priceValue); ?>) * 100) / 100).toFixed(2)"></span></p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </a>
<?php else: ?>
  <div class="bg-ox-green-100 rounded-2xl overflow-hidden h-full transition-all duration-300 hover:shadow-lg hover:-translate-y-1">
    <div class="relative h-48">
      <img src="<?php echo e($attributes->get('image', null) ?: @Vite::image('/project-placeholder.jpg')); ?>"
           alt="<?php echo e($title); ?>"
           class="w-full h-full object-cover">
    </div>
  </div>
<?php endif; ?>
<?php /**PATH /Users/<USER>/Herd/co2market/wp-content/themes/co2market/resources/views/components/project-card.blade.php ENDPATH**/ ?>