<?php
    $available_currencies = get_woocommerce_currencies();
    $current_currency = get_woocommerce_currency();
?>

<div class="relative inline-block text-left">
    <div>
        <button type="button"
            class="inline-flex justify-center items-center w-full px-3 py-2 text-base font-bold text-ox-black hover:text-ox-black focus:outline-none"
            id="currency-menu-button" aria-expanded="true" aria-haspopup="true">
            <?php echo e($current_currency); ?>

            <svg class="-mr-1 ml-2 h-6 w-6 text-ox-black" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd"
                    d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                    clip-rule="evenodd" />
            </svg>
        </button>
    </div>

    <div class="hidden origin-top-right absolute right-0 mt-2 w-20 z-50 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none"
        role="menu" aria-orientation="vertical" aria-labelledby="currency-menu-button" tabindex="-1"
        id="currency-menu">
        <?php $__currentLoopData = $available_currencies; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $code => $name): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php if($code !== $current_currency): ?>
                <a href="?currency=<?php echo e($code); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    role="menuitem">
                    <?php echo e($code); ?>

                </a>
            <?php endif; ?>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>
</div>

<script>
    document.getElementById('currency-menu-button').addEventListener('click', function() {
        document.getElementById('currency-menu').classList.toggle('hidden');
    });

    document.addEventListener('click', function(event) {
        if (!event.target.closest('#currency-menu-button')) {
            document.getElementById('currency-menu').classList.add('hidden');
        }
    });
</script>
<?php /**PATH /Users/<USER>/Herd/co2market/wp-content/themes/co2market/resources/views/components/currency-picker.blade.php ENDPATH**/ ?>