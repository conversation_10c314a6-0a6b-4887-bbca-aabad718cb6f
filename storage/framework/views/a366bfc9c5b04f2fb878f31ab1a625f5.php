<?php echo app('Illuminate\Foundation\Vite')('resources/css/swiper.css'); ?>

<div class="<?php echo e($block->classes); ?>  w-full py-8" style="<?php echo e($block->inlineStyle); ?>">
  <div class="relative">
    <div class="swiper <?php echo e($slider_id); ?>">
      <?php
    /** @var \App\Models\Product[] $products */
      ?>
      <div class="swiper-wrapper h-auto">
        <?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
      <div class="swiper-slide">
        <?php if($product->is_compensation_project()): ?>
        <?php if (isset($component)) { $__componentOriginaldbcceabf4a99a34f9999233ae1fef693 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginaldbcceabf4a99a34f9999233ae1fef693 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.project-card','data' => ['title' => $product->get_name(),'price' => $product->get_price(),'image' => wp_get_attachment_url($product->get_image_id()),'product' => $product]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('project-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($product->get_name()),'price' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($product->get_price()),'image' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(wp_get_attachment_url($product->get_image_id())),'product' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($product)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginaldbcceabf4a99a34f9999233ae1fef693)): ?>
<?php $attributes = $__attributesOriginaldbcceabf4a99a34f9999233ae1fef693; ?>
<?php unset($__attributesOriginaldbcceabf4a99a34f9999233ae1fef693); ?>
<?php endif; ?>
<?php if (isset($__componentOriginaldbcceabf4a99a34f9999233ae1fef693)): ?>
<?php $component = $__componentOriginaldbcceabf4a99a34f9999233ae1fef693; ?>
<?php unset($__componentOriginaldbcceabf4a99a34f9999233ae1fef693); ?>
<?php endif; ?>
        <?php else: ?>
        <?php if (isset($component)) { $__componentOriginal6a2fcae3a2db84210a495f01ddc39fde = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal6a2fcae3a2db84210a495f01ddc39fde = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.consult-card','data' => ['title' => $product->get_name(),'price' => $product->get_price(),'image' => wp_get_attachment_url($product->get_image_id()),'product' => $product]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('consult-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($product->get_name()),'price' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($product->get_price()),'image' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(wp_get_attachment_url($product->get_image_id())),'product' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($product)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal6a2fcae3a2db84210a495f01ddc39fde)): ?>
<?php $attributes = $__attributesOriginal6a2fcae3a2db84210a495f01ddc39fde; ?>
<?php unset($__attributesOriginal6a2fcae3a2db84210a495f01ddc39fde); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal6a2fcae3a2db84210a495f01ddc39fde)): ?>
<?php $component = $__componentOriginal6a2fcae3a2db84210a495f01ddc39fde; ?>
<?php unset($__componentOriginal6a2fcae3a2db84210a495f01ddc39fde); ?>
<?php endif; ?>
        <?php endif; ?>
      </div>

    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
      </div>
      <div class="swiper-pagination <?php echo e($slider_id); ?>"></div>
    </div>
    <div class="swiper-button-next <?php echo e($slider_id); ?> bg-ox-green-400 after:!content-[''] flex items-center justify-center">
      <i class="fa-solid fa-chevron-right text-ox-green-600 text-sm"></i>
    </div>
    <div class="swiper-button-prev <?php echo e($slider_id); ?> bg-ox-green-400 after:!content-[''] flex items-center justify-center">
      <i class="fa-solid fa-chevron-left text-ox-green-600 text-sm"></i>
    </div>
  </div>
</div>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    const swiper = new Swiper(".<?php echo e($slider_id); ?>", {
      slidesPerView: 4,
      loop: true,
      spaceBetween: 0,
      autoHeight: true,
      pagination: {
        el: ".swiper-pagination.<?php echo e($slider_id); ?>",
      },
      navigation: {
        nextEl: '.swiper-button-next.<?php echo e($slider_id); ?>',
        prevEl: '.swiper-button-prev.<?php echo e($slider_id); ?>',
      },
      breakpoints: {
        1300: {
          slidesPerView: 4,
        },
        768: {
          slidesPerView: 3,
        },
        640: {
          slidesPerView: 2,
        },
      },
    });
  });
</script>
<?php /**PATH /Users/<USER>/Herd/co2market/wp-content/themes/co2market/resources/views/blocks/product-slider.blade.php ENDPATH**/ ?>