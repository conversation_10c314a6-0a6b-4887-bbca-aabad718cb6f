<header class="bg-white">
    <div class="mx-auto">
        <div class="flex items-center justify-between py-5 px-6">
            <!-- Logo and Search -->
            <div class="flex flex-col xl:flex-row items-start xl:items-center gap-6 xl:gap-[72px] w-full xl:w-auto">
                <a href="<?php echo e(home_url('/')); ?>" class="flex items-center">
                    <img src="<?php echo e(Vite::image('logo.webp')); ?>" alt="<?php echo e(get_bloginfo('name', 'display')); ?>" class="h-11 sm:h-16">
                </a>
                <div class="hidden sm:block w-full xl:w-auto [&_.is-search-form]:w-full [&_.is-search-form]:flex [&_.is-search-form]:bg-white [&_.is-search-form]:rounded-[5px] [&_.is-form-style.is-form-style-3_label]:!border-solid [&_.is-form-style.is-form-style-3_label]:!border-2 [&_.is-form-style.is-form-style-3_label]:!border-ox-green-200 [&_.is-form-style.is-form-style-3_label]:!rounded-[5px] [&_.is-form-style.is-form-style-3_label]:!ring-0 [&_.is-form-style.is-form-style-3_label]:!ring-offset-0 [&_.is-form-style.is-form-style-3_label:has(input:focus)]:!border-ox-green-400 [&_#is-search-input-5909]:!text-ox-green-200 [&_#is-search-input-5909]:!text-base [&_#is-search-input-5909]:placeholder:!text-ox-green-200 [&_#is-search-input-5909]:placeholder:!opacity-100 [&_#is-search-input-5909]:focus:!text-ox-green-600 [&_#is-search-input-5909]:focus:placeholder:!text-ox-green-600 [&_#is-search-input-5909]:active:!text-ox-green-600 [&_#is-search-input-5909.is-search-input]:!text-ox-green-600 [&_.is-form-style.is-form-style-3_input.is-search-input]:!border-0 [&_.is-form-style.is-form-style-3_input.is-search-input]:!bg-white [&_.is-form-style.is-form-style-3_input.is-search-input]:min-w-[400px] [&_.is-form-style.is-form-style-3_input.is-search-input]:w-full [&_.is-form-style.is-form-style-3_input.is-search-input]:!h-10 [&_.is-form-style.is-form-style-3_input.is-search-input]:!px-6 [&_.is-form-style.is-form-style-3_input.is-search-input]:!ring-0 [&_.is-form-style.is-form-style-3_input.is-search-input]:!ring-offset-0 [&_.is-form-style.is-form-style-3_input.is-search-input]:focus:!outline-none [&_.is-form-style.is-form-style-3_input.is-search-input]:focus:!ring-0 [&_.is-form-style.is-form-style-3_input.is-search-input]:focus:!ring-offset-0 [&_.is-ajax-search-no-result]:!text-left [&_#is-ajax-search-result-5909]:!border-ox-green-100 [&_.is-ajax-search-post_a]:!text-ox-green-600 [&_.is-ajax-search-post_a]:!font-bold [&_.is-ajax-woocommerce-actions]:!hidden [&_.is-form-id-5909_.is-search-submit]:!rounded-[5px] [&_.is-form-id-5909_.is-search-icon]:!rounded-[5px] [&_.is-form-style_button.is-search-submit]:!w-[2.8rem] [&_.is-form-style_button.is-search-submit]:!h-[2.8rem] [&_.is-form-style_button.is-search-submit]:!flex [&_.is-form-style_button.is-search-submit]:!items-center [&_.is-form-style_button.is-search-submit]:!justify-center [&_.is-form-style_button.is-search-submit]:!ml-[5px] [&_.is-form-id-5909_.is-search-submit]:!transition-all [&_.is-form-id-5909_.is-search-submit]:!duration-200 [&_.is-form-id-5909_.is-search-icon]:!transition-all [&_.is-form-id-5909_.is-search-icon]:!duration-200 [&_.is-form-id-5909_.is-search-submit:hover]:!bg-ox-green-200 [&_.is-form-id-5909_.is-search-submit:focus]:!bg-ox-green-200 [&_.is-form-id-5909_.is-search-submit:hover]:!border-ox-green-200 [&_.is-form-id-5909_.is-search-submit:focus]:!border-ox-green-200 [&_.is-form-id-5909_.is-search-icon:hover]:!bg-ox-green-200 [&_.is-form-id-5909_.is-search-icon:focus]:!bg-ox-green-200 [&_.is-form-id-5909_.is-search-icon:hover]:!border-ox-green-200 [&_.is-form-id-5909_.is-search-icon:focus]:!border-ox-green-200 [&_.is-search-icon_svg]:!scale-125">
                    <?php echo do_shortcode('[ivory-search id="5909" title="Header search"]'); ?>

                </div>
            </div>

            <div class="flex self-start xl:self-center pt-1 sm:pt-4 md:pt-4 lg:pt-4 xl:pt-0">
                <div class="flex items-start xl:items-center self-start xl:self-center gap-1 sm:gap-2 xl:gap-4">
                    <div class="hidden sm:flex items-start xl:items-center self-start xl:self-center gap-2">
                        <?php if (isset($component)) { $__componentOriginal9779db95d94b7c164c70b5fd925e1003 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9779db95d94b7c164c70b5fd925e1003 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.currency-picker','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('currency-picker'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9779db95d94b7c164c70b5fd925e1003)): ?>
<?php $attributes = $__attributesOriginal9779db95d94b7c164c70b5fd925e1003; ?>
<?php unset($__attributesOriginal9779db95d94b7c164c70b5fd925e1003); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9779db95d94b7c164c70b5fd925e1003)): ?>
<?php $component = $__componentOriginal9779db95d94b7c164c70b5fd925e1003; ?>
<?php unset($__componentOriginal9779db95d94b7c164c70b5fd925e1003); ?>
<?php endif; ?>
                        <?php if (isset($component)) { $__componentOriginal8d3bff7d7383a45350f7495fc470d934 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal8d3bff7d7383a45350f7495fc470d934 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.language-switcher','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('language-switcher'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal8d3bff7d7383a45350f7495fc470d934)): ?>
<?php $attributes = $__attributesOriginal8d3bff7d7383a45350f7495fc470d934; ?>
<?php unset($__attributesOriginal8d3bff7d7383a45350f7495fc470d934); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8d3bff7d7383a45350f7495fc470d934)): ?>
<?php $component = $__componentOriginal8d3bff7d7383a45350f7495fc470d934; ?>
<?php unset($__componentOriginal8d3bff7d7383a45350f7495fc470d934); ?>
<?php endif; ?>
                    </div>
                    <?php if(is_user_logged_in()): ?>
                        <a href="<?php echo e(get_permalink(5964)); ?>" class="block">
                            <?php if (isset($component)) { $__componentOriginald0f1fd2689e4bb7060122a5b91fe8561 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.button','data' => ['variant' => 'green','style' => 'secondary','rounded' => true,'class' => '!rounded-full h-10 flex items-center justify-center whitespace-nowrap !bg-white !text-ox-green-600 hover:!bg-ox-green-200 !border-2 !border-ox-green-400']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['variant' => 'green','style' => 'secondary','rounded' => true,'class' => '!rounded-full h-10 flex items-center justify-center whitespace-nowrap !bg-white !text-ox-green-600 hover:!bg-ox-green-200 !border-2 !border-ox-green-400']); ?>
                                <span class="hidden sm:inline">Get Started</span>
                                <span class="sm:hidden">Get Started</span>
                             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561)): ?>
<?php $attributes = $__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561; ?>
<?php unset($__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald0f1fd2689e4bb7060122a5b91fe8561)): ?>
<?php $component = $__componentOriginald0f1fd2689e4bb7060122a5b91fe8561; ?>
<?php unset($__componentOriginald0f1fd2689e4bb7060122a5b91fe8561); ?>
<?php endif; ?>
                        </a>
                    <?php else: ?>
                        <a href="<?php echo e(get_permalink(658)); ?>" class="block">
                            <?php if (isset($component)) { $__componentOriginald0f1fd2689e4bb7060122a5b91fe8561 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.button','data' => ['variant' => 'green','style' => 'secondary','rounded' => true,'class' => '!rounded-full h-10 flex items-center justify-center whitespace-nowrap !bg-white !text-ox-green-600 hover:!bg-ox-green-200 !border-2 !border-ox-green-400']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['variant' => 'green','style' => 'secondary','rounded' => true,'class' => '!rounded-full h-10 flex items-center justify-center whitespace-nowrap !bg-white !text-ox-green-600 hover:!bg-ox-green-200 !border-2 !border-ox-green-400']); ?>
                                <span class="hidden sm:inline">Register</span>
                                <span class="sm:hidden">Register</span>
                             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561)): ?>
<?php $attributes = $__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561; ?>
<?php unset($__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald0f1fd2689e4bb7060122a5b91fe8561)): ?>
<?php $component = $__componentOriginald0f1fd2689e4bb7060122a5b91fe8561; ?>
<?php unset($__componentOriginald0f1fd2689e4bb7060122a5b91fe8561); ?>
<?php endif; ?>
                        </a>
                    <?php endif; ?>
                    <?php if(is_user_logged_in()): ?>
                        <?php if (isset($component)) { $__componentOriginal42edc48abdcb6c65aa0760095ea712dd = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal42edc48abdcb6c65aa0760095ea712dd = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.user-menu','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('user-menu'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal42edc48abdcb6c65aa0760095ea712dd)): ?>
<?php $attributes = $__attributesOriginal42edc48abdcb6c65aa0760095ea712dd; ?>
<?php unset($__attributesOriginal42edc48abdcb6c65aa0760095ea712dd); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal42edc48abdcb6c65aa0760095ea712dd)): ?>
<?php $component = $__componentOriginal42edc48abdcb6c65aa0760095ea712dd; ?>
<?php unset($__componentOriginal42edc48abdcb6c65aa0760095ea712dd); ?>
<?php endif; ?>
                    <?php else: ?>
                        <a href="<?php echo e(get_login_url()); ?>">
                            <?php if (isset($component)) { $__componentOriginald0f1fd2689e4bb7060122a5b91fe8561 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.button','data' => ['variant' => 'green','style' => 'filled','class' => '!rounded-full h-10 flex items-center justify-center whitespace-nowrap']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['variant' => 'green','style' => 'filled','class' => '!rounded-full h-10 flex items-center justify-center whitespace-nowrap']); ?>
                                <div class="flex items-center">
                                    <?php echo e(svg('general_contact', ["class" => "w-[1.875rem] h-[1.875rem] fill-ox-green-600"])); ?>
                                    <span class="hidden sm:inline ml-1">Log In</span>
                                </div>
                             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561)): ?>
<?php $attributes = $__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561; ?>
<?php unset($__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald0f1fd2689e4bb7060122a5b91fe8561)): ?>
<?php $component = $__componentOriginald0f1fd2689e4bb7060122a5b91fe8561; ?>
<?php unset($__componentOriginald0f1fd2689e4bb7060122a5b91fe8561); ?>
<?php endif; ?>
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <div class="flex flex-col xl:flex-row justify-between bg-ox-green-100 mx-6 py-4 px-5">
            <div class="flex justify-between xl:justify-start w-full xl:w-auto mb-4 xl:mb-0">
                <?php if (isset($component)) { $__componentOriginalf0b4c7727e373dbba72c6d4cf3b9ded8 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf0b4c7727e373dbba72c6d4cf3b9ded8 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.process-progress','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('process-progress'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf0b4c7727e373dbba72c6d4cf3b9ded8)): ?>
<?php $attributes = $__attributesOriginalf0b4c7727e373dbba72c6d4cf3b9ded8; ?>
<?php unset($__attributesOriginalf0b4c7727e373dbba72c6d4cf3b9ded8); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf0b4c7727e373dbba72c6d4cf3b9ded8)): ?>
<?php $component = $__componentOriginalf0b4c7727e373dbba72c6d4cf3b9ded8; ?>
<?php unset($__componentOriginalf0b4c7727e373dbba72c6d4cf3b9ded8); ?>
<?php endif; ?>
            </div>
            <div class="flex flex-col sm:flex-row items-start sm:items-center gap-4 sm:gap-8 xl:gap-16 w-full xl:w-auto">
                <div class="w-full sm:w-auto sm:flex-1 xl:flex-auto">
                    <?php if (isset($component)) { $__componentOriginal8f4cfc381241bf91a574318d52a98b49 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal8f4cfc381241bf91a574318d52a98b49 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.process-selector','data' => ['addNewLink' => '/calculate']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('process-selector'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['add-new-link' => '/calculate']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal8f4cfc381241bf91a574318d52a98b49)): ?>
<?php $attributes = $__attributesOriginal8f4cfc381241bf91a574318d52a98b49; ?>
<?php unset($__attributesOriginal8f4cfc381241bf91a574318d52a98b49); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8f4cfc381241bf91a574318d52a98b49)): ?>
<?php $component = $__componentOriginal8f4cfc381241bf91a574318d52a98b49; ?>
<?php unset($__componentOriginal8f4cfc381241bf91a574318d52a98b49); ?>
<?php endif; ?>
                </div>
                <div class="w-full sm:flex-1 xl:flex-auto flex justify-start sm:justify-end">
                    <?php if (isset($component)) { $__componentOriginal7cb8ce508f0c0323a1ef462cf7f26cbc = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal7cb8ce508f0c0323a1ef462cf7f26cbc = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.cart-button','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('cart-button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal7cb8ce508f0c0323a1ef462cf7f26cbc)): ?>
<?php $attributes = $__attributesOriginal7cb8ce508f0c0323a1ef462cf7f26cbc; ?>
<?php unset($__attributesOriginal7cb8ce508f0c0323a1ef462cf7f26cbc); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7cb8ce508f0c0323a1ef462cf7f26cbc)): ?>
<?php $component = $__componentOriginal7cb8ce508f0c0323a1ef462cf7f26cbc; ?>
<?php unset($__componentOriginal7cb8ce508f0c0323a1ef462cf7f26cbc); ?>
<?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</header>
<?php /**PATH /Users/<USER>/Herd/co2market/wp-content/themes/co2market/resources/views/sections/header.blade.php ENDPATH**/ ?>