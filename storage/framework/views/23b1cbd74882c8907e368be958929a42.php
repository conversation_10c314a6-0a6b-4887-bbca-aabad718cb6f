<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'type' => 'button',
    'variant' => 'primary', // primary, green, orange, blue
    'style' => 'filled', // filled, outline
    'disabled' => false,
    'class' => '',
    'rounded' => false,
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'type' => 'button',
    'variant' => 'primary', // primary, green, orange, blue
    'style' => 'filled', // filled, outline
    'disabled' => false,
    'class' => '',
    'rounded' => false,
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<button type="<?php echo e($type); ?>"
    <?php echo e($attributes->merge([
        'class' =>
            'button ' .
            'button-' .
            $variant .
            ' button-' .
            $style .
            ($disabled ? ' button-disabled' : '') .
            ($rounded ? ' rounded-full' : '') .
            ' ' .
            $class,
    ])); ?>

    <?php if($disabled): echo 'disabled'; endif; ?>>
    <?php echo e($slot); ?>

</button>
<?php /**PATH /Users/<USER>/Herd/co2market/wp-content/themes/co2market/resources/views/components/button.blade.php ENDPATH**/ ?>