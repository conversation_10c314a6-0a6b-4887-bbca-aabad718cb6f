<?php
  $menu_location = 'footer-menu';
  $menu_locations = get_nav_menu_locations();
  $menu_id = isset($menu_locations[$menu_location]) ? $menu_locations[$menu_location] : null;
  $menu = wp_get_nav_menu_object($menu_id);
  $footer_fields = get_field('navigation_sections', $menu);
  $information_section = get_field('information_section', $menu);
  $social_section = get_field('social_section', $menu);
?>

<footer class="bg-ox-green-100">
  <div class="max-w-screen-lg mx-auto px-4 py-20">
    
    <div class="flex flex-col md:flex-row gap-24 mb-12">
      
      <div class="flex flex-col mb-0 md:mb-0">
        <?php if($social_section['main_image']): ?>
          <img
            src="<?php echo e($social_section['main_image']['url']); ?>"
            alt="<?php echo e($social_section['main_image']['alt']); ?>"
            class="h-12 mb-3"
          >
        <?php endif; ?>
        <div class="flex space-x-4 justify-end">
          <?php if($social_section['socials']): ?>
            <?php $__currentLoopData = $social_section['socials']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $social): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
              <a class="bg-ox-green-400 rounded-md p-0.5 size-8" href="<?php echo e($social['url']); ?>" target="_blank" rel="noopener noreferrer">
                <img
                  src="<?php echo e($social['icon']['url']); ?>"
                  alt="<?php echo e($social['name']); ?>"
                >
              </a>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
          <?php endif; ?>
        </div>
      </div>

      
      <div class="flex flex-col flex-1 w-full ">
        <h3 class="text-xl font-bold text-ox-black mb-4">NEWSLETTER</h3>
        <form class="flex w-full gap-2">
          <input
            type="email"
            placeholder="Enter email address"
            class="px-4 w-full py-2 border border-ox-green-200 text-ox-green-300 rounded-l focus:outline-none focus:border-ox-green-500 focus:text-ox-green-600"
          >
          <?php if (isset($component)) { $__componentOriginald0f1fd2689e4bb7060122a5b91fe8561 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.button','data' => ['type' => 'submit','variant' => 'green','style' => 'filled']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['type' => 'submit','variant' => 'green','style' => 'filled']); ?>
            Subscribe
           <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561)): ?>
<?php $attributes = $__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561; ?>
<?php unset($__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald0f1fd2689e4bb7060122a5b91fe8561)): ?>
<?php $component = $__componentOriginald0f1fd2689e4bb7060122a5b91fe8561; ?>
<?php unset($__componentOriginald0f1fd2689e4bb7060122a5b91fe8561); ?>
<?php endif; ?>
        </form>
      </div>
    </div>

    
    <div class="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
      <?php if($footer_fields): ?>
        <?php $__currentLoopData = $footer_fields; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $section): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
          <div class="flex flex-col">
            <h3 class="text-lg font-bold text-ox-black mb-4"><?php echo e($section['title']); ?></h3>
            <?php if($section['links']): ?>
              <ul class="space-y-2">
                <?php $__currentLoopData = $section['links']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $link): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                  <li>
                    <a
                      href="<?php echo e($link['link']['url']); ?>"
                      class="text-ox-black hover:text-ox-green-500 transition"
                      <?php if($link['link']['target']): ?> target="<?php echo e($link['link']['target']); ?>" <?php endif; ?>
                    >
                      <?php echo e($link['link']['title']); ?>

                    </a>
                  </li>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
              </ul>
            <?php endif; ?>
          </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
      <?php endif; ?>
    </div>

    
    <hr class="border-ox-green-300 my-8">

    
    <div class="flex flex-col md:flex-row justify-between">
      
      <div class="flex flex-col w-full md:w-1/2 mb-8 md:mb-0 md:pr-8">
        <h3 class="text-lg font-bold text-ox-black mb-4">Contact Us</h3>
        <form class="space-y-4">
          <div class="flex space-x-4">
            <input
              type="text"
              placeholder="Name"
              class="w-1/2 px-4 py-2 border border-ox-green-200 text-ox-green-300 rounded focus:outline-none focus:border-ox-green-500 focus:text-ox-green-600"
            >
            <input
              type="email"
              placeholder="Email"
              class="w-1/2 px-4 py-2 border border-ox-green-200 text-ox-green-300 rounded focus:outline-none focus:border-ox-green-500 focus:text-ox-green-600"
            >
          </div>
          <textarea
            placeholder="Message"
            rows="4"
            class="w-full px-4 py-2 border border-ox-green-200 text-ox-green-300 rounded focus:outline-none focus:border-ox-green-500 focus:text-ox-green-600"
          ></textarea>
          <?php if (isset($component)) { $__componentOriginald0f1fd2689e4bb7060122a5b91fe8561 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.button','data' => ['type' => 'submit','variant' => 'green','style' => 'filled']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['type' => 'submit','variant' => 'green','style' => 'filled']); ?>
            Send message
           <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561)): ?>
<?php $attributes = $__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561; ?>
<?php unset($__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald0f1fd2689e4bb7060122a5b91fe8561)): ?>
<?php $component = $__componentOriginald0f1fd2689e4bb7060122a5b91fe8561; ?>
<?php unset($__componentOriginald0f1fd2689e4bb7060122a5b91fe8561); ?>
<?php endif; ?>
        </form>
      </div>

      
      <div class="flex flex-col w-full md:w-1/3">
        <?php if($information_section): ?>
          <h3 class="text-lg font-bold text-ox-black mb-4"><?php echo e($information_section['title']); ?></h3>
          <?php if($information_section['links']): ?>
            <ul class="space-y-2">
              <?php $__currentLoopData = $information_section['links']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $link): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <li>
                  <a
                    href="<?php echo e($link['link']['url']); ?>"
                    class="text-ox-black hover:text-ox-green-500 transition"
                    <?php if($link['link']['target']): ?> target="<?php echo e($link['link']['target']); ?>" <?php endif; ?>
                  >
                    <?php echo e($link['link']['title']); ?>

                  </a>
                </li>
              <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </ul>
          <?php endif; ?>
        <?php endif; ?>
      </div>
    </div>
  </div>
</footer>
<?php /**PATH /Users/<USER>/Herd/co2market/wp-content/themes/co2market/resources/views/sections/footer.blade.php ENDPATH**/ ?>