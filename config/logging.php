<?php

use Monolog\Handler\StreamHandler;
use Logtail\Monolog\LogtailHandler;

return [

  /*
    |--------------------------------------------------------------------------
    | Default Log Channel
    |--------------------------------------------------------------------------
    |
    | This option defines the default log channel that gets used when writing
    | messages to the logs. The name specified in this option should match
    | one of the channels defined in the "channels" configuration array.
    |
    */

  'default' => env('LOG_CHANNEL', 'stack'),

  /*
    |--------------------------------------------------------------------------
    | Log Channels
    |--------------------------------------------------------------------------
    |
    | Here you may configure the log channels for your application. Out of
    | the box, Laravel uses the Monolog PHP logging library. This gives
    | you a variety of powerful log handlers / formatters to utilize.
    |
    | Available Drivers: "single", "daily", "slack", "syslog",
    |                    "errorlog", "monolog",
    |                    "custom", "stack"
    |
    */

  'channels' => [
    'logtail'    => [
      'driver'  => 'monolog',
      'level'   => env('LOG_LEVEL', 'debug'),
      'handler' => LogtailHandler::class,
      'with'    => [
        'sourceToken' => env('LOGTAIL_SOURCE_TOKEN'),
        "endpoint" => env("BETTER_STACK_SOURCE_ENDPOINT")
      ],
    ],

    'stack' => [
      'driver' => 'stack',
      'channels' => ['single', "logtail"],
    ],

    'error_stack' => [
      'driver' => 'stack',
      'channels' => ["slack", "logtail"],
    ],

    'single' => [
      'driver' => 'single',
      'path' => storage_path('logs/laravel.log'),
      'level' => 'debug',
    ],

    'daily' => [
      'driver' => 'daily',
      'path' => storage_path('logs/laravel.log'),
      'level' => 'debug',
      'days' => 7,
    ],

    'slack' => [
      'driver' => 'slack',
      'url' => env('SLACK_ERROR', "*********************************************************************************"),
      'username' => 'COMARKET',
      'emoji' => ':robot_face:',
      'level' => 'debug',
    ],

    'stderr' => [
      'driver' => 'monolog',
      'handler' => StreamHandler::class,
      'with' => [
        'stream' => 'php://stderr',
      ],
    ],

    'syslog' => [
      'driver' => 'syslog',
      'level' => 'debug',
    ],

    'errorlog' => [
      'driver' => 'errorlog',
      'level' => 'debug',
    ],
  ],

];
