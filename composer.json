{"name": "roots/sage", "type": "wordpress-theme", "license": "MIT", "description": "WordPress starter theme with a modern development workflow", "homepage": "https://roots.io/sage/", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/retlehs"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/swalkinshaw"}, {"name": "QWp6t", "email": "<EMAIL>", "homepage": "https://github.com/qwp6t"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/log1x"}], "keywords": ["wordpress"], "support": {"issues": "https://github.com/roots/sage/issues", "forum": "https://discourse.roots.io/"}, "autoload": {"psr-4": {"App\\": "app/"}, "exclude-from-classmap": ["vendor/laravel/framework/src/Illuminate/Foundation"]}, "require": {"php": ">=8.2", "blade-ui-kit/blade-icons": "^1.7", "dedoc/scramble": "^0.12.16", "generoi/sage-woocommerce": "^1.1", "google/cloud-translate": "^2.0", "inertiajs/inertia-laravel": "^2.0", "laravel/pail": "^1.2", "livewire/livewire": "^3.5", "log1x/acf-composer": "^3.3", "logtail/monolog-logtail": "^3.3", "roots/acorn": "^5.0.2", "roots/acorn-mail": "^1.0", "spatie/laravel-tail": "^4.5"}, "require-dev": {"barryvdh/laravel-ide-helper": "^3.5", "humanmade/psalm-plugin-wordpress": "^3.1", "kimhf/advanced-custom-fields-stubs": "^5.9", "laravel/pint": "^1.13", "php-stubs/acf-pro-stubs": "^6.3", "php-stubs/woocommerce-stubs": "^9.5", "vimeo/psalm": "^6.1"}, "suggest": {"log1x/sage-directives": "A collection of useful Blade directives for WordPress and Sage (^1.0)."}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "scripts": {"post-autoload-dump": ["Roots\\Acorn\\ComposerScripts::postAutoloadDump"], "cache": ["wp acorn optimize", "wp acorn acf:cache"], "cache:clear": ["wp acorn optimize:clear", "wp acorn acf:clear"]}, "minimum-stability": "dev", "prefer-stable": true}